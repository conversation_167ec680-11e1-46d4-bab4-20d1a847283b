server:
  port: 5007

quartz:
  enabled: true

spring:
  profiles:
    active: @profiles.active@
  application:
    name: @artifactId@
  cloud:
    nacos:
      username: @nacos.username@
      password: @nacos.password@
      discovery:
        server-addr: ${NACOS_HOST:ydsf-register}:${NACOS_PORT:8848}
        namespace: ${NACOS_NAMESPACE:public}
      config:
        server-addr: ${spring.cloud.nacos.discovery.server-addr}
        namespace: ${spring.cloud.nacos.discovery.namespace}
  config:
    import:
      - optional:nacos:<EMAIL>@.yml
      - optional:nacos:${spring.application.name}-@profiles.active@.yml
