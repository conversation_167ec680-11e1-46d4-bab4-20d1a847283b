package com.yuedu.ydsf.daemon.quartz;

import com.yuedu.ydsf.common.feign.annotation.EnableYdsfFeignClients;
import com.yuedu.ydsf.common.security.annotation.EnableYdsfResourceServer;
import com.yuedu.ydsf.common.swagger.annotation.EnableOpenApi;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;

/**
 * <AUTHOR>
 * @date 2019/01/23 定时任务模块
 */
@EnableOpenApi("job")
@EnableYdsfFeignClients
@EnableYdsfResourceServer
@EnableDiscoveryClient
@SpringBootApplication
public class YdsfDaemonQuartzApplication {

	public static void main(String[] args) {
		SpringApplication.run(YdsfDaemonQuartzApplication.class, args);
	}

}
