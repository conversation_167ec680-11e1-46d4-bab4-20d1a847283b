<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
	<parent>
		<groupId>com.yuedu</groupId>
		<artifactId>ydsf-visual</artifactId>
		<version>5.6.17-SNAPSHOT</version>
	</parent>
	<modelVersion>4.0.0</modelVersion>

	<artifactId>ydsf-daemon-quartz</artifactId>
	<packaging>jar</packaging>

	<description>基于quartz后台跑批定时任务模块</description>

	<dependencies>
		<!--注册中心客户端-->
		<dependency>
			<groupId>com.alibaba.cloud</groupId>
			<artifactId>spring-cloud-starter-alibaba-nacos-discovery</artifactId>
		</dependency>
		<!--配置中心客户端-->
		<dependency>
			<groupId>com.alibaba.cloud</groupId>
			<artifactId>spring-cloud-starter-alibaba-nacos-config</artifactId>
		</dependency>
		<!--日志处理-->
		<dependency>
			<groupId>com.yuedu</groupId>
			<artifactId>ydsf-common-log</artifactId>
		</dependency>
		<dependency>
			<groupId>com.yuedu</groupId>
			<artifactId>ydsf-common-data</artifactId>
		</dependency>
		<!--mybatis-->
		<dependency>
			<groupId>com.baomidou</groupId>
			<artifactId>mybatis-plus-spring-boot3-starter</artifactId>
		</dependency>
		<!--数据库-->
		<dependency>
			<groupId>com.mysql</groupId>
			<artifactId>mysql-connector-j</artifactId>
		</dependency>
		<!--mssql-->
		<dependency>
			<groupId>com.microsoft.sqlserver</groupId>
			<artifactId>mssql-jdbc</artifactId>
		</dependency>
		<!-- ojdbc8 -->
		<dependency>
			<groupId>com.oracle.database.jdbc</groupId>
			<artifactId>ojdbc8</artifactId>
		</dependency>
		<!--PG-->
		<dependency>
			<groupId>org.postgresql</groupId>
			<artifactId>postgresql</artifactId>
		</dependency>
		<!-- druid 连接池 -->
		<dependency>
			<groupId>com.alibaba</groupId>
			<artifactId>druid-spring-boot-3-starter</artifactId>
		</dependency>
		<!--swagger-->
		<dependency>
			<groupId>com.yuedu</groupId>
			<artifactId>ydsf-common-swagger</artifactId>
		</dependency>
		<!--spring security 、oauth、jwt依赖-->
		<dependency>
			<groupId>com.yuedu</groupId>
			<artifactId>ydsf-common-security</artifactId>
		</dependency>
		<!-- sentinel-->
		<dependency>
			<groupId>com.yuedu</groupId>
			<artifactId>ydsf-common-sentinel</artifactId>
		</dependency>
		<!--灰度支持-->
		<dependency>
			<groupId>com.yuedu</groupId>
			<artifactId>ydsf-common-gray</artifactId>
		</dependency>
		<!--web 模块-->
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-web</artifactId>
		</dependency>
		<!--undertow容器-->
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-undertow</artifactId>
		</dependency>
		<!-- quartz 模块 -->
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-quartz</artifactId>
		</dependency>

	</dependencies>

	<profiles>
		<profile>
			<id>cloud</id>
			<activation>
				<activeByDefault>true</activeByDefault>
			</activation>
			<build>
				<plugins>
					<plugin>
						<groupId>org.springframework.boot</groupId>
						<artifactId>spring-boot-maven-plugin</artifactId>
						<executions>
							<execution>
								<goals>
									<goal>repackage</goal>
								</goals>
								<configuration>
									<loaderImplementation>CLASSIC</loaderImplementation>
								</configuration>
							</execution>
						</executions>
					</plugin>
					<plugin>
						<groupId>io.fabric8</groupId>
						<artifactId>docker-maven-plugin</artifactId>
						<configuration>
							<skip>false</skip>
						</configuration>
					</plugin>
					<plugin>
						<groupId>org.apache.maven.plugins</groupId>
						<artifactId>maven-deploy-plugin</artifactId>
						<version>2.7</version>
						<configuration>
							<skip>true</skip>
						</configuration>
					</plugin>
				</plugins>
			</build>
		</profile>
		<profile>
			<id>boot</id>
		</profile>
	</profiles>
</project>
