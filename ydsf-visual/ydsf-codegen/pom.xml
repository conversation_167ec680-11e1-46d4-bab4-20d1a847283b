<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>com.yuedu</groupId>
        <artifactId>ydsf-visual</artifactId>
        <version>5.6.17-SNAPSHOT</version>
    </parent>

    <artifactId>ydsf-codegen</artifactId>
    <packaging>jar</packaging>

    <description>代码生成模块</description>

    <properties>
        <magic.version>2.0.2</magic.version>
        <screw.version>0.0.6</screw.version>
        <db2.version>********</db2.version>
        <configuration.version>1.10</configuration.version>
    </properties>

    <dependencies>
        <!--注册中心客户端-->
        <dependency>
            <groupId>com.alibaba.cloud</groupId>
            <artifactId>spring-cloud-starter-alibaba-nacos-discovery</artifactId>
        </dependency>
        <!--配置中心客户端-->
        <dependency>
            <groupId>com.alibaba.cloud</groupId>
            <artifactId>spring-cloud-starter-alibaba-nacos-config</artifactId>
        </dependency>
        <!--数据操作-->
        <dependency>
            <groupId>com.yuedu</groupId>
            <artifactId>ydsf-common-data</artifactId>
        </dependency>
        <!--动态数据源 数据操作-->
        <dependency>
            <groupId>com.yuedu</groupId>
            <artifactId>ydsf-common-datasource</artifactId>
        </dependency>
        <!--mybatis-->
        <dependency>
            <groupId>com.baomidou</groupId>
            <artifactId>mybatis-plus-spring-boot3-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>com.mysql</groupId>
            <artifactId>mysql-connector-j</artifactId>
        </dependency>
        <!-- postgresql 驱动 -->
        <dependency>
            <groupId>org.postgresql</groupId>
            <artifactId>postgresql</artifactId>
        </dependency>
        <!-- oracle 驱动 -->
        <dependency>
            <groupId>com.oracle.database.jdbc</groupId>
            <artifactId>ojdbc8</artifactId>
        </dependency>
        <!-- sqlserver 驱动 -->
        <dependency>
            <groupId>com.microsoft.sqlserver</groupId>
            <artifactId>mssql-jdbc</artifactId>
        </dependency>
        <!-- db2 驱动-->
        <dependency>
            <groupId>com.ibm.db2</groupId>
            <artifactId>jcc</artifactId>
            <version>${db2.version}</version>
        </dependency>
        <!-- 瀚高 -->
        <dependency>
            <groupId>com.highgo</groupId>
            <artifactId>HgdbJdbc</artifactId>
        </dependency>
        <!--DM-->
        <dependency>
            <groupId>com.dameng</groupId>
            <artifactId>DmJdbcDriver18</artifactId>
        </dependency>
        <!-- druid 连接池 -->
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>druid-spring-boot-3-starter</artifactId>
        </dependency>
        <!--anyline-->
        <dependency>
            <groupId>org.anyline</groupId>
            <artifactId>anyline-environment-spring-data-jdbc</artifactId>
        </dependency>
        <dependency>
            <groupId>org.anyline</groupId>
            <artifactId>anyline-data-jdbc-mysql</artifactId>
        </dependency>
        <dependency>
            <groupId>org.anyline</groupId>
            <artifactId>anyline-data-jdbc-postgresql</artifactId>
        </dependency>
        <dependency>
            <groupId>org.anyline</groupId>
            <artifactId>anyline-data-jdbc-oracle</artifactId>
        </dependency>
        <dependency>
            <groupId>org.anyline</groupId>
            <artifactId>anyline-data-jdbc-dm</artifactId>
        </dependency>
        <!--common-->
        <dependency>
            <groupId>com.yuedu</groupId>
            <artifactId>ydsf-common-core</artifactId>
        </dependency>
        <!-- 幂等插件使用 -->
        <dependency>
            <groupId>com.yuedu</groupId>
            <artifactId>ydsf-common-idempotent</artifactId>
        </dependency>
        <dependency>
            <groupId>commons-configuration</groupId>
            <artifactId>commons-configuration</artifactId>
            <version>${configuration.version}</version>
        </dependency>
        <!--swagger-->
        <dependency>
            <groupId>com.yuedu</groupId>
            <artifactId>ydsf-common-swagger</artifactId>
        </dependency>
        <!--安全模块-->
        <dependency>
            <groupId>com.yuedu</groupId>
            <artifactId>ydsf-common-xss</artifactId>
        </dependency>
        <dependency>
            <groupId>com.yuedu</groupId>
            <artifactId>ydsf-common-security</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.security.oauth</groupId>
            <artifactId>spring-security-oauth2</artifactId>
        </dependency>
        <!--sentinel-->
        <dependency>
            <groupId>com.yuedu</groupId>
            <artifactId>ydsf-common-sentinel</artifactId>
        </dependency>
        <dependency>
            <groupId>com.yuedu</groupId>
            <artifactId>ydsf-common-log</artifactId>
        </dependency>
        <!--灰度支持-->
        <dependency>
            <groupId>com.yuedu</groupId>
            <artifactId>ydsf-common-gray</artifactId>
        </dependency>
        <!--代码生成模板引擎-->
        <dependency>
            <groupId>org.apache.velocity</groupId>
            <artifactId>velocity-engine-core</artifactId>
        </dependency>
        <dependency>
            <groupId>org.apache.velocity.tools</groupId>
            <artifactId>velocity-tools-generic</artifactId>
        </dependency>
        <!--生成文档-->
        <dependency>
            <groupId>group.springframework.plugin</groupId>
            <artifactId>screw-spring-boot-starter</artifactId>
            <version>${screw.version}</version>
        </dependency>
        <!--web 模块-->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
        </dependency>
        <!--undertow容器-->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-undertow</artifactId>
        </dependency>
    </dependencies>

    <profiles>
        <profile>
            <id>cloud</id>
            <activation>
                <activeByDefault>true</activeByDefault>
            </activation>
            <build>
                <plugins>
                    <plugin>
                        <groupId>org.springframework.boot</groupId>
                        <artifactId>spring-boot-maven-plugin</artifactId>
                        <executions>
                            <execution>
                                <goals>
                                    <goal>repackage</goal>
                                </goals>
                                <configuration>
                                    <loaderImplementation>CLASSIC</loaderImplementation>
                                </configuration>
                            </execution>
                        </executions>
                    </plugin>
                    <plugin>
                        <groupId>io.fabric8</groupId>
                        <artifactId>docker-maven-plugin</artifactId>
                        <configuration>
                            <skip>false</skip>
                        </configuration>
                    </plugin>
                    <plugin>
                        <groupId>org.apache.maven.plugins</groupId>
                        <artifactId>maven-deploy-plugin</artifactId>
                        <version>2.7</version>
                        <configuration>
                            <skip>true</skip>
                        </configuration>
                    </plugin>
                </plugins>
            </build>
        </profile>
        <profile>
            <id>boot</id>
        </profile>
    </profiles>
</project>
