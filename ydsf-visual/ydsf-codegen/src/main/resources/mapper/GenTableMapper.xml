<?xml version="1.0" encoding="UTF-8"?>

<!--
  ~
  ~      Copyright (c) 2018-2025, ydsf All rights reserved.
  ~
  ~  Redistribution and use in source and binary forms, with or without
  ~  modification, are permitted provided that the following conditions are met:
  ~
  ~ Redistributions of source code must retain the above copyright notice,
  ~  this list of conditions and the following disclaimer.
  ~  Redistributions in binary form must reproduce the above copyright
  ~  notice, this list of conditions and the following disclaimer in the
  ~  documentation and/or other materials provided with the distribution.
  ~  Neither the name of the pig4cloud.com developer nor the names of its
  ~  contributors may be used to endorse or promote products derived from
  ~  this software without specific prior written permission.
  ~  Author: ydsf
  ~
  -->

<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.yuedu.ydsf.codegen.mapper.GenTableMapper">

  <resultMap id="tableMap" type="com.yuedu.ydsf.codegen.entity.GenTable">
        <id property="id" column="id"/>
        <result property="tableName" column="table_name"/>
        <result property="className" column="class_name"/>
        <result property="tableComment" column="table_comment"/>
        <result property="author" column="author"/>
        <result property="email" column="email"/>
        <result property="packageName" column="package_name"/>
        <result property="version" column="version"/>
        <result property="generatorType" column="generator_type"/>
        <result property="backendPath" column="backend_path"/>
        <result property="frontendPath" column="frontend_path"/>
        <result property="moduleName" column="module_name"/>
        <result property="functionName" column="function_name"/>
        <result property="formLayout" column="form_layout"/>
        <result property="datasourceId" column="datasource_id"/>
        <result property="baseclassId" column="baseclass_id"/>
        <result property="createTime" column="create_time"/>
  </resultMap>
</mapper>
