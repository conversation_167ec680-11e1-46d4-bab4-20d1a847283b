package com.yuedu.permission.controller;

import cn.hutool.json.JSONUtil;
import com.yuedu.permission.api.dto.SysUserDTO;
import com.yuedu.permission.api.vo.SysUserVO;
import com.yuedu.permission.service.LeadTeacherService;
import jakarta.annotation.Resource;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.runner.RunWith;
import org.mockito.Mockito;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;
import org.springframework.test.web.servlet.result.MockMvcResultMatchers;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;

import java.util.ArrayList;
import java.util.List;

import static org.springframework.test.web.servlet.result.MockMvcResultHandlers.print;


/**
 * <AUTHOR>
 * @date 2024/10/16
 **/
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
@AutoConfigureMockMvc
@RunWith(SpringRunner.class)
class LeadTeacherControllerTest {
    @Resource
    private MockMvc mockMvc;

    @Resource
    @MockBean
    private LeadTeacherService leadTeacherService;


    @BeforeEach
    public void setUp() {
        this.mockMvc = MockMvcBuilders.standaloneSetup(new LeadTeacherController(leadTeacherService)).build();
    }

    @Test
    void testListNikeName() throws Exception {
        List<SysUserVO> sysUserVOList = new ArrayList<>();
        SysUserVO sysUserVO = new SysUserVO();
        sysUserVO.setUserId(1840305293909585922L);
        sysUserVO.setName("于琦");
        sysUserVOList.add(sysUserVO);
        Mockito.when(leadTeacherService.listNikeName()).thenReturn(sysUserVOList);

        mockMvc.perform(MockMvcRequestBuilders.get("/leadTeacher/list"))
                .andExpectAll(
                        MockMvcResultMatchers.status().isOk(),
                        MockMvcResultMatchers.content().json("{\"code\":0,\"msg\":null,\"data\":[{\"userId\":1840305293909585922,\"name\":\"于琦\"}],\"ok\":true}"))
                .andDo(print());
    }

    @Test
    void testGetById() throws Exception {
        SysUserVO sysUserVO = new SysUserVO();
        sysUserVO.setUserId(1840305293909585922L);
        sysUserVO.setName("于琦");
        Mockito.when(leadTeacherService.getById(1840305293909585922L)).thenReturn(sysUserVO);

        mockMvc.perform(MockMvcRequestBuilders.get("/leadTeacher/get/1840305293909585922"))
                .andExpectAll(
                        MockMvcResultMatchers.status().isOk(),
                        MockMvcResultMatchers.content().json("{\"code\":0,\"msg\":null,\"data\":{\"userId\":1840305293909585922,\"name\":\"于琦\"},\"ok\":true}"))
                .andDo(print());
    }

    @Test
    void testListById() throws Exception {
        SysUserDTO sysUserDTO = new SysUserDTO();
        List<Long> userIdList = new ArrayList<>();
        userIdList.add(1840305293909585922L);
        userIdList.add(1840305307050340354L);
        sysUserDTO.setUserIdList(userIdList);
        List<SysUserVO> sysUserVOList = new ArrayList<>();
        SysUserVO sysUserVO = new SysUserVO();
        sysUserVO.setUserId(1840305293909585922L);
        sysUserVO.setName("于琦");
        sysUserVOList.add(sysUserVO);
        SysUserVO sysUserVO2 = new SysUserVO();
        sysUserVO2.setUserId(1840305307050340354L);
        sysUserVO2.setName("冷晓燕");
        sysUserVOList.add(sysUserVO2);
        Mockito.when(leadTeacherService.listById(sysUserDTO)).thenReturn(sysUserVOList);

        mockMvc.perform(MockMvcRequestBuilders.post("/leadTeacher/listUser")
                        .contentType("application/json; charset=UTF-8")
                        .content(JSONUtil.toJsonStr(sysUserDTO)))
                .andExpect(MockMvcResultMatchers.status().isOk())
                .andExpect(MockMvcResultMatchers.content().json("{\"code\":0,\"msg\":null,\"data\":[{\"userId\":1840305293909585922,\"name\":\"于琦\"},{\"userId\":1840305307050340354,\"name\":\"冷晓燕\"}],\"ok\":true}"))
                .andDo(print());

    }
}