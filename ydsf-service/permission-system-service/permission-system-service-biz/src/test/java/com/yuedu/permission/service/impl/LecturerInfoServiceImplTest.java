package com.yuedu.permission.service.impl;

import com.yuedu.permission.api.dto.LecturerInfoDTO;
import com.yuedu.permission.api.entity.LecturerInfo;
import com.yuedu.permission.mapper.LecturerInfoMapper;
import com.yuedu.ydsf.common.core.exception.BizException;
import com.yuedu.ydsf.common.core.util.R;
import com.yuedu.ydsf.eduConnect.api.feign.RemoteTeachingPlanDetailPubService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.Mockito.when;

/**
 * <AUTHOR>
 * @date 2024/12/17 15:31
 */

@ExtendWith(MockitoExtension.class)
class LecturerInfoServiceImplTest {

    @InjectMocks
    private LecturerInfoServiceImpl lecturerInfoService;

    @Mock
    private LecturerInfoMapper lecturerInfoMapper;

    @Mock
    private RemoteTeachingPlanDetailPubService remoteTeachingPlanService;

    @Mock
    private LecturerInfoDTO lecturerInfoDTO;

    @BeforeEach
    public void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    // 更新从带课改为不带课成功更新返回true
    @Test
    void updateLecturer_0To1_SuccessfulUpdate_ReturnsTrue() {
        // Arrange
        when(remoteTeachingPlanService.hasUnfinishedClass(anyLong())).thenReturn(R.ok(Boolean.FALSE));
        when(lecturerInfoDTO.getStatus()).thenReturn(1);

        // Act
        Boolean result = lecturerInfoService.updateLecturer(lecturerInfoDTO);

        // Assert
        assertTrue(result);
    }

    // 更新从不带课改为带课成功更新返回true
    @Test
    void updateLecturer_1To0_SuccessfulUpdate_ReturnsTrue() {
        // Arrange
        when(lecturerInfoDTO.getStatus()).thenReturn(0);

        // Act
        Boolean result = lecturerInfoService.updateLecturer(lecturerInfoDTO);

        // Assert
        assertTrue(result);
    }

    // 更新讲师远程呼叫失败引发业务异常
    @Test
    void updateLecturer_RemoteCallFails_ThrowsBizException() {
        // Arrange
        when(remoteTeachingPlanService.hasUnfinishedClass(anyLong())).thenReturn(R.failed());
        when(lecturerInfoDTO.getStatus()).thenReturn(1);

        // Act & Assert
        assertThrows(BizException.class, () -> lecturerInfoService.updateLecturer(lecturerInfoDTO));
    }

    // 更新讲师有未完成的课程返回false
    @Test
    void updateLecturer_HasUnfinishedClasses_ThrowsBizException() {
        // Arrange
        when(remoteTeachingPlanService.hasUnfinishedClass(anyLong())).thenReturn(R.ok(true));
        when(lecturerInfoDTO.getStatus()).thenReturn(1);

        // Act
        Boolean result = lecturerInfoService.updateLecturer(lecturerInfoDTO);

        // Assert
        assertFalse(result);
    }

    // 更新讲师没有未完成的课程成功更新返回true
    @Test
    void updateLecturer_NoUnfinishedClasses_SuccessfulUpdate_ReturnsTrue() {
        // Arrange
        when(remoteTeachingPlanService.hasUnfinishedClass(anyLong())).thenReturn(R.ok(false));
        when(lecturerInfoMapper.updateById(any(LecturerInfo.class))).thenReturn(1);
        when(lecturerInfoDTO.getStatus()).thenReturn(1);

        // Act
        Boolean result = lecturerInfoService.updateLecturer(lecturerInfoDTO);

        // Assert
        assertTrue(result);
    }

}