package com.yuedu.permission.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yuedu.permission.api.dto.LecturerInfoAddDTO;
import com.yuedu.permission.api.dto.LecturerInfoDTO;
import com.yuedu.permission.api.query.LecturerInfoQuery;
import com.yuedu.permission.api.vo.LecturerInfoVO;
import com.yuedu.permission.service.LecturerInfoService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;
import org.springframework.test.web.servlet.result.MockMvcResultHandlers;
import org.springframework.test.web.servlet.result.MockMvcResultMatchers;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultHandlers.print;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

/**
 * <AUTHOR>
 * @date 2024/11/28
 **/
class LecturerInfoControllerTest {

    private MockMvc mockMvc;

    @Mock
    private LecturerInfoService lecturerInfoService;

    @InjectMocks
    private LecturerInfoController lecturerInfoController;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.initMocks(this);
        mockMvc = MockMvcBuilders.standaloneSetup(lecturerInfoController).build();
    }

    @Test
    void getLecturerInfoPage_ValidPageAndQuery_ReturnsPage() throws Exception {
        Page<LecturerInfoVO> page = new Page<>(1, 10);
        when(lecturerInfoService.pageLecturer(any(Page.class), any(LecturerInfoQuery.class))).thenReturn(page);

        mockMvc.perform(get("/LecturerInfo/page")
                        .param("current", "1")
                        .param("size", "10")
                        .param("name", "test")
                        .contentType(MediaType.APPLICATION_JSON))
                .andDo(print())
                .andExpect(status().isOk());
    }

    @Test
    void getDetails_ValidQuery_ReturnsDetails() throws Exception {
        when(lecturerInfoService.listDetails(any(LecturerInfoQuery.class))).thenReturn(java.util.Collections.emptyList());

        mockMvc.perform(get("/LecturerInfo/details")
                        .param("name", "张三")
                        .contentType(MediaType.APPLICATION_JSON))
                .andDo(print())
                .andExpect(status().isOk());
    }

    @Test
    void getDetailsById_ValidQuery_ReturnsDetailsById() throws Exception {
        LecturerInfoVO lecturerInfoVO = LecturerInfoVO.builder()
                .name("张三")
                .nickname("法外狂徒")
                .userId(1231241243L)
                .xgjLecturerId("123")
                .status(1)
                .updateBy("admin")
                .updateTime(java.time.LocalDateTime.now())
                .build();

        when(lecturerInfoService.getDetailsById(any(LecturerInfoQuery.class))).thenReturn(lecturerInfoVO);

        // Act & Assert
        mockMvc.perform(MockMvcRequestBuilders.get("/LecturerInfo/detailsById")
                        .param("userId", "1231241243")
                        .contentType(MediaType.APPLICATION_JSON))
                .andDo(MockMvcResultHandlers.print())
                .andExpect(MockMvcResultMatchers.status().isOk())
                .andExpect(MockMvcResultMatchers.jsonPath("$.data.name").value("张三"))
                .andExpect(MockMvcResultMatchers.jsonPath("$.data.userId").value(1231241243L));
    }

    @Test
    void save_ValidLecturerInfoAddDTO_ReturnsSuccess() throws Exception {
        when(lecturerInfoService.saveLecturer(any(LecturerInfoAddDTO.class))).thenReturn(true);

        mockMvc.perform(post("/LecturerInfo/add")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content("""
                                {
                                    "name": "张三",
                                    "userId": 1234141241
                                }
                                """))
                .andDo(print())
                .andExpect(status().isOk());
    }

    @Test
    void updateById_ValidLecturerInfoDTO_ReturnsSuccess() throws Exception {
        when(lecturerInfoService.updateLecturer(any(LecturerInfoDTO.class))).thenReturn(true);

        mockMvc.perform(MockMvcRequestBuilders.put("/LecturerInfo/edit")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content("""
                                {
                                    "userId": 12345345,
                                    "status": 1
                                }
                                """))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("code").value(0));
    }
}