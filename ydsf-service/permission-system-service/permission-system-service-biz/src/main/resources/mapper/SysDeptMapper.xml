<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.yuedu.permission.mapper.SysDeptMapper">

    <resultMap id="sysDeptMap" type="com.yuedu.permission.api.entity.SysDept">
        <id property="deptId" column="dept_id"/>
        <result property="name" column="name"/>
        <result property="sortOrder" column="sort_order"/>
        <result property="createBy" column="create_by"/>
        <result property="updateBy" column="update_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
        <result property="parentId" column="parent_id"/>
        <result property="delFlag" column="del_flag"/>
        <result property="tenantId" column="tenant_id"/>
    </resultMap>

    <select id="selectList" resultType="com.yuedu.permission.api.entity.SysDept">
        select *
        from sys_dept
    </select>

    <select id="selectOneByDeptId" resultType="com.yuedu.permission.api.entity.SysDept">
        select *
        from sys_dept
        order by dept_id
        limit 1
    </select>

    <update id="updateDept">
        update sys_dept
        <set>
            <if test="name != null">name = #{name},</if>
            <if test="sortOrder != null">sort_order = #{sortOrder},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="parentId != null">parent_id = #{parentId},</if>
            <if test="tenantId != null">tenant_id = #{tenantId},</if>
            <if test="delFlag != null">del_flag = #{delFlag}</if>
        </set>
        where dept_id = #{deptId}
    </update>
</mapper>