package com.yuedu.permission.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.yuedu.permission.api.dto.LecturerInfoAddDTO;
import com.yuedu.permission.api.dto.LecturerInfoDTO;
import com.yuedu.permission.api.entity.LecturerInfo;
import com.yuedu.permission.api.query.LecturerInfoQuery;
import com.yuedu.permission.api.vo.LecturerInfoVO;

import java.util.List;

/**
 * 主讲老师属性表 服务类
 *
 * <AUTHOR>
 * @date 2024-11-25 15:08:25
 */
public interface LecturerInfoService extends IService<LecturerInfo> {

    /**
     * 分页查询
     *
     * @param page  分页对象
     * @param query 查询条件
     * @return Page  对象列表
     */
    Page<LecturerInfoVO> pageLecturer(Page<?> page, LecturerInfoQuery query);

    /**
     * 查询主讲老师
     */
    List<LecturerInfoVO> lists(Integer status);

    /**
     * 通过名称查询主讲老师属性表
     *
     * @param query 查询条件
     * @return List  对象列表
     */
    List<LecturerInfoVO> listDetails(LecturerInfoQuery query);

    /**
     * 通过id查询主讲老师属性表
     *
     * @param query 查询条件
     * @return LecturerInfoVO  对象列表
     */
    LecturerInfoVO getDetailsById(LecturerInfoQuery query);

    /**
     * 新增主讲老师属性表
     *
     * @param addDto 主讲老师属性表传输类
     * @return Boolean
     */
    Boolean saveLecturer(LecturerInfoAddDTO addDto);

    /**
     * 修改主讲老师属性表
     *
     * @param lecturerInfoDTO 主讲老师属性表传输类
     * @return Boolean
     */
    Boolean updateLecturer(LecturerInfoDTO lecturerInfoDTO);

}
