package com.yuedu.permission.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yuedu.permission.api.entity.SysRole;
import com.yuedu.permission.mapper.SysRoleMapper;
import com.yuedu.permission.service.SysRoleService;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

/**
 * 角色服务实现类
 *
 * <AUTHOR>
 * @date 2024/09/29
 */
@Service
@AllArgsConstructor
public class SysRoleServiceImpl extends ServiceImpl<SysRoleMapper, SysRole> implements SysRoleService {
}
