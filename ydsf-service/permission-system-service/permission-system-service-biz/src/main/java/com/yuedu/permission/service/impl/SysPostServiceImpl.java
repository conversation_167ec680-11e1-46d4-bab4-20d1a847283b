package com.yuedu.permission.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yuedu.permission.api.constant.WxCpCommonConstant;
import com.yuedu.permission.api.entity.SysPost;
import com.yuedu.permission.mapper.SysPostMapper;
import com.yuedu.permission.service.SysPostService;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;

/**
 * 岗位服务实现类
 *
 * <AUTHOR>
 * @date 2024/09/29
 */
@Service
public class SysPostServiceImpl extends ServiceImpl<SysPostMapper, SysPost> implements SysPostService {
    @Value("${wxConfig.tenantId}")
    private Long tenantId;

    @Override
    public boolean save(String position) {
        SysPost sysPost = new SysPost();
        sysPost.setPostCode(position);
        sysPost.setPostName(position);
        sysPost.setPostSort(0);
        sysPost.setRemark(position);
        sysPost.setCreateBy(WxCpCommonConstant.CREATE_BY_ADMIN);
        sysPost.setUpdateBy(WxCpCommonConstant.UPDATE_BY_ADMIN);
        sysPost.setCreateTime(LocalDateTime.now());
        sysPost.setUpdateTime(LocalDateTime.now());
        sysPost.setTenantId(tenantId);

        return super.save(sysPost);
    }
}
