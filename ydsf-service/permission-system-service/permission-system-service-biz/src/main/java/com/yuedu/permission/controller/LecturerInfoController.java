package com.yuedu.permission.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yuedu.permission.api.constant.LeadTeacherConstant;
import com.yuedu.permission.api.dto.LecturerInfoAddDTO;
import com.yuedu.permission.api.dto.LecturerInfoDTO;
import com.yuedu.permission.api.query.LecturerInfoQuery;
import com.yuedu.permission.api.valid.LecturerInfoValidGroup;
import com.yuedu.permission.api.vo.LecturerInfoVO;
import com.yuedu.permission.service.LecturerInfoService;
import com.yuedu.ydsf.common.core.util.R;
import com.yuedu.ydsf.common.log.annotation.SysLog;
import com.yuedu.ydsf.common.security.annotation.HasPermission;
import com.yuedu.ydsf.common.security.annotation.Inner;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springdoc.core.annotations.ParameterObject;
import org.springframework.http.HttpHeaders;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 主讲老师属性表 控制类
 *
 * <AUTHOR>
 * @date 2024-11-25 15:08:25
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/LecturerInfo")
@Tag(description = "lecturer_info", name = "主讲老师属性表管理")
@SecurityRequirement(name = HttpHeaders.AUTHORIZATION)
public class LecturerInfoController {

    private final LecturerInfoService lecturerInfoService;

    /**
     * 分页查询
     *
     * @param page  分页对象
     * @param query 查询条件
     * @return R
     */
    @Operation(summary = "分页查询", description = "分页查询")
    @GetMapping("/page")
    @HasPermission("teaching_LecturerInfo_view")
    public R<Page<LecturerInfoVO>> getLecturerInfoPage(@ParameterObject Page<?> page,
                                                       @ParameterObject LecturerInfoQuery query) {
        return R.ok(lecturerInfoService.pageLecturer(page, query));
    }

    /**
     * 查询代课主讲老师
     * 后续可以扩展为根据代课状态查询
     *
     * @return R
     */
    @Operation(summary = "查询代课主讲老师", description = "查询代课主讲老师")
    @GetMapping("/list")
    public R<List<LecturerInfoVO>> list(@RequestParam(name = "status", required = false) Integer status) {
        return R.ok(lecturerInfoService.lists(status));
    }

    /**
     * 通过名称查询主讲老师属性表
     *
     * @param lecturerInfoQuery 查询条件
     * @return R  对象列表
     */
    @Operation(summary = "通过名称查询", description = "通过名称查询对象")
    @GetMapping("/details")
    @HasPermission("teaching_LecturerInfo_view")
    public R<List<LecturerInfoVO>> getDetails(@Validated({ LecturerInfoValidGroup.GetDetails.class })
                                              @ParameterObject LecturerInfoQuery lecturerInfoQuery) {
        return R.ok(lecturerInfoService.listDetails(lecturerInfoQuery));
    }

    /**
     * 通过id查询主讲老师属性表
     *
     * @param lecturerInfoQuery 查询条件
     * @return R  对象列表
     */
    @Operation(summary = "通过id查询", description = "通过id查询对象")
    @GetMapping("/detailsById")
    @HasPermission("teaching_LecturerInfo_view")
    public R<LecturerInfoVO> getDetailsById(@Validated({ LecturerInfoValidGroup.GetDetailsById.class })
                                            @ParameterObject LecturerInfoQuery lecturerInfoQuery) {
        return R.ok(lecturerInfoService.getDetailsById(lecturerInfoQuery));
    }


    /**
     * 新增主讲老师属性表
     *
     * @param lecturerInfoAddDTO 主讲老师属性表
     * @return R
     */
    @Operation(summary = "新增主讲老师属性表", description = "新增主讲老师属性表")
    @SysLog("新增主讲老师属性表")
    @PostMapping("/add")
    @HasPermission("teaching_LecturerInfo_add")
    public R<Boolean> save(@Valid @RequestBody LecturerInfoAddDTO lecturerInfoAddDTO) {
        return R.ok(lecturerInfoService.saveLecturer(lecturerInfoAddDTO));
    }

    /**
     * 修改主讲老师属性表
     *
     * @param lecturerInfoDTO 主讲老师属性表
     * @return R
     */
    @Operation(summary = "修改主讲老师属性表", description = "修改主讲老师属性表")
    @SysLog("修改主讲老师属性表")
    @PutMapping("/edit")
    @HasPermission("teaching_LecturerInfo_edit")
    public R<Boolean> updateById(@Validated @RequestBody LecturerInfoDTO lecturerInfoDTO) {
        return R.ok(lecturerInfoService.updateLecturer(lecturerInfoDTO));
    }

    /**
     * 内部调用:查询代课主讲老师
     *
     * @return R
     */
    @Operation(summary = "内部调用:查询代课主讲老师", description = "内部调用:查询代课主讲老师")
    @GetMapping("/info")
    @Inner
    public R<List<LecturerInfoVO>> getLecturerInfo() {
        return R.ok(lecturerInfoService.lists(LeadTeacherConstant.LECTURER_INFO_STATUS_ENABLE));
    }

    /**
     * 内部调用:查询全部主讲老师
     *
     * @return R
     */
    @Operation(summary = "内部调用:查询全部主讲老师", description = "内部调用:查询全部主讲老师")
    @GetMapping("/AllInfo")
    @Inner
    public R<List<LecturerInfoVO>> getAllLecturerInfo() {
        return R.ok(lecturerInfoService.lists(LeadTeacherConstant.LECTURER_INFO_STATUS_ALL));
    }
}
