package com.yuedu.permission.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yuedu.permission.api.entity.SysSocialDetails;
import com.yuedu.permission.mapper.SysSocialDetailsMapper;
import com.yuedu.permission.service.SysSocialDetailsService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * 系统社交登录账号表
 *
 * <AUTHOR>
 * @date 2024/09/29
 */
@Slf4j
@AllArgsConstructor
@Service("sysSocialDetailsService")
public class SysSocialDetailsServiceImpl extends ServiceImpl<SysSocialDetailsMapper, SysSocialDetails>
        implements SysSocialDetailsService {

}
