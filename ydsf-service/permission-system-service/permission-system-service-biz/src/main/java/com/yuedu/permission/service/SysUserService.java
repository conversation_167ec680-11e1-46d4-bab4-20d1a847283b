package com.yuedu.permission.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.yuedu.permission.api.entity.SysUser;
import com.yuedu.permission.api.query.SysUserQuery;
import com.yuedu.permission.api.vo.SysUserInfoVO;
import me.chanjar.weixin.cp.bean.WxCpUser;

import java.util.List;

/**
 * 用户服务类
 *
 * <AUTHOR>
 * @date 2024/09/29
 */
public interface SysUserService extends IService<SysUser> {

    boolean save(WxCpUser cpUser);

    /**
     * 通过名称查询用户列表
     *
     * @param query 查询条件
     * @return List  对象列表
     */
    List<SysUserInfoVO> listUsersByName(SysUserQuery query);
}
