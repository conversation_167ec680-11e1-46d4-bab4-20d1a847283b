/*
 *
 *      Copyright (c) 2018-2025, ydsf All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 * Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the pig4cloud.com developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: ydsf
 *
 */

package com.yuedu.permission.mapper;

import com.yuedu.permission.api.entity.SysUser;
import com.yuedu.ydsf.common.data.datascope.YdsfBaseMapper;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * <p>
 * 用户表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2017-10-29
 */
@Mapper
public interface SysUserMapper extends YdsfBaseMapper<SysUser> {
    /**
     * 查询全部用户数据
     *
     * @return List<SysUser>
     */
    List<SysUser> selectUserList();

    /**
     * 更新用户数据
     *
     * @param user 用户
     */
    void updateUser(SysUser user);
}
