package com.yuedu.permission.service;

import com.yuedu.permission.api.dto.SysUserDTO;
import com.yuedu.permission.api.vo.SysUserVO;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 主讲老师服务
 *
 * <AUTHOR>
 * @date 2024/10/15
 **/
@Service
public interface LeadTeacherService {

    /**
     * 获取主讲老师列表 -无昵称
     *
     * @return 主讲老师列表
     */
    List<SysUserVO> list();

    /**
     * 获取主讲老师列表 -拼接昵称
     *
     * @return 主讲老师列表
     */
    List<SysUserVO> listNikeName();

    /**
     * 根据主讲老师id获取主讲老师信息
     *
     * @param id 主讲老师id
     * @return 主讲老师信息
     */
    SysUserVO getById(Long id);

    /**
     * 根据主讲老师id列表获取主讲老师列表
     *
     * @param ids 主讲老师id列表
     * @return 主讲老师列表
     */
    List<SysUserVO> listById(SysUserDTO ids);
}
