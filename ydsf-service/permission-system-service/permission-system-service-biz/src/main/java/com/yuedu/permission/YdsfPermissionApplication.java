/*
 *
 *      Copyright (c) 2018-2025, ydsf All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 * Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the pig4cloud.com developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: ydsf
 *
 */

package com.yuedu.permission;

import com.yuedu.ydsf.common.feign.annotation.EnableYdsfFeignClients;
import com.yuedu.ydsf.common.job.annotation.EnableYdsfXxlJob;
import com.yuedu.ydsf.common.security.annotation.EnableYdsfResourceServer;
import com.yuedu.ydsf.common.swagger.annotation.EnableOpenApi;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;

/**
 * <AUTHOR>
 * @date 2024年09月20日
 * <p>
 * 用户统一管理系统副本
 */
@EnableOpenApi("permission")
@EnableYdsfResourceServer
@EnableDiscoveryClient
@EnableYdsfFeignClients
@SpringBootApplication
@EnableYdsfXxlJob
public class YdsfPermissionApplication {

	public static void main(String[] args) {
		SpringApplication.run(YdsfPermissionApplication.class, args);
	}

}
