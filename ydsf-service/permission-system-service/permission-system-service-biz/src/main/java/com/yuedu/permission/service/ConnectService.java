package com.yuedu.permission.service;

import com.yuedu.ydsf.common.core.util.R;
import me.chanjar.weixin.cp.config.impl.WxCpDefaultConfigImpl;

/**
 * 同步服务
 *
 * <AUTHOR>
 * @date 2024/9/23
 */
public interface ConnectService {

    /**
     * 同步企微部门
     *
     * @return Boolean
     */
    R<Boolean> syncCpDept();

    /**
     * 同步企微用户
     *
     * @return Boolean
     */
    R<Boolean> syncCpUser();

    WxCpDefaultConfigImpl getCpConfig();

}
