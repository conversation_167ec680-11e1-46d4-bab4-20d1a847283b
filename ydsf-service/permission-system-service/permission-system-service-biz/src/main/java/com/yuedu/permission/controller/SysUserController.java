package com.yuedu.permission.controller;

import com.yuedu.permission.api.query.SysUserQuery;
import com.yuedu.permission.api.valid.SysUserValidGroup;
import com.yuedu.permission.api.vo.SysUserInfoVO;
import com.yuedu.permission.service.SysUserService;
import com.yuedu.ydsf.common.core.util.R;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springdoc.core.annotations.ParameterObject;
import org.springframework.http.HttpHeaders;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 用户表 控制类
 *
 * <AUTHOR>
 * @date 2024-11-25 15:08:25
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/SysUser")
@Tag(description = "sys_user", name = "用户表管理")
@SecurityRequirement(name = HttpHeaders.AUTHORIZATION)
public class SysUserController {

    private final SysUserService SysUserService;

    /**
     * 通过名称查询主讲老师属性表
     *
     * @param query 查询条件
     * @return R  对象列表
     */
    @Operation(summary = "通过名称查询", description = "通过名称查询对象")
    @GetMapping("/usersByName")
    public R<List<SysUserInfoVO>> getUsersByName(@Validated({ SysUserValidGroup.GetUsersByName.class })
                                                 @ParameterObject SysUserQuery query) {
        return R.ok(SysUserService.listUsersByName(query));
    }
}
