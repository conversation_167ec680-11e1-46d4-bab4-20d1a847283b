package com.yuedu.permission.service.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.yuedu.permission.api.constant.LeadTeacherConstant;
import com.yuedu.permission.api.dto.SysUserDTO;
import com.yuedu.permission.api.entity.SysUser;
import com.yuedu.permission.api.entity.SysUserPost;
import com.yuedu.permission.api.vo.SysUserVO;
import com.yuedu.permission.service.LeadTeacherService;
import com.yuedu.permission.service.SysUserPostService;
import com.yuedu.permission.service.SysUserService;
import com.yuedu.ydsf.common.core.exception.BizException;
import com.yuedu.ydsf.common.data.resolver.ParamResolver;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * 主讲老师服务实现类
 *
 * <AUTHOR>
 * @date 2024/10/15
 **/
@Slf4j
@Service
@RequiredArgsConstructor
public class LeadTeacherServiceImpl implements LeadTeacherService {

    private final SysUserService sysUserService;

    private final SysUserPostService sysUserPostService;

    /**
     * 获取主讲老师列表 -无昵称
     *
     * @return 主讲老师列表
     */
    @Override
    public List<SysUserVO> list() {
        List<Long> userIdList = getUserIdList();
        return convertToSysUserVOList(userIdList, false);
    }

    /**
     * 获取主讲老师列表 -拼接昵称
     *
     * @return 主讲老师列表
     */
    @Override
    public List<SysUserVO> listNikeName() {
        List<Long> userIdList = getUserIdList();
        return convertToSysUserVOList(userIdList, true);
    }

    /**
     * 根据主讲老师id获取主讲老师信息
     *
     * @param id 主讲老师id
     * @return 主讲老师信息
     */
    @Override
    public SysUserVO getById(Long id) {
        SysUser sysUser = sysUserService.getOne(Wrappers.<SysUser>lambdaQuery().eq(SysUser::getUserId, id));
        SysUserVO sysUserVO = new SysUserVO();
        sysUserVO.setUserId(sysUser.getUserId());
        sysUserVO.setName(sysUser.getNickname() == null ? sysUser.getName() : sysUser.getNickname());
        return sysUserVO;
    }

    /**
     * 根据主讲老师id列表获取主讲老师列表
     *
     * @param ids 主讲老师id列表
     * @return 主讲老师列表
     */
    @Override
    public List<SysUserVO> listById(SysUserDTO ids) {
        List<Long> userIdList = ids.getUserIdList();
        return convertToSysUserVOList(userIdList, false);
    }

    /**
     * 获取主讲老师id列表
     *
     * @return 主讲老师id列表
     */
    @NotNull
    private List<Long> getUserIdList() {
        String leadTeacher = ParamResolver.getStr(LeadTeacherConstant.LEAD_TEACHER_ID);
        if (leadTeacher.isBlank()){
            throw new BizException("主讲老师ID参数配置为空");
        }

        List<SysUserPost> listUserPost = sysUserPostService
                .list(Wrappers.<SysUserPost>lambdaQuery().eq(SysUserPost::getPostId, leadTeacher));
        return listUserPost.stream().map(SysUserPost::getUserId).toList();
    }

    /**
     * 将主讲老师id列表转换为主讲老师列表
     *
     * @param userIdList 主讲老师id列表
     * @param isNickname 是否拼接昵称
     * @return 主讲老师列表
     */
    @NotNull
    private List<SysUserVO> convertToSysUserVOList(List<Long> userIdList, boolean isNickname) {
        List<SysUser> sysUsers = sysUserService.listByIds(userIdList);
        List<SysUserVO> sysUserVOList = new ArrayList<>();
        if (isNickname) {
            sysUsers.forEach(user -> {
                SysUserVO sysUserVO = new SysUserVO();
                sysUserVO.setUserId(user.getUserId());
                String nickname = user.getNickname();
                sysUserVO.setName((nickname == null || nickname.isEmpty()) ? user.getName() : user.getName()
                        + "【" + nickname + "】");
                sysUserVOList.add(sysUserVO);
            });
        } else {
            sysUsers.forEach(user -> {
                SysUserVO sysUserVO = new SysUserVO();
                sysUserVO.setUserId(user.getUserId());
                sysUserVO.setName(user.getName());
                sysUserVOList.add(sysUserVO);
            });
        }
        return sysUserVOList;
    }
}
