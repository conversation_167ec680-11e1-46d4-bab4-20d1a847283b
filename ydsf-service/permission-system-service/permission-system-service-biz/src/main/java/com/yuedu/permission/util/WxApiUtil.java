package com.yuedu.permission.util;

import com.yuedu.ydsf.common.core.exception.ErrorCodes;
import lombok.extern.slf4j.Slf4j;
import me.chanjar.weixin.common.error.WxErrorException;
import me.chanjar.weixin.cp.api.WxCpService;
import me.chanjar.weixin.cp.api.impl.WxCpServiceImpl;
import me.chanjar.weixin.cp.bean.WxCpDepart;
import me.chanjar.weixin.cp.bean.WxCpUser;
import me.chanjar.weixin.cp.config.impl.WxCpDefaultConfigImpl;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/09/27
 **/

@Slf4j
public class WxApiUtil {

    /**
     * 获取企业微信部门列表
     *
     * @param config 微信配置
     * @return List<WxCpDepart> 微信部门列表
     */
    public static List<WxCpDepart> getDeptList(WxCpDefaultConfigImpl config) {
        WxCpService wxCpService = new WxCpServiceImpl();
        wxCpService.setWxCpConfigStorage(config);

        // 用于存储企业微信部门列表的变量
        List<WxCpDepart> departList;
        try {
            departList = wxCpService.getDepartmentService().list(null);
        } catch (WxErrorException e) {
            log.error("获取企业微信部门列表失败", e);
            throw new RuntimeException(ErrorCodes.SYS_CONNECT_CP_DEPT_SYNC_ERROR);
        }

        return departList;
    }

    /**
     * 获取企业微信用户列表
     *
     * @param config 微信配置
     * @param deptId 部门id
     * @return List<WxCpUser> 微信用户列表
     */
    public static List<WxCpUser> getUserList(WxCpDefaultConfigImpl config, Long deptId) {
        WxCpService wxCpService = new WxCpServiceImpl();
        wxCpService.setWxCpConfigStorage(config);

        List<WxCpUser> cpUserList;

        // 尝试获取企业微信用户列表，1L表示部门ID，true表示递归获取子部门用户，0表示获取启用的用户
        try {
            cpUserList = wxCpService.getUserService().listByDepartment(deptId, true, 0);
        } catch (WxErrorException e) {
            // 如果获取用户列表失败，记录错误并返回错误信息
            log.error("获取企业微信用户列表失败", e);
            throw new RuntimeException(ErrorCodes.SYS_CONNECT_CP_USER_SYNC_ERROR);
        }

        return cpUserList;
    }

//    // 获取部门列表
//    private List<WxCpDepart> getWxCpDeparts() {
//        List<WxCpDepart> departList;
//        try {
//            departList = WxApiUtil.getDeptList(listCpConfig());
//        } catch (WxErrorException e) {
//            log.error("企业微信配置错误", e);
//            throw new RuntimeException(e);
//        }
//        return departList;
//    }
//
//    public List<WxCpUser> getWxCpUsers(SysDept one) {
//        List<WxCpUser> cpUserList;
//        try {
//            cpUserList = WxApiUtil.getUserList(listCpConfig(), one.getDeptId());
//        } catch (WxErrorException e) {
//            log.error("企业微信配置错误", e);
//            throw new RuntimeException(e);
//        }
//        return cpUserList;
//    }
}
