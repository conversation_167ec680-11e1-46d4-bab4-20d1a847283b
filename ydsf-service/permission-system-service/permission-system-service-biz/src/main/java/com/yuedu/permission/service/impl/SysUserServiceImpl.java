package com.yuedu.permission.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.LocalDateTimeUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yuedu.permission.api.constant.WxCpCommonConstant;
import com.yuedu.permission.api.entity.SysUser;
import com.yuedu.permission.api.query.SysUserQuery;
import com.yuedu.permission.api.vo.SysUserInfoVO;
import com.yuedu.permission.mapper.SysUserMapper;
import com.yuedu.permission.service.SysUserService;
import com.yuedu.permission.util.TelephoneUtil;
import com.yuedu.ydsf.admin.api.constant.UserStateEnum;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import me.chanjar.weixin.cp.bean.WxCpUser;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 用户服务实现类
 *
 * <AUTHOR>
 * @date 2024/09/29
 */
@Slf4j
@Service
@AllArgsConstructor
public class SysUserServiceImpl extends ServiceImpl<SysUserMapper, SysUser> implements SysUserService {
    private static final PasswordEncoder ENCODER = new BCryptPasswordEncoder();

    private SysUserMapper sysUserMapper;

    @Value("${wxConfig.tenantId}")
    private Long tenantId;

    @Override
    public boolean save(WxCpUser cpUser) {
        SysUser sysUser = new SysUser();
        String phone = TelephoneUtil.getPhoneFromWxCpUser(cpUser);

        sysUser.setUsername(phone.isEmpty() ? cpUser.getUserId().toLowerCase() : phone);
        // 初始化密码为手机号 没有手机号的默认密码为123456
        sysUser.setPassword(ENCODER.encode(phone.isEmpty() ? WxCpCommonConstant.DEFAULT_PASSWORD : phone));
        sysUser.setCreateBy(WxCpCommonConstant.CREATE_BY_ADMIN);
        sysUser.setUpdateBy(WxCpCommonConstant.UPDATE_BY_ADMIN);
        sysUser.setUpdateTime(LocalDateTimeUtil.now());
        sysUser.setLockFlag(UserStateEnum.NORMAL.getCode());
        sysUser.setPasswordModifyTime(LocalDateTime.now());
        sysUser.setPhone(phone);
        sysUser.setAvatar(cpUser.getAvatar());
        sysUser.setDeptId(cpUser.getDepartIds()[ 0 ]);
        sysUser.setTenantId(tenantId);
        sysUser.setWxCpUserid(cpUser.getUserId());
        sysUser.setName(cpUser.getName());
        sysUser.setEmail(cpUser.getEmail());
        return super.save(sysUser);
    }

    /**
     * 通过名称查询用户列表
     *
     * @param query 查询条件
     * @return List  对象列表
     */
    @Override
    public List<SysUserInfoVO> listUsersByName(SysUserQuery query) {
        List<SysUser> sysUsers = sysUserMapper.selectList(Wrappers.<SysUser>lambdaQuery()
                .like(SysUser::getName, query.getName()));
        if (sysUsers.isEmpty()) {
            return List.of();
        }
        return sysUsers.stream().map(s -> BeanUtil.copyProperties(s, SysUserInfoVO.class)).toList();
    }
}
