package com.yuedu.permission.controller;

import com.yuedu.permission.api.dto.SysUserDTO;
import com.yuedu.permission.api.vo.SysUserVO;
import com.yuedu.permission.service.LeadTeacherService;
import com.yuedu.ydsf.common.core.util.R;
import com.yuedu.ydsf.common.security.annotation.Inner;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpHeaders;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 主讲老师
 *
 * <AUTHOR>
 * @date 2024/10/15
 **/
@RestController
@RequiredArgsConstructor
@RequestMapping("/leadTeacher")
@Tag(description = "leadTeacher", name = "主讲老师")
@SecurityRequirement(name = HttpHeaders.AUTHORIZATION)
public class LeadTeacherController {

    private final LeadTeacherService leadTeacherService;

    /**
     * 获取主讲老师列表
     *
     * @return List<SysUser>
     */
    @Inner
    @GetMapping("/list")
    @Operation(summary = "获取全部主讲老师列表", description = "获取全部主讲老师列表")
    public R<List<SysUserVO>> list() {
        return R.ok(leadTeacherService.list());
    }

    /**
     * 根据id查询主讲老师
     *
     * @param id 主讲老师id
     * @return SysUser
     */
    @Inner
    @GetMapping("/get/{id}")
    @Operation(summary = "根据id查询主讲老师", description = "根据id查询主讲老师")
    public R<SysUserVO> getById(@PathVariable Long id) {
        return R.ok(leadTeacherService.getById(id));
    }

    /**
     * 根据id列表获取主讲老师列表
     *
     * @param ids 主讲老师id列表
     * @return List<SysUser>
     */
    @Inner
    @PostMapping("/listUser")
    @Operation(summary = "根据id列表获取主讲老师列表", description = "根据id列表获取主讲老师列表")
    public R<List<SysUserVO>> listById(@RequestBody SysUserDTO ids) {
        return R.ok(leadTeacherService.listById(ids));
    }
}
