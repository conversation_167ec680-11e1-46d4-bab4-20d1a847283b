package com.yuedu.permission.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.yuedu.permission.api.constant.WxCpCommonConstant;
import com.yuedu.permission.api.entity.*;
import com.yuedu.permission.mapper.SysDeptMapper;
import com.yuedu.permission.mapper.SysUserMapper;
import com.yuedu.permission.service.*;
import com.yuedu.permission.util.TelephoneUtil;
import com.yuedu.permission.util.WxApiUtil;
import com.yuedu.ydsf.common.core.constant.enums.LoginTypeEnum;
import com.yuedu.ydsf.common.core.util.R;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import me.chanjar.weixin.cp.bean.WxCpDepart;
import me.chanjar.weixin.cp.bean.WxCpUser;
import me.chanjar.weixin.cp.config.impl.WxCpDefaultConfigImpl;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 同步服务实现
 *
 * <AUTHOR>
 * @date 2024/9/23
 */
//@SuppressWarnings("unchecked")
@Slf4j
@Service
@RequiredArgsConstructor
public class ConnectServiceImpl implements ConnectService {

    private final SysDeptService sysDeptService;

    private final SysUserService sysUserService;

    private final SysPostService sysPostService;

    private final SysUserPostService sysUserPostService;

    private final SysUserRoleService sysUserRoleService;

    private final SysSocialDetailsService sysSocialDetailsService;

    private final SysDeptMapper sysDeptMapper;

    private final SysUserMapper sysUserMapper;

    @Value("${wxConfig.tenantId}")
    private Long tenantId;


    /**
     * 同步企微部门
     *
     * @return R
     */
    @Override
    @XxlJob(value = "syncCpDept")
    @Transactional(rollbackFor = Exception.class)
    public R<Boolean> syncCpDept() {
        List<WxCpDepart> wxDepartList = WxApiUtil.getDeptList(getCpConfig());
        List<SysDept> dbDepts = sysDeptMapper.selectList();

        //获取所有部门ids
        Set<Long> dbDeptIds = dbDepts.stream().map(SysDept::getDeptId).collect(Collectors.toSet());
        Set<Long> wxDeptIds = wxDepartList.stream().map(WxCpDepart::getId).collect(Collectors.toSet());
        // 转map id为key
        Map<Long, SysDept> dbDeptMap = dbDepts.stream().collect(Collectors.toMap(SysDept::getDeptId, e -> e));

        // 数据库存在 但名称不相等 或 删除标记为已删除
        List<WxCpDepart> existingDepts = filterModifiedOrDeletedDepts(wxDepartList, dbDeptIds, dbDeptMap);
        // 数据库不存在 新增
        List<WxCpDepart> newDepts = wxDepartList.stream().filter(s -> !dbDeptIds.contains(s.getId())).toList();
        // 企微不存在 删除
        List<SysDept> obsoleteDepts = dbDepts.stream().filter(s -> !wxDeptIds.contains(s.getDeptId())).toList();

        // 删除
        sysDeptService.removeByIds(obsoleteDepts);
        // 新增
        addDept(newDepts);
        // 修改
        updateDept(existingDepts);

        log.info("企微部门数据同步完成");
        return R.ok();
    }

    /**
     * 同步企微用户
     *
     * @return R
     */
    @Override
    @XxlJob(value = "syncCpUser")
    public R<Boolean> syncCpUser() {
        SysDept one = sysDeptMapper.selectOneByDeptId();
        // 获取员工数据
        List<WxCpUser> wxCpUserList = WxApiUtil.getUserList(getCpConfig(), one.getDeptId());
        List<SysUser> allUsers = sysUserMapper.selectUserList();

        // 获取员工id
        Set<String> dbIds = allUsers.stream().map(SysUser::getWxCpUserid).collect(Collectors.toSet());
        Set<String> wxIds = wxCpUserList.stream().map(WxCpUser::getUserId).collect(Collectors.toSet());
        Map<String, SysUser> dbUserMap = allUsers.stream().collect(Collectors.toMap(SysUser::getWxCpUserid, e -> e));

        // 已离职的员工
        List<Long> delUserList = getDelUserList(allUsers, wxIds);
        // 已存在的员工 被修改或被标记为已删除
        List<WxCpUser> oldList = filterModifiedOrDeletedUsers(wxCpUserList, dbIds, dbUserMap);
        // 新员工
        List<WxCpUser> newList = wxCpUserList.stream().filter(s -> !dbIds.contains(s.getUserId())).toList();

        // 禁用已离职的员工
        disableDepartedUsers(delUserList);
        // 更新已存在的员工
        updateUser(oldList);
        // 添加新员工
        addUser(newList);

        log.info("企微员工数据同步完成");
        return R.ok();
    }

    /**
     * 获取已离职的员工
     *
     * @param allUsers 所有员工
     * @param wxIds    企业微信id
     * @return 离职员工id集合
     */
    @NotNull
    private List<Long> getDelUserList(List<SysUser> allUsers, Set<String> wxIds) {
        return allUsers.stream()
                .filter(s -> s.getDelFlag().equals(WxCpCommonConstant.DELETED_STATUS_NO))
                .filter(s -> !wxIds.contains(s.getWxCpUserid()))
                .map(SysUser::getUserId).toList();
    }

    /**
     * 获取已存在的员工中被修改或被标记为已删除
     *
     * @param wxCpUserList 企业微信用户数据
     * @param dbIds        数据库用户id
     * @param dbUserMap    数据库用户map
     * @return 企业微信用户数据
     */
    @NotNull
    private List<WxCpUser> filterModifiedOrDeletedUsers(List<WxCpUser> wxCpUserList, Set<String> dbIds, Map<String, SysUser> dbUserMap) {
        return wxCpUserList.stream()
                .filter(s -> dbIds.contains(s.getUserId()))
                .filter(wxUser -> {
                    SysUser dbUser = dbUserMap.get(wxUser.getUserId());
                    return dbUser != null && !areUsersEqual(wxUser, dbUser);
                }).toList();
    }

    /**
     * 获取企微部门数据
     * 数据库存在 但名称不相等 或 删除标记为已删除
     *
     * @param wxDepartList 企微部门数据
     * @param dbDeptIds    数据库存在部门id
     * @param dbDeptMap    数据库存在部门map
     * @return List<WxCpDepart>
     */
    @NotNull
    private List<WxCpDepart> filterModifiedOrDeletedDepts(List<WxCpDepart> wxDepartList, Set<Long> dbDeptIds, Map<Long, SysDept> dbDeptMap) {
        return wxDepartList.stream()
                .filter(s -> dbDeptIds.contains(s.getId()))
                .filter(s -> {
                    SysDept sysDept = dbDeptMap.get(s.getId());
                    return !s.getName().equals(sysDept.getName()) ||
                            !Objects.equals(sysDept.getDelFlag(), WxCpCommonConstant.DELETED_STATUS_NO);
                })
                .toList();
    }

    /**
     * 更新部门
     *
     * @param departList 企微中部门数据
     */
    private void updateDept(List<WxCpDepart> departList) {
        List<SysDept> updateSysDept = departList.stream()
                .map(s -> SysDept.builder()
                        .deptId(s.getId())
                        .name(s.getName())
                        .sortOrder(s.getOrder().intValue())
                        .parentId(s.getParentId())
                        .delFlag(WxCpCommonConstant.DELETED_STATUS_NO)
                        .tenantId(tenantId)
                        .build())
                .toList();

        // 批量更新部门
        if (!updateSysDept.isEmpty()) {
            for (SysDept sysDept : updateSysDept) {
                sysDeptMapper.updateDept(sysDept);
            }
        }
    }

    /**
     * 新增部门
     *
     * @param departList 企微中部门数据
     */
    private void addDept(List<WxCpDepart> departList) {
        // 过滤出需要新增的部门
        List<SysDept> newDepts = departList.stream()
                .map(dept -> {
                    SysDept sysDept = new SysDept();
                    sysDept.setDeptId(dept.getId());
                    sysDept.setName(dept.getName());
                    sysDept.setSortOrder(dept.getOrder().intValue());
                    sysDept.setParentId(dept.getParentId());
                    sysDept.setTenantId(tenantId);
                    return sysDept;
                })
                .toList();

        // 批量插入部门
        // id 不能重复
        if (!newDepts.isEmpty()) {
            sysDeptService.saveBatch(newDepts);
        }
    }

    /**
     * 新增员工
     *
     * @param cpUserList 企微中员工数据
     */
    private void addUser(List<WxCpUser> cpUserList) {
        if (cpUserList.isEmpty()) {
            return;
        }

        List<SysPost> sysPosts = sysPostService.list(Wrappers.emptyWrapper());

        Map<String, Long> collect = sysPosts.stream()
                .collect(Collectors.toMap(SysPost::getPostName, SysPost::getPostId));

        cpUserList.forEach(cpUser -> {
            Long userId = saveUser(cpUser);
            // 插入角色
            insertRole(userId);
            // 插入岗位
            insertPosition(userId, cpUser.getPosition(), collect);
        });
    }

    /**
     * 禁用已离职的员工
     *
     * @param delUserList 已离职的员工id
     */
    private void disableDepartedUsers(List<Long> delUserList) {
        if (delUserList.isEmpty()) {
            return;
        }
        sysUserService.update(Wrappers.<SysUser>lambdaUpdate()
                .set(SysUser::getLockFlag, 9)
                .in(SysUser::getUserId, delUserList));
    }

    /**
     * 保存用户
     *
     * @param cpUser 企微用户数据
     * @return 用户ID
     */
    private Long saveUser(WxCpUser cpUser) {
        sysUserService.save(cpUser);
        return sysUserService.getOne(Wrappers.<SysUser>lambdaQuery()
                        .eq(SysUser::getWxCpUserid, cpUser.getUserId())
                        .last("limit 1"))
                .getUserId();
    }

    /**
     * 插入角色
     *
     * @param userId 用户ID
     */
    private void insertRole(Long userId) {
        SysUserRole sysUserRole = new SysUserRole();
        sysUserRole.setUserId(userId);
        sysUserRole.setRoleId(2L);
        sysUserRoleService.save(sysUserRole);
    }

    /**
     * 插入岗位
     *
     * @param userId   用户ID
     * @param position 岗位名称
     * @param postMap  岗位映射表
     */
    private void insertPosition(Long userId, String position, Map<String, Long> postMap) {
        if (!position.isEmpty()) {
            Long postId = postMap.get(position);
            if (postId == null) {
                boolean insert = sysPostService.save(position);
                if (insert) {
                    postId = sysPostService.getOne(Wrappers.<SysPost>lambdaQuery()
                            .eq(SysPost::getPostName, position)).getPostId();
                    postMap.put(position, postId);
                }
            }
            SysUserPost sysUserPost = new SysUserPost();
            sysUserPost.setUserId(userId);
            sysUserPost.setPostId(postId);
            sysUserPostService.save(sysUserPost);
        }
    }


    /**
     * 比较企微员工数据是否与数据库中的数据一致
     *
     * @param wxUser 企微员工数据
     * @param dbUser 数据库中的员工数据
     * @return 是否一致
     */
    private boolean areUsersEqual(WxCpUser wxUser, SysUser dbUser) {
        return dbUser.getDelFlag().equals(WxCpCommonConstant.DELETED_STATUS_NO) &&
                ObjectUtil.equals(wxUser.getUserId(), dbUser.getWxCpUserid()) &&
                ObjectUtil.equals(wxUser.getEmail(), dbUser.getEmail()) &&
                ObjectUtil.equals(TelephoneUtil.getPhoneFromWxCpUser(wxUser), dbUser.getPhone()) &&
                ObjectUtil.equals(wxUser.getAvatar(), dbUser.getAvatar()) &&
                ObjectUtil.equals(wxUser.getName(), dbUser.getName()) &&
                ObjectUtil.equals(wxUser.getDepartIds()[ 0 ], dbUser.getDeptId());
    }

    /**
     * 更新已存在的员工
     *
     * @param cpUserList 企微员工数据
     */
    private void updateUser(List<WxCpUser> cpUserList) {
        cpUserList.forEach(cpUser -> {
            SysUser user = SysUser.builder()
                    .wxCpUserid(cpUser.getUserId())
                    .email(cpUser.getEmail())
                    .phone(TelephoneUtil.getPhoneFromWxCpUser(cpUser))
                    .avatar(cpUser.getAvatar())
                    .name(cpUser.getName())
                    .tenantId(tenantId)
                    .deptId(cpUser.getDepartIds()[ 0 ])
                    .delFlag(WxCpCommonConstant.DELETED_STATUS_NO)
                    .build();
            sysUserMapper.updateUser(user);
        });
    }

    /**
     * 获取企业微信配置
     *
     * @return 配置信息
     */
    @Override
    public WxCpDefaultConfigImpl getCpConfig() {
        if (tenantId == null) {
            log.error("租户id不能为null");
            throw new RuntimeException("租户id不能为null");
        }
        SysSocialDetails cp = sysSocialDetailsService.getOne(Wrappers.<SysSocialDetails>lambdaQuery()
                .eq(SysSocialDetails::getType, LoginTypeEnum.WEIXIN_CP.getType())
                .eq(SysSocialDetails::getTenantId, tenantId));

        WxCpDefaultConfigImpl config = new WxCpDefaultConfigImpl();
        // 设置微信企业号的appid
        config.setCorpId(cp.getAppId());
        // 设置微信企业号的app
        config.setCorpSecret(cp.getAppSecret());
//        // corpSecret
//        JSONObject ext = JSONUtil.parseObj(cp.getExt());
//        // 设置微信企业号应用ID
//        config.setAgentId(ext.getInt("agentId"));
//        // 设置微信企业号应用的token
//        config.setToken(ext.getStr("token"));
//        // 设置微信企业号应用的EncodingAESKey
//        config.setAesKey(ext.getStr("aesKey"));

        return config;
    }
}