package com.yuedu.permission.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yuedu.permission.api.constant.LeadTeacherConstant;
import com.yuedu.permission.api.dto.LecturerInfoAddDTO;
import com.yuedu.permission.api.dto.LecturerInfoDTO;
import com.yuedu.permission.api.entity.LecturerInfo;
import com.yuedu.permission.api.entity.SysUser;
import com.yuedu.permission.api.entity.SysUserPost;
import com.yuedu.permission.api.query.LecturerInfoQuery;
import com.yuedu.permission.api.vo.LecturerInfoVO;
import com.yuedu.permission.api.vo.SysUserVO;
import com.yuedu.permission.mapper.LecturerInfoMapper;
import com.yuedu.permission.service.*;
import com.yuedu.ydsf.common.core.exception.BizException;
import com.yuedu.ydsf.common.core.util.R;
import com.yuedu.ydsf.common.data.resolver.ParamResolver;
import com.yuedu.ydsf.eduConnect.api.feign.RemoteTeachingPlanDetailPubService;
import jakarta.annotation.Resource;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 主讲老师属性表 服务类
 *
 * <AUTHOR>
 * @date 2024-11-25 15:08:25
 */
@Slf4j
@Service
@AllArgsConstructor
public class LecturerInfoServiceImpl extends ServiceImpl<LecturerInfoMapper, LecturerInfo> implements LecturerInfoService {

    private LecturerInfoMapper lecturerInfoMapper;

    private SysUserService sysUserService;

    private SysUserPostService sysUserPostService;

    private LeadTeacherService leadTeacherService;

    @Resource
    private RemoteTeachingPlanDetailPubService remoteTeachingPlanService;

    /**
     * 分页查询
     *
     * @param page  分页对象
     * @param query 查询条件
     * @return R  对象列表
     */
    @Override
    public Page<LecturerInfoVO> pageLecturer(Page<?> page, LecturerInfoQuery query) {
        Set<Long> userIds = fetchUserIds(query.getName());
        initLecturerData();

        // 本表查数据
        Page<LecturerInfo> lecturerInfoPage = fetchLecturerInfoPage(page, userIds);

        // 拆包清理数据
        List<LecturerInfoVO> lecturerInfoVoList = convertLecturerInfos(lecturerInfoPage.getRecords());

        // 打包返回
        return createLecturerInfoVoPage(lecturerInfoPage, lecturerInfoVoList);
    }

    /**
     * 查询主讲老师
     *
     * @return R  对象列表
     */
    @Override
    public List<LecturerInfoVO> lists(Integer status) {
        List<LecturerInfo> lecturerInfoList = lecturerInfoMapper.selectList(Wrappers.<LecturerInfo>lambdaQuery()
                .eq(Objects.isNull(status), LecturerInfo::getStatus, LeadTeacherConstant.LECTURER_INFO_STATUS_ENABLE)
                .eq(Objects.nonNull(status) && status == LeadTeacherConstant.LECTURER_INFO_STATUS_ENABLE, LecturerInfo::getStatus, status));
        return convertLecturerInfos(lecturerInfoList);
    }


    /**
     * 通过名称查询主讲老师属性表
     * 只返回启用状态的的主讲老师
     *
     * @param query 查询条件
     * @return R  对象列表
     */
    @Override
    public List<LecturerInfoVO> listDetails(LecturerInfoQuery query) {
        // 从用户表中查询用户
        Set<Long> userIds = fetchUserIds(query.getName());
        if (userIds == null || userIds.isEmpty()) {
            return List.of();
        }

        List<LecturerInfo> lecturerInfoList = lecturerInfoMapper.selectList(Wrappers.<LecturerInfo>lambdaQuery()
                .eq(LecturerInfo::getStatus, LeadTeacherConstant.LECTURER_INFO_STATUS_ENABLE)
                .in(LecturerInfo::getUserId, userIds));

        return convertLecturerInfos(lecturerInfoList);
    }

    /**
     * 通过id查询主讲老师属性表
     *
     * @param query 查询条件
     * @return R  对象列表
     */
    @Override
    public LecturerInfoVO getDetailsById(LecturerInfoQuery query) {
        LecturerInfo lecturer = lecturerInfoMapper.selectOne(Wrappers.<LecturerInfo>lambdaQuery()
                .eq(LecturerInfo::getUserId, query.getUserId()));

        if (ObjectUtil.isNull(lecturer)) {
            throw new BizException("没有该老师");
        }

        SysUser user = sysUserService.getById(query.getUserId());
        Map<Long, SysUser> userMap = Collections.singletonMap(lecturer.getUserId(), user);
        return mapToLecturerInfoVO(lecturer, userMap);
    }

    /**
     * 新增主讲老师属性表
     *
     * @param addDto 主讲老师属性表传输类
     * @return Boolean
     */
    @Override
    public Boolean saveLecturer(LecturerInfoAddDTO addDto) {
        // 检查用户是否存在
        checkUserExists(addDto.getUserId());

        // 检查老师是否存在
        checkLecturerExists(addDto.getUserId());

        // 检查是否是主讲老师职位
        checkAndAddLecturerPost(addDto.getUserId());

        // 添加老师
        // 默认填充值为空
        LecturerInfo lecturer = LecturerInfo.builder()
                .userId(addDto.getUserId())
                .xgjLecturerId(" ")
                .status(LeadTeacherConstant.LECTURER_INFO_STATUS_ENABLE)
                .build();

        // 修改昵称
        if (ObjectUtil.isNotNull(addDto.getNickname())) {
            SysUser user = BeanUtil.copyProperties(addDto, SysUser.class, "name");
            sysUserService.updateById(user);
        }

        lecturerInfoMapper.insert(lecturer);
        return true;
    }

    /**
     * 修改主讲老师属性表
     * 参数为1时，表示从带课修改为不带课，不跳过校验
     * 参数为0时，表示从不带课修改为带课，跳过校验
     *
     * @param lecturerInfoDTO 主讲老师属性表传输类
     * @return Boolean
     */
    @Override
    public Boolean updateLecturer(LecturerInfoDTO lecturerInfoDTO) {
        if (lecturerInfoDTO.getStatus() != LeadTeacherConstant.LECTURER_INFO_STATUS_ENABLE) {
            // 查询主讲老师是否有未结课的排课 true:有未结束的排课 false:无未结束的排课
            R<Boolean> classResult = remoteTeachingPlanService.hasUnfinishedClass(lecturerInfoDTO.getUserId());
            if (!classResult.isOk()) {
                throw new BizException("远程调用失败");
            }
            log.info("主讲老师Id：{},排课状态{}", lecturerInfoDTO.getUserId(), classResult);

            // 有未结课的排课
            if (Boolean.TRUE.equals(classResult.getData())) {
                return false;
            }
        }

        // 没有未结课的排课
        LecturerInfo updatedLecturer = BeanUtil.copyProperties(lecturerInfoDTO, LecturerInfo.class, "xgj_lecturer_id");
        lecturerInfoMapper.updateById(updatedLecturer);
        return true;
    }

    /**
     * 检查是否是主讲老师职位
     *
     * @param userId 用户id
     */
    private void checkAndAddLecturerPost(Long userId) {
        String leadTeacherId = ParamResolver.getStr(LeadTeacherConstant.LEAD_TEACHER_ID);
        SysUserPost userPost = sysUserPostService.getOne(Wrappers.<SysUserPost>lambdaQuery()
                .eq(SysUserPost::getUserId, userId)
                .eq(SysUserPost::getPostId, leadTeacherId));

        if (ObjectUtil.isNull(userPost)) {
            userPost = new SysUserPost();
            userPost.setUserId(userId);
            userPost.setPostId(Long.parseLong(leadTeacherId));
            sysUserPostService.save(userPost);
        }
    }

    /**
     * 检查老师是否存在
     *
     * @param userId 用户id
     */
    private void checkLecturerExists(Long userId) {
        LecturerInfo existingLecturer = lecturerInfoMapper.selectOne(Wrappers.<LecturerInfo>lambdaQuery()
                .eq(LecturerInfo::getUserId, userId));
        if (ObjectUtil.isNotNull(existingLecturer)) {
            throw new BizException("该老师已存在");
        }
    }

    /**
     * 检查用户是否存在
     *
     * @param userId 用户id
     */
    private void checkUserExists(Long userId) {
        SysUser user = sysUserService.getOne(Wrappers.<SysUser>lambdaQuery()
                .eq(SysUser::getUserId, userId));

        if (ObjectUtil.isNull(user)) {
            throw new BizException("没有该用户");
        }
    }

    /**
     * 将LecturerInfo对象转换为LecturerInfoVO对象
     *
     * @param lecturer 对象
     * @param userMap  用户信息
     * @return LecturerInfoVO对象
     */
    private LecturerInfoVO mapToLecturerInfoVO(LecturerInfo lecturer, Map<Long, SysUser> userMap) {
        LecturerInfoVO lecturerInfoVO = BeanUtil.copyProperties(lecturer, LecturerInfoVO.class);
        SysUser user = userMap.get(lecturer.getUserId());
        Optional.ofNullable(user).ifPresent(u -> {
            lecturerInfoVO.setName(u.getName());
            lecturerInfoVO.setNickname(u.getNickname());
        });
        return lecturerInfoVO;
    }

    /**
     * 根据名称模糊查询用户id
     *
     * @param name 名称
     * @return Set<Long>
     */
    private Set<Long> fetchUserIds(String name) {
        if (name == null) {
            // 由于需要3种查询（查全部、按条件查、不查）而集合就两种状态（有值、空值），所以这里返回null
            return null;
        }

        List<SysUser> list = sysUserService.list(Wrappers.<SysUser>lambdaQuery()
                .like(SysUser::getName, name)
                .or().like(SysUser::getNickname, name)
                .select(SysUser::getUserId));

        return list.stream().map(SysUser::getUserId).collect(Collectors.toSet());
    }

    /**
     * 获取主讲老师属性表VO
     *
     * @param records 主讲老师属性表集合
     * @return 主讲老师属性表VO集合
     */
    private List<LecturerInfoVO> convertLecturerInfos(List<LecturerInfo> records) {
        if (records.isEmpty()) {
            return List.of();
        }

        Set<Long> userIds = records.stream().map(LecturerInfo::getUserId).collect(Collectors.toSet());
        Map<Long, SysUser> userMap = sysUserService.listByIds(userIds)
                .stream().collect(Collectors.toMap(SysUser::getUserId, e -> e));
        return records.stream()
                .map(s -> mapToLecturerInfoVO(s, userMap))
                .toList();
    }

    /**
     * 创建主讲老师属性表VO分页对象
     *
     * @param lecturerInfoPage   主讲老师属性表分页对象
     * @param lecturerInfoVoList 主讲老师属性表VO集合
     * @return Page<LecturerInfoVO>
     */
    private Page<LecturerInfoVO> createLecturerInfoVoPage(Page<LecturerInfo> lecturerInfoPage, List<LecturerInfoVO> lecturerInfoVoList) {
        Page<LecturerInfoVO> lecturerInfoVoPage = new Page<>(lecturerInfoPage.getCurrent(), lecturerInfoPage.getSize(), lecturerInfoPage.getTotal());
        lecturerInfoVoPage.setRecords(lecturerInfoVoList);

        return lecturerInfoVoPage;
    }

    /**
     * 获取主讲老师属性表分页
     *
     * @param page 分页对象
     * @param ids  主讲老师属性表id集合
     * @return R  分页对象
     */
    private Page<LecturerInfo> fetchLecturerInfoPage(Page<?> page, Set<Long> ids) {
        Page<LecturerInfo> lecturerInfoPage = new Page<>(page.getCurrent(), page.getSize());
        LambdaQueryWrapper<LecturerInfo> wrapper = Wrappers.<LecturerInfo>lambdaQuery()
                .orderByAsc(LecturerInfo::getStatus)
                .orderByDesc(LecturerInfo::getUpdateTime);
        if (ids == null) {
            lecturerInfoPage = lecturerInfoMapper.selectPage(lecturerInfoPage, wrapper);
        } else if (CollUtil.isNotEmpty(ids)) {
            lecturerInfoPage = lecturerInfoMapper.selectPage(lecturerInfoPage, wrapper.in(LecturerInfo::getUserId, ids));
        }
        return lecturerInfoPage;
    }


    /**
     * 表为空的时候自动插入数据
     */
    private void initLecturerData() {
        Long count = lecturerInfoMapper.selectCount(null);

        if (count != 0) {
            return;
        }

        List<SysUserVO> list = leadTeacherService.list();

        list.forEach(s -> {
            LecturerInfo lecturer = LecturerInfo.builder()
                    .userId(s.getUserId())
                    .xgjLecturerId("-1")
                    .status(LeadTeacherConstant.LECTURER_INFO_STATUS_ENABLE)
                    .build();
            lecturerInfoMapper.insert(lecturer);
        });
    }
}