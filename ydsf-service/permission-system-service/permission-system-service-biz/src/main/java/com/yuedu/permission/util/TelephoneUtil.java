package com.yuedu.permission.util;

import lombok.extern.slf4j.Slf4j;
import me.chanjar.weixin.cp.bean.WxCpUser;
import org.apache.commons.lang3.StringUtils;

/**
 * 手机号工具类
 *
 * <AUTHOR>
 * @date 2024/09/26
 **/
@Slf4j
public class TelephoneUtil {

    /**
     * 获取手机号
     *
     * @param cpUser 企微用户数据
     * @return 手机号
     */
    public static String getPhoneFromWxCpUser(WxCpUser cpUser) {
        // 初始化手机号变量
        String phone = "";
        // 尝试获取用户扩展属性中的文本值
        String textValue = null;
        try {
            textValue = cpUser.getExtAttrs().get(0).getTextValue();
        } catch (RuntimeException e) {
            log.warn("下标索引错误");
        }

        // 优先使用用户电话号码作为手机值，如果未设置则使用扩展属性中的文本值
        if (StringUtils.isNotBlank(cpUser.getTelephone())) {
            phone = cpUser.getTelephone();
        } else if (StringUtils.isNotBlank(textValue)) {
            phone = cpUser.getExtAttrs().get(0).getTextValue();
        }
        return phone;
    }
}
