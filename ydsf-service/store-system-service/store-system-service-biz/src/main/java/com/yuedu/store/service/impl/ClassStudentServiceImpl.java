package com.yuedu.store.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.yulichang.wrapper.MPJLambdaWrapper;
import com.yuedu.store.constant.enums.ClassStatusEnum;
import com.yuedu.store.constant.enums.ClassStudentEnum;
import com.yuedu.store.constant.enums.StudentStatusEnum;
import com.yuedu.store.dto.ClassStudentDTO;
import com.yuedu.store.dto.ClassStudentMemberDTO;
import com.yuedu.store.entity.Class;
import com.yuedu.store.entity.ClassStudent;
import com.yuedu.store.entity.Student;
import com.yuedu.store.entity.ft.Member;
import com.yuedu.store.mapper.ClassMapper;
import com.yuedu.store.mapper.ClassStudentMapper;
import com.yuedu.store.mapper.StudentMapper;
import com.yuedu.store.mapper.ft.MemberMapper;
import com.yuedu.store.service.ClassStudentService;
import com.yuedu.store.vo.ClassStudentVO;
import com.yuedu.ydsf.common.core.exception.BizException;
import com.yuedu.ydsf.eduConnect.jw.api.feign.RemoteTimetableChangeService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 学生 服务类
 *
 * <AUTHOR>
 * @date 2025-01-03 09:24:05
 */
@Slf4j
@Service
@AllArgsConstructor
public class ClassStudentServiceImpl extends ServiceImpl<ClassStudentMapper, ClassStudent> implements ClassStudentService {

    private final ClassStudentMapper classStudentMapper;
    private final StudentMapper studentMapper;
    private final ClassMapper classMapper;
    private final RemoteTimetableChangeService remoteTimetableChangeService;
    private final MemberMapper memberMapper;

    /**
     * 根据学员id获取班级列表
     *
     * @param studentId 学员id
     * @return 学生列表
     */
    @Override
    public List<ClassStudentVO> getClassList(Integer studentId) {

        return classStudentMapper.selectList(Wrappers.lambdaQuery(ClassStudent.class)
                        .eq(ClassStudent::getStudentId, studentId)
                        .eq(ClassStudent::getStatus, ClassStatusEnum.EFFECTIVE.getType()))
                .stream().map(student -> {
                    ClassStudentVO studentVO = new ClassStudentVO();
                    BeanUtil.copyProperties(student, studentVO);
                    return studentVO;
                }).toList();
    }

    /**
     * 新增学员
     *
     * @param classStudentDTO
     * @return Boolean
     */
    @Override
    public void saveStudent(ClassStudentDTO classStudentDTO) {
        String studentStr = classStudentDTO.getStudentId();

        // 转换为数组并去重
        Set<Integer> uniqueStudentIds = Arrays.stream(studentStr.split(","))
                .map(String::trim)
                .filter(s -> !s.isEmpty())
                .map(Integer::parseInt)
                .collect(Collectors.toCollection(HashSet::new));

        if (uniqueStudentIds.isEmpty()) {
            throw new BizException("学生ID数组为空");
        }

        // 提前查询班级信息和校管家ID
        Class classCont = classMapper.selectById(classStudentDTO.getClassId());
        if (classCont == null) {
            throw new BizException("班级ID不存在");
        }

        int batchSize = 1000;
        List<ClassStudent> classStudentsToSave = new ArrayList<>();
        List<Integer> classStudentsToUpdate = new ArrayList<>();
        List<Integer> studentIdList = new ArrayList<>(uniqueStudentIds);
        for (int i = 0; i < studentIdList.size(); i += batchSize) {
            int end = Math.min(i + batchSize, studentIdList.size());
            List<Integer> batchStudentIds = studentIdList.subList(i, end);

            // 批量查询学员信息
            List<Student> students = studentMapper.selectBatchIds(batchStudentIds);
            Map<Long, Student> studentMap = students.stream()
                    .collect(Collectors.toMap(Student::getUserId, student -> student));

            for (Integer studentId : batchStudentIds) {
                Student studentCont = studentMap.get(Long.valueOf(studentId));
                if (studentCont != null) {
                    List<ClassStudent> existingClassStudents = classStudentMapper.selectList(Wrappers.lambdaQuery(ClassStudent.class)
                            .eq(ClassStudent::getStudentId, studentId)
                            .eq(ClassStudent::getClassId, classStudentDTO.getClassId()));
                    if (existingClassStudents.isEmpty()) {
                        // 如果没有获取到学员班级关系，则新增
                        ClassStudent classCon = new ClassStudent();
                        BeanUtil.copyProperties(classStudentDTO, classCon);
                        classCon.setStatus(ClassStudentEnum.FORMAL.getCode());
                        classCon.setStudentId(studentId);
                        classStudentsToSave.add(classCon);
                    } else {
                        for (ClassStudent existingClassStudent : existingClassStudents) {
                            if (existingClassStudent.getStatus() == 0) {
                                classStudentsToUpdate.add(existingClassStudent.getId());
                            }
                        }
                    }
                }
            }
            // 批量插入
            if (!classStudentsToSave.isEmpty()) {
                classStudentMapper.insert(classStudentsToSave);
                classStudentsToSave.clear();
            }
            // 批量更新
            if (!classStudentsToUpdate.isEmpty()) {
                this.update(Wrappers.lambdaUpdate(ClassStudent.class)
                        .eq(ClassStudent::getStoreId, classStudentDTO.getStoreId())
                        .in(ClassStudent::getId, classStudentsToUpdate)
                        .set(ClassStudent::getStatus, ClassStudentEnum.FORMAL.getCode())
                        .set(ClassStudent::getChangeClass, ClassStudentEnum.UNCHANGE.getCode())
                );
                classStudentsToUpdate.clear();
            }
        }
    }


    /**
     * 移除学员
     *
     * @param
     */
    @Override
    public void removeStudent(ClassStudentDTO classStudentDTO) {
        List<ClassStudent> classStudent = classStudentMapper.selectList(Wrappers.lambdaQuery(ClassStudent.class)
                .eq(ClassStudent::getStudentId, classStudentDTO.getStuId())
                .eq(ClassStudent::getClassId, classStudentDTO.getClassId()));
        if (classStudent.isEmpty()) {
            throw new BizException("学员不存在");
        }
        this.update(Wrappers.lambdaUpdate(ClassStudent.class)
                .eq(ClassStudent::getStudentId, classStudentDTO.getStuId())
                .eq(ClassStudent::getClassId, classStudentDTO.getClassId())
                .set(ClassStudent::getStatus, ClassStudentEnum.REMOVE.getCode()));
        //清空未开始的调课课程
        remoteTimetableChangeService.missedClassRescheduleClear(Long.valueOf(classStudentDTO.getStuId()), classStudentDTO.getStoreId());
    }

    /**
     * 学员转班
     *
     * @param
     */
    @Override
    public void changeClass(ClassStudentDTO classStudentDTO) {
        List<ClassStudent> classStudent = classStudentMapper.selectList(Wrappers.lambdaQuery(ClassStudent.class)
                .eq(ClassStudent::getStudentId, classStudentDTO.getStuId())
                .eq(ClassStudent::getClassId, classStudentDTO.getClassId())
                .eq(ClassStudent::getStatus, ClassStudentEnum.FORMAL.getCode()));
        if (classStudent.isEmpty()) {
            throw new BizException("学员不存在");
        }
        List<ClassStudent> classStudentNew = classStudentMapper.selectList(Wrappers.lambdaQuery(ClassStudent.class)
                .eq(ClassStudent::getStudentId, classStudentDTO.getStuId())
                .eq(ClassStudent::getClassId, classStudentDTO.getNewClassId())
                .eq(ClassStudent::getStatus, ClassStudentEnum.FORMAL.getCode()));
        if (!classStudentNew.isEmpty()) {
            throw new BizException("学员已存在");
        }
        this.update(Wrappers.lambdaUpdate(ClassStudent.class)
                .eq(ClassStudent::getStudentId, classStudentDTO.getStuId())
                .eq(ClassStudent::getClassId, classStudentDTO.getClassId())
                .set(ClassStudent::getStatus, ClassStudentEnum.REMOVE.getCode()));

        ClassStudent classCon = new ClassStudent();
        classCon.setStudentId(classStudentDTO.getStuId());
        classCon.setSchoolId(Math.toIntExact(classStudentDTO.getSchoolId()));
        classCon.setClassId(Math.toIntExact(classStudentDTO.getNewClassId()));
        classCon.setStatus(ClassStudentEnum.FORMAL.getCode());
        classCon.setStoreId(Math.toIntExact(classStudentDTO.getStoreId()));
        classCon.setChangeClass(ClassStudentEnum.CHANGECLASS.getCode());
        //清空未开始的调课课程
        remoteTimetableChangeService.missedClassRescheduleClear(Long.valueOf(classStudentDTO.getStuId()), classStudentDTO.getStoreId());
        this.save(classCon);

    }

    @Override
    public List<ClassStudentMemberDTO> getStudentListByClassIdList(List<Integer> classIds) {
        if (classIds == null || classIds.isEmpty()) {
            return Collections.emptyList();
        }

        MPJLambdaWrapper<ClassStudent> mpjLambdaWrapper = new MPJLambdaWrapper<ClassStudent>()
                .selectAll(ClassStudent.class)
                .select(Student::getName, Student::getSchStudentId)
                .innerJoin(Student.class, Student::getUserId, ClassStudent::getStudentId)
                .in(ClassStudent::getClassId, classIds)
                .eq(ClassStudent::getStatus, ClassStatusEnum.EFFECTIVE.getType())
                .eq(ClassStudent::getStatus, ClassStudentEnum.FORMAL.getCode())
                .ne(Student::getStatus, StudentStatusEnum.LEAVE.getCode())
                .isNotNull(Student::getSchStudentId);

        List<ClassStudentMemberDTO> classStudentMemberDTOList = classStudentMapper
                .selectJoinList(ClassStudentMemberDTO.class, mpjLambdaWrapper);

        if (classStudentMemberDTOList.isEmpty()) {
            return Collections.emptyList();
        }
        // 获取所有学员的校管家ID
        List<String> schStudentIds = classStudentMemberDTOList.stream()
                .map(ClassStudentMemberDTO::getSchStudentId)
                .distinct()
                .toList();
        // 查询所有学员的Member信息
        Map<String, Member> memberMap = memberMapper.selectList(Wrappers.lambdaQuery(Member.class)
                        .in(Member::getSchStudentId, schStudentIds))
                .stream()
                .collect(Collectors.toMap(Member::getSchStudentId, Function.identity(), (e1, e2) -> e1));
        classStudentMemberDTOList.forEach(classStudentMemberDTO -> {
            Member member = memberMap.get(classStudentMemberDTO.getSchStudentId());
            if (member != null) {
                classStudentMemberDTO.setPublicOpenId(member.getPublicOpenId());
            }
        });
        return classStudentMemberDTOList;
    }
}
