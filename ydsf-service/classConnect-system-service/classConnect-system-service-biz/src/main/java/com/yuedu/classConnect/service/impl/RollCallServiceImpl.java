package com.yuedu.classConnect.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yuedu.classConnect.dto.RollCallDTO;
import com.yuedu.classConnect.entity.RollCallEntity;
import com.yuedu.classConnect.mapper.RollCallMapper;
import com.yuedu.classConnect.service.RollCallService;
import com.yuedu.classConnect.vo.RollCallVO;
import com.yuedu.store.api.feign.RemoteStudentService;
import com.yuedu.store.vo.StudentVO;
import com.yuedu.ydsf.common.core.exception.BizException;
import com.yuedu.ydsf.common.core.util.R;
import com.yuedu.ydsf.common.security.util.SecurityUtils;
import com.yuedu.ydsf.eduConnect.jw.api.feign.RemoteClassTimeStudentsService;
import com.yuedu.ydsf.eduConnect.jw.api.vo.CheckInStudentVO;
import com.yuedu.ydsf.eduConnect.live.api.dto.SsCourseStepDTO;
import com.yuedu.ydsf.eduConnect.live.api.dto.SsInteractionSettingDTO;
import com.yuedu.ydsf.eduConnect.live.api.feign.RemoteInteractionService;
import com.yuedu.ydsf.common.core.util.PinyinComparatorUtil;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 点名表
 *
 * <AUTHOR>
 * @date 2025-01-06 10:51:49
 */
@Slf4j
@Service
@AllArgsConstructor
public class RollCallServiceImpl extends ServiceImpl<RollCallMapper, RollCallEntity> implements RollCallService {
    private final RemoteInteractionService remoteInteractionService;
    private final RemoteClassTimeStudentsService remoteClassTimeStudentsService;
    private final RemoteStudentService remoteStudentService;
    /**
     * 查询学员列表
     *
     * @return List<RollCallVO>
     */
    @Override
    public List<RollCallVO> getList(Integer lessonNo, Integer classId,Long storeId) {
        //调取当前出勤学员列表（已绑定答题器的学生）
        R<List<CheckInStudentVO.StudentCheckInInfoVO>> classResult = remoteClassTimeStudentsService.getCheckedInStudentsByLessonNo(Long.valueOf(lessonNo), storeId);
        List<CheckInStudentVO.StudentCheckInInfoVO> classList = classResult.getData();
        if (classList == null) {
            classList = Collections.emptyList();
        }

        // 查询已点名的学生记录
        List<RollCallEntity> rollCallEntities = baseMapper.selectList(Wrappers.lambdaQuery(RollCallEntity.class)
                .eq(RollCallEntity::getLessonNo, lessonNo));

        // 统计每个学生的点名次数
        Map<Long, Integer> userIdCountMap = new HashMap<>();
        for (RollCallEntity rollCall : rollCallEntities) {
            Long userId = rollCall.getUserId();
            userIdCountMap.put(userId, userIdCountMap.getOrDefault(userId, 0) + 1);
        }

        // 获取所有已点名学生的ID列表（去重）
        List<Long> rolledStudentIds = rollCallEntities.stream()
                .map(RollCallEntity::getUserId)
                .distinct()
                .collect(Collectors.toList());

        // 获取已点名学生的详细信息
        Map<Long, StudentVO> rolledStudentMap = new HashMap<>();
        if (!rolledStudentIds.isEmpty()) {
            R<List<StudentVO>> studentResult = remoteStudentService.getStudentListByIds(rolledStudentIds);
            if (studentResult.isOk() && studentResult.getData() != null) {
                rolledStudentMap = studentResult.getData().stream()
                        .collect(Collectors.toMap(StudentVO::getUserId, student -> student));
            }
        }

        return rollList(classList, userIdCountMap, rolledStudentMap);
    }

    /**
     * 合并获取点名列表
     *
     * @param classList 当前绑定答题器的学生列表
     * @param userIdCountMap 学生点名次数统计
     * @param rolledStudentMap 已点名学生信息映射
     * @return
     */
    public List<RollCallVO> rollList(List<CheckInStudentVO.StudentCheckInInfoVO> classList,
                                   Map<Long, Integer> userIdCountMap,
                                   Map<Long, StudentVO> rolledStudentMap) {
        List<RollCallVO> rollCallVos = new ArrayList<>();
        Set<Long> processedStudentIds = new HashSet<>();

        // 1. 处理当前绑定答题器的学生
        for (CheckInStudentVO.StudentCheckInInfoVO student : classList) {
            RollCallVO rollCallVO = new RollCallVO();
            Long userId = student.getStudentId();
            rollCallVO.setUserId(userId);
            rollCallVO.setName(student.getStudentName());
            rollCallVO.setPinyinPre(student.getPinyinPre());

            // 设置点名次数
            int num = userIdCountMap.getOrDefault(userId, 0);
            rollCallVO.setNum(num);

            rollCallVos.add(rollCallVO);
            processedStudentIds.add(userId);
        }

        // 2. 处理已点名但当前未绑定答题器的学生
        for (Map.Entry<Long, StudentVO> entry : rolledStudentMap.entrySet()) {
            Long userId = entry.getKey();
            StudentVO student = entry.getValue();

            // 如果该学生还没有被处理过（即当前未绑定答题器）
            if (!processedStudentIds.contains(userId)) {
                RollCallVO rollCallVO = new RollCallVO();
                rollCallVO.setUserId(userId);
                rollCallVO.setName(student.getName());
                rollCallVO.setPinyinPre(student.getPinyinPre());

                // 设置点名次数
                int num = userIdCountMap.getOrDefault(userId, 0);
                rollCallVO.setNum(num);

                rollCallVos.add(rollCallVO);
            }
        }

        rollCallVos.sort(Comparator.nullsLast(Comparator.comparing(RollCallVO::getPinyinPre,
                        Comparator.nullsLast(PinyinComparatorUtil.getInstance())))
                .thenComparing(rollCallVO -> rollCallVO != null ? rollCallVO.getUserId() : null));

        return rollCallVos;
    }

    /**
     * 新增教学页模板
     *
     * @param rollCallDTO 新增教学页DTO
     * @return Boolean
     */
    @Override
    public Boolean saveDetail(RollCallDTO rollCallDTO) {
        //发送消息给教室端
        SsCourseStepDTO ssCourseStepDTO = new SsCourseStepDTO();
        ssCourseStepDTO.setAttendClassType(rollCallDTO.getAttendClassType());
        if (rollCallDTO.getAttendClassType() == 0) {
            ssCourseStepDTO.setRoomUUID(rollCallDTO.getRoomUUID());
        }
        SsInteractionSettingDTO propertiesDTO = new SsInteractionSettingDTO();
        propertiesDTO.setStudentId(String.valueOf(rollCallDTO.getUserId()));
        propertiesDTO.setStudentName(rollCallDTO.getUserName());
        propertiesDTO.setCreator(SecurityUtils.getUser().getUsername());
        propertiesDTO.setCreatorId(SecurityUtils.getUser().getId());
        propertiesDTO.setCtime(LocalDateTime.now());
        ssCourseStepDTO.setProperties(propertiesDTO);
        ssCourseStepDTO.setTimeTableId(rollCallDTO.getTimeTableId());
        RollCallEntity rollCallEntity = new RollCallEntity();
        BeanUtil.copyProperties(rollCallDTO, rollCallEntity);

        R response = remoteInteractionService.rollCall(ssCourseStepDTO);
        if (response.isOk()) {
            save(rollCallEntity);
        } else {
            throw new BizException(response.getMsg());
        }
        return Boolean.TRUE;
    }

}
