package com.yuedu.ydsf.eduConnect.system.proxy.entity;

import lombok.Data;

/**
 * 开始录制响应
 *
 * @author: KL
 * @date: 2024/09/28
 **/
@Data
public class StartResp extends AgoraRecordResp {

    /**
     * 录制服务所在频道的名称
     */
    private String cname;
    /**
     * RTC 频道内使用的 UID
     */
    private String uid;
    /**
     * 云端录制资源 Resource ID
     */
    private String resourceId;
    /**
     * 录制 ID
     */
    private String sid;
}
