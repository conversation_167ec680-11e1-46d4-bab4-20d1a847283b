package com.yuedu.ydsf.eduConnect.system.proxy.dto;

import lombok.Data;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;

/**
 * @author: zhangchu<PERSON>fu
 * @date: 2024/10/11
 **/
@Data
public class CreateAgoraClassRoomDTO {
    /**
     * 声网UUID
     */
    private String roomUuid;

    /**
     * 班级ID
     */
    private Long classId;

    /**
     * 排课ID
     */
    private Long courseScheduleId;

    /**
     * 排课书籍ID
     */
    private Long courseScheduleBooksId;

    /**
     * 排课规则ID
     */
    private Long courseScheduleRuleId;

    /**
     * 上课日期（yyyy-MM-dd）
     */
    private LocalDate attendClassDate;

    /**
     * 上课开始时间（HH:mm）
     */
    private LocalTime attendClassStartTime;

    /**
     * 上课结束时间（HH:mm）
     */
    private LocalTime attendClassEndTime;

    /**
     * 是否已同步声网创建课堂: 0-否; 1-是;
     */
    private Integer isSyncAgora;

    /**
     * 上课类型: 0-直播课; 1-点播课;
     */
    private Integer attendClassType;

    /**
     * 监课链接url路径
     */
    private String supervisionClassUrl;

    /**
     * 监课开始时间(yyyy-MM-dd HH:mm:ss）
     */
    private LocalDateTime supervisionClassStartTime;

    /**
     * 监课结束时间(yyyy-MM-dd HH:mm:ss）
     */
    private LocalDateTime supervisionClassEndTime;

    /**
     * 主讲老师ID(ss_lecturer主键ID)
     */
    private Long lecturerId;

    /**
     * 主讲设备ID
     */
    private Long deviceId;

    /**
     * 主讲教室ID
     */
    private Long classRoomId;

    /**
     * 书籍ID
     */
    private String booksId;

    /**
     * 书籍名称
     */
    private String booksName;

    /**
     * 课程库ID(录播课资源ID)
     */
    private Long recordingId;

    /**
     * 主讲端上课码(上课端标识1 + 5位随机数  例:115329)
     */
    private String lecturerRoomCode;

    /**
     * 教室端上课码(教室端标识2 + 5位随机数  例:235329)
     */
    private String classRoomCode;
}
