package com.yuedu.ydsf.eduConnect.system.proxy.entity;

import lombok.Data;

import java.util.List;

/**
 * 录制上传
 *
 * @author: KL
 * @date: 2024/09/30
 **/
@Data
public class RecordFileUpload {

    private String cname;

    private String uid;

    private String sid;

    private Integer sequence;

    private Long sendts;

    private Integer serviceType;

    private UploadDetails details;

    @Data
    public static class UploadDetails {

        private String msgName;

        /**
         * Number 类型，事件状态，0 表示正常，其他值表示异常。
         */
        private Integer status;

        private List<UploadedFile> fileList;

        public boolean isSuccess() {
            return status != null && status == 0;
        }

        @Data
        public static class UploadedFile {
            private String fileName;

            private String trackType;

            private String uid;

            private boolean mixedAllUser;

            private boolean isPlayable;

            private long sliceStartTime;
        }
    }
}
