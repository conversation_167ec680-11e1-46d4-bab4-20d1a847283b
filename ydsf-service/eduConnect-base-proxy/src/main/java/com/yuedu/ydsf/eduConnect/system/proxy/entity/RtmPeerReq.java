package com.yuedu.ydsf.eduConnect.system.proxy.entity;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Builder;
import lombok.Data;

/**
 * TODO
 *
 * @author: KL
 * @date: 2024/11/06
 **/
@Data
@Builder
public class RtmPeerReq {

    /**
     * RTM 系统中接收点对点消息的用户 ID
     */
    private String destination;

    /**
     * 是否开启离线消息
     */
    @Builder.Default
    @JSONField(name = "enable_offline_messaging")
    private Boolean enableOfflineMessaging = false;

    /**
     * 是否保存为历史消息
     */
    @Builder.Default
    @JSONField(name = "enable_historical_messaging")
    private Boolean enableHistoricalMessaging = false;

    /**
     * 对点消息内容。不能为空字符串，长度最大 32 KB。
     */
    private String payload;
}
