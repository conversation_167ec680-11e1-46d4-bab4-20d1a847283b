package com.yuedu.ydsf.eduConnect.system.proxy.constant;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 校管家推送事件枚举类
 *
 * <AUTHOR> href="https://www.6yi.plus">刘艺</a>
 * @date 2023/2/21 9:20
 * @project
 * @Title: XiaogjEnum.java
 **/
@Getter
@AllArgsConstructor
public enum XiaogjPushEventEnum {

    // eventId为班级ID
    CUS_CLASS_CREATE("edu_class_create", "新增班级"),
    CUS_CLASS_MODIFY("edu_class_modify", "更新班级"),
    CUS_CLASS_REMOVE("edu_class_remove", "删除班级"),
    CUS_CLASS_FINISH("edu_class_finish", "班级结业"),
    EDU_CLASS_STUDENT_MODIFY("edu_class_student_modify", "班级学员变更"),

    // eventId为排课记录ID
    EDU_COURSE_CREATE("edu_course_create", "新增排课"),
    EDU_COURSE_MODIFY("edu_course_modify", "更新排课"),
    EDU_COURSE_REMOVE("edu_course_remove", "删除排课"),
    EDU_COURSE_CANCEL("edu_course_cancel", "取消排课"),
    EDU_COURSE_ATTEND("edu_course_attend", "点名上课"),
    EDU_COURSE_ATTEND_CANCEL("edu_course_attendcancel", "撤销点名"),
    EDU_COURSE_STUDENT_MODIFY("edu_course_student_modify", "排课学员变更"),

    // eventId为正式学员ID
    USER_STUDENT_CREATE("user_student_create", "新增学员"),
    USER_STUDENT_MODIFY("user_student_modify","更新学员"),
    USER_STUDENT_REMOVE("user_student_remove","删除学员"),

    CUS_CUSTOMER_CREATE("cus_customer_create", "新增意向客户"),

    BASE_DEPT_CREATE("base_dept_create","新增部门"),
    BASE_DEPT_MODIFY("base_dept_modify","更新部门"),
    BASE_DEPT_REMOVE("base_dept_remove","删除部门"),

    ORG_EMPLOYEE_CREATE("org_employee_create","新增员工"),
    ORG_EMPLOYEE_MODIFY("org_employee_modify","更新员工"),
    ORG_EMPLOYEE_REMOVE("org_employee_remove","删除员工");






    public static final String EDU_CLASS_CREATE = "edu_class_create";
    private final String type;

    private final String desc;

}
