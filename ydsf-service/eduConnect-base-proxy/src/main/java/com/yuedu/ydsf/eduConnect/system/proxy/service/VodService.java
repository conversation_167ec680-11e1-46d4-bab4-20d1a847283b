package com.yuedu.ydsf.eduConnect.system.proxy.service;

import com.aliyun.vod20170321.models.*;
import com.yuedu.ydsf.eduConnect.system.proxy.dto.RegisterMetaDataDTO;

import java.util.List;

/**
 * 阿里云vod 实现
 *
 * @author: KL
 * @date: 2024/09/27
 **/
public interface VodService {

   /**
    *  获取上传凭证
    *
    * <AUTHOR>
    * @date 2024年09月27日 13时28分
    * @param  fileName 文件名称
    * @param title 标题
    */
   CreateUploadVideoResponseBody getUploadAuth(String fileName, String title) throws Exception;



    /**
     *  获取播放凭证
     *
     * <AUTHOR>
     * @date 2024年09月27日 13时28分
     * @param  videoId  视频Id
     * @param expiredTime 有效期
     */
    GetVideoPlayAuthResponseBody getVideoPlayAuth(String videoId, Long expiredTime) throws Exception;


    /**
     *  刷新获取上传凭证
     *
     * <AUTHOR>
     * @date 2024年09月27日 13时28分
     * @param  videoId 视频Id
     */
    RefreshUploadVideoResponseBody refreshUploadAuth(String videoId) throws Exception;



    /**
     *  提交转码任务
     *
     * <AUTHOR>
     * @date 2024年10月11日 10时35分
     */
    SubmitTranscodeJobsResponseBody submitTranscodeTask(String videoId,String templateGroupId) throws Exception ;




    /**
     *  获取原始播放地址
     *
     * <AUTHOR>
     * @param videoId 视频Id
     */
    String getMezzanineInfo(String videoId) throws Exception;


  /**
   *  获取媒体列表
   *
   * <AUTHOR>
   * @param videoId 视频Id
   */
  List<String> getPlayInfo(String videoId) throws Exception;


    /**
     *  注册媒体列表
     *
     * <AUTHOR>
     * @date 2024年11月05日 15时09分
     */
    List<RegisterMetaDataDTO> registerMedia(List<RegisterMetaDataDTO> registerMetadatas);


    /**
     *  注册媒体列表
     *
     * <AUTHOR>
     * @date 2024年11月05日 15时09分
     */
    List<RegisterMetaDataDTO> registerMedia(List<RegisterMetaDataDTO> registerMetadatas,String templateGroupId);



    /**
     *  获取原始播放地址
     *
     * <AUTHOR>
     * @param videoId 视频Id
     */
    String getMezzanine(String videoId);
}
