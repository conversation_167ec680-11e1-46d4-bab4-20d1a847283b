package com.yuedu.ydsf.eduConnect.system.proxy.handler.interceptor;

import com.dtflys.forest.http.ForestRequest;
import com.dtflys.forest.interceptor.Interceptor;
import com.dtflys.forest.reflection.ForestMethod;
import com.dtflys.forest.utils.StringUtils;
import com.yuedu.ydsf.eduConnect.system.proxy.config.AgoraTokenGenerator;
import lombok.AllArgsConstructor;

/**
 * TODO
 *
 * @author: KL
 * @date: 2024/11/06
 **/
@AllArgsConstructor
public class AgoraRtmInterceptor implements Interceptor {

    private AgoraTokenGenerator agoraTokenGenerator;

    @Override
    public void onInvokeMethod(ForestRequest request, ForestMethod method, Object[] args) {

        if (StringUtils.isNotBlank(agoraTokenGenerator.getAgoraRtmHost())) {
            if (method.getMetaRequest().getUrl().contains("$appId")) {
                request.setUrl(agoraTokenGenerator.getAgoraRtmHost() + method.getMetaRequest().getUrl().replace("$appId", agoraTokenGenerator.getAgoraRtmAppId()), args);
            } else {
                request.setUrl(agoraTokenGenerator.getAgoraRtmHost() + method.getMetaRequest().getUrl(), args);
            }

        }
        request.addHeader("Authorization", String.format("Basic %s",agoraTokenGenerator.getAgoraBasicAuthorization()));
    }
}
