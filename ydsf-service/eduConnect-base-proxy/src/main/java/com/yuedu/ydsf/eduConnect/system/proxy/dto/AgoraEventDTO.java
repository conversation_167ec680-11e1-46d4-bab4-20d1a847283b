package com.yuedu.ydsf.eduConnect.system.proxy.dto;

import com.alibaba.fastjson.JSONObject;
import lombok.Data;

/**
 * T
 *
 * @author: KL
 * @date: 2024/09/30
 **/
@Data
public class AgoraEventDTO {

    /**
     * 通知ID
     */
    private String noticeID;

    /**
     * 产品ID
     */
    private Integer productId;

    /**
     * 事件类型
     */
    private Integer eventType;

    /**
     * 通知时间
     */
    private Long notifyMs;

    /**
     * 课堂 uuid
     */
    private String roomUuid;

    /**
     * 事件类型
     */
    private Integer cmd;

    /**
     * 事件序号
     */
    private Integer sequence;

    /**
     * 版本号
     */
    private Integer version;

    /**
     * 事件的具体数据
     */
    private JSONObject data;

    /**
     * 通知载体
     */
    private JSONObject payload;
}
