package com.yuedu.ydsf.eduConnect.system.proxy.util;

import cn.hutool.core.lang.UUID;
import com.dtflys.forest.utils.StringUtils;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.*;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

/**
 * 文件读取工具类
 */
public class FileUtil {

    //文件大小限制1M
    public static final Long FILE_SIZE_1_M = 1L * 1024 * 1024;
    //文件大小限制5M
    public static final Long FILE_SIZE_5_M = 5L * 1024 * 1024;

    /**
     * 读取文件内容，作为字符串返回
     */
    public static String readFileAsString(String filePath) throws IOException {
        File file = new File(filePath);
        if (!file.exists()) {
            throw new FileNotFoundException(filePath);
        } 

        if (file.length() > 1024 * 1024 * 1024) {
            throw new IOException("File is too large");
        } 

        StringBuilder sb = new StringBuilder((int) (file.length()));
        // 创建字节输入流  
        FileInputStream fis = new FileInputStream(filePath);  
        // 创建一个长度为10240的Buffer
        byte[] bbuf = new byte[10240];  
        // 用于保存实际读取的字节数  
        int hasRead = 0;  
        while ( (hasRead = fis.read(bbuf)) > 0 ) {  
            sb.append(new String(bbuf, 0, hasRead));  
        }  
        fis.close();  
        return sb.toString();
    }

    /**
     * 根据文件路径读取byte[] 数组
     */
    public static byte[] readFileByBytes(String filePath) throws IOException {
        File file = new File(filePath);
        if (!file.exists()) {
            throw new FileNotFoundException(filePath);
        } else {
            ByteArrayOutputStream bos = new ByteArrayOutputStream((int) file.length());
            BufferedInputStream in = null;

            try {
                in = new BufferedInputStream(new FileInputStream(file));
                short bufSize = 1024;
                byte[] buffer = new byte[bufSize];
                int len1;
                while (-1 != (len1 = in.read(buffer, 0, bufSize))) {
                    bos.write(buffer, 0, len1);
                }

                byte[] var7 = bos.toByteArray();
                return var7;
            } finally {
                try {
                    if (in != null) {
                        in.close();
                    }
                } catch (IOException var14) {
                    var14.printStackTrace();
                }

                bos.close();
            }
        }
    }



    /**
     * 检查文件是否超过最大大小
     *
     * <AUTHOR>
     * @date 2025年02月13日 13时59分
     * @param len 文件大小
     * @param maxSize 最大文件大小
     * @param unit 单位 （B,K,M,G）
     */
    public static boolean checkFileSize(Long len, Long maxSize,String unit){
        double fileSize = 0;
        if ("B".equalsIgnoreCase(unit)) {
            fileSize = (double) len;
        } else if ("K".equalsIgnoreCase(unit)) {
            fileSize = (double) len / 1024;
        } else if ("M".equalsIgnoreCase(unit)) {
            fileSize = (double) len / 1048576;
        } else if ("G".equalsIgnoreCase(unit)) {
            fileSize = (double) len / 1073741824;
        }
        return !(fileSize > maxSize);
    }


    /**
     * 检查文件是否超过最大大小
     *
     * <AUTHOR>
     * @date 2025年02月13日 13时59分
     * @param fileSize 文件大小
     * @param maxSize 最大文件大小
     */
    public static boolean checkFileSize(Long fileSize, Long maxSize){
        return !(fileSize > maxSize);
    }


    /**
     * 检查文件类型
     *
     * <AUTHOR>
     * @date 2025年02月13日 13时59分
     * @param fileName 文件名
     * @param fileTypes 文件类型
     */
    public static boolean checkFileType(String fileName,String[] fileTypes){
        for (String fileType : fileTypes) {
            if (fileName.endsWith(fileType)) {
                return true;
            }
        }
        return false;
    }

    /**
     * 根据日期生成文件名
     *
     * <AUTHOR>
     * @date 2025年02月13日 13时59分
     * @param prefix 时间之前路径
     * @param middle 时间之后路径
     * @param originalFilename 文件名
     */
    public static String generateFileNameByDate(String prefix,String middle, String originalFilename) {
       StringBuilder sb = new StringBuilder();
       if(StringUtils.isNotBlank(prefix)){
           sb.append(prefix).append("/");
       }

       sb.append(LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd"))).append("/");

       if(StringUtils.isNotBlank(middle)){
           sb.append(middle).append("/");
       }

       if(StringUtils.isNotBlank(originalFilename) && originalFilename.lastIndexOf(".") > 0){
           sb.append(UUID.randomUUID().toString().toLowerCase()).append(originalFilename.substring(originalFilename.lastIndexOf(".")));
       }

       return sb.toString();
    }
}
