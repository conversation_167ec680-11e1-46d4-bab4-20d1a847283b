package com.yuedu.ydsf.eduConnect.system.proxy.util;

import org.apache.commons.codec.digest.DigestUtils;

/**
 * 阿里云视频点播工具类
 *
 * @author: KL
 * @date: 2024/10/11
 **/
public class AliVodUtil {


    /**
     *  验证签名
     *
     * <AUTHOR>
     * @date 2024年10月11日 09时52分
     */
    public static boolean verifySign(String sign, String url, String timestamp, String secret) {
        return sign.equals(sign(url, timestamp, secret));
    }


    /**
     * 获得签名
     *
     * <AUTHOR>
     * @date 2024年10月11日 09时23分
     */
    public static String sign(String url, String timestamp, String secret) {
        return DigestUtils.md5Hex(String.format("%s|%s|%s", url, timestamp, secret).getBytes());
    }
}
