package com.yuedu.ydsf.eduConnect.system.proxy.entity;

import lombok.Data;

/**
 * 房间信息
 *
 * @author: KL
 * @date: 2024/09/29
 **/
@Data
public class RoomInfoResp extends AgoraEducationResp{

    /**
     * 房间名
     */
    private String roomName;
    /**
     * 房间ID
     */
    private String roomUuid;

    /**
     * 房间属性
     */
    private RoomProperties roomProperties;


    @Data
    public static class RoomProperties{
        /**
         * 房间类型 必填
         * 0: 一对一
         * 2: 大班课
         * 4: 小班课
         */
        private Integer roomType;

        /**
         * 课程计划
         */
        private Schedule schedule;


        @Data
        public static class Schedule {
            /**
             * 开始时间，单位为毫秒
             */
            private Integer startTime;
            /**
             * 结束时间，单位为毫秒
             */
            private Integer endTime;
            /**
             * 房间状态 0: 未开始。1: 开始。 2: 结束。 3: 关闭。
             */
            private Integer state;
            /**
             * 关闭时间
             */
            private Integer closeTime;
        }

    }

}
