package com.yuedu.ydsf.eduConnect.system.proxy.entity;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

/**
 * TODO
 *
 * @author: KL
 * @date: 2024/11/06
 **/
@Data
public class RtmChannelResp {

    /**
     * 消息发送状态。
     * "message_sent": 消息已发送。
     */
    private String code;
    /**
     * 标识此次请求的唯一 ID。
     */
    @JSONField(name = "request_id")
    private String requestId;
    /**
     * 请求结果。
     * "success"：请求成功。
     * "failed"：请求失败。
     */
    private String result;

    public boolean isSuccess(){
        return "success".equals(result);
    }
}
