package com.yuedu.ydsf.eduConnect.system.proxy.util;

import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;

/**
 * 验签工具
 *
 * @author: KL
 * @date: 2024/09/30
 **/
public class SignUtils {


    /**
     * 验签
     */
    public  static boolean verifySign(String sign,String body, String secret) {
        return sign.equals(hmacSha1(body,secret));
    }

    /**
     * 将加密后的字节数组转换成字符串
     */
    public static String bytesToHex(byte[] bytes) {
        StringBuffer sb = new StringBuffer();
        for (int i = 0; i < bytes.length; i++) {
            String hex = Integer.toHexString(bytes[i] & 0xFF);
            if (hex.length() < 2) {
                sb.append(0);
            }
            sb.append(hex);
        }
        return sb.toString();
    }

    /**
     * HMAC/SHA256 加密，返回加密后的字符串
     */
    public static String hmacSha256(String message, String secret) {
        try {
            SecretKeySpec signingKey = new SecretKeySpec(secret.getBytes(
                    "utf-8"), "HmacSHA256");
            Mac mac = Mac.getInstance("HmacSHA256");
            mac.init(signingKey);
            byte[] rawHmac = mac.doFinal(message.getBytes("utf-8"));
            return bytesToHex(rawHmac);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    /**
     * HMAC/SHA1 加密，返回加密后的字符串
     */
    public static String hmacSha1(String message, String secret) {
        try {
            SecretKeySpec signingKey = new SecretKeySpec(secret.getBytes(
                    "utf-8"), "HmacSHA1");
            Mac mac = Mac.getInstance("HmacSHA1");
            mac.init(signingKey);
            byte[] rawHmac = mac.doFinal(message.getBytes("utf-8"));
            return bytesToHex(rawHmac);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }
}
