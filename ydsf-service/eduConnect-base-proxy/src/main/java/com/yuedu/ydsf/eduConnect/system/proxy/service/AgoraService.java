package com.yuedu.ydsf.eduConnect.system.proxy.service;

import com.yuedu.ydsf.eduConnect.system.proxy.dto.*;

import java.util.function.Function;

/**
 * 声网处理程序
 *
 * @author: KL
 * @date: 2024/09/30
 **/
public interface AgoraService {


    /**
     *  处理声网回调时间
     *
     * <AUTHOR>
     * @date 2024年09月30日 10时26分
     * @param agoraEventDTO
     */
    boolean handlerEvent(AgoraEventDTO agoraEventDTO, Function<AgoraUploadEventDTO,Boolean> function);


    /**
     *  验签
     *
     * <AUTHOR>
     * @date 2024年09月30日 15时30分
     */
    boolean verifySign(String body, String sign);

    /**
     *  创建声网课堂
     *
     * @param createAgoraClassRoomDTO 入参
     * @return 声网房间UUID
     */
    String syncAgoraClassRoom(CreateAgoraClassRoomDTO createAgoraClassRoomDTO);




    /**
     *  开启录制
     *
     * <AUTHOR>
     * @date 2024年10月21日 10时46分
     * @param   startRecordingDTO
     */
    void startRecording(StartRecordingDTO startRecordingDTO);


    /**
     *  结束录制
     *
     * <AUTHOR>
     * @date 2024年10月21日 10时46分
     */
    void stopRecording(StopRecordingDTO stopRecordingDTO);

    /**
     *  创建录制房间
     *
     * <AUTHOR>
     * @date 2024年11月01日 15时48分
     */
    void createRecordingRoom(CreateRecordingRoomDTO createRecordingRoomDTO);

    /**
     *  踢出房间用户
     *
     * <AUTHOR>
     * @date 2024年11月05日 10时49分
     */
    void removeUser(RemoveRoomUserDTO removeRoomUserDTO);

    /**
     * RTM 发送频道消息
     *
     * <AUTHOR>
     * @date 2024/11/7 9:14
     * @param publishChannelDTO
     * @return void
     */
    void publishChannelMessage(PublishChannelDTO publishChannelDTO);

    /**
     * RTM 发送点对点消息
     * <AUTHOR>
     * @date 2024/11/7 9:14
     * @param publishPeerDTO
     * @return void
     */
    void publishPeerMessage(PublishPeerDTO publishPeerDTO);

  /**
   * 声网更新自定义房间属性
   *
   * <AUTHOR>
   * @date 2024/11/13 8:58
   * @param eduUpdatePropertiesDTO
   * @return void
   */
  void eduUpdateRoomProperties(EduUpdateRoomPropertiesDTO eduUpdatePropertiesDTO);
}
