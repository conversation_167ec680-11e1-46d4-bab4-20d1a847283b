package com.yuedu.ydsf.eduConnect.system.proxy.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * 阿里回调事件
 *
 * @author: KL
 * @date: 2024/10/11
 **/
@Data
public class AliEventDTO {


    /**
     * 事件产生时间, 为UTC时间：yyyy-MM-ddTHH:mm:ssZ。
     */
    @JsonProperty("EventTime")
    private String eventTime;

    /**
     * 事件类型
     */
    @JsonProperty("EventType")
    private String eventType;

    /**
     * 视频ID
     */
    @JsonProperty("VideoId")
    private String videoId;

    /**
     * 处理状态，取值：
     * success（成功）。
     * fail（失败）
     */
    @JsonProperty("Status")
    private String status;

    /**
     * 在上传或提交作业接口中，指定UserData
     */
    @JsonProperty("Extend")
    private String extend;


    public boolean isSuccess(){
        return "success".equals(status);
    }
}
