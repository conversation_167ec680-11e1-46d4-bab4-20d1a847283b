package com.yuedu.ydsf.eduConnect.system.proxy.entity;

import lombok.Builder;
import lombok.Data;

/**
 * 点对点消息
 *
 * @author: KL
 * @date: 2024/09/29
 **/
@Data
@Builder
public class PeerMessageReq {
    /**
     * cmd
     */
    @Builder.Default
    private String cmd = "flexMsg";

    /**
     * 传输数据
     */
    private Object data;

    /**
     * 目标用户ID
     */
    private String toUserUuid;

    /**
     * 是否确认响应
     */
    @Builder.Default
    private Boolean ack = false;
}
