package com.yuedu.ydsf.eduConnect.system.proxy.handler.interceptor;

import com.dtflys.forest.http.ForestRequest;
import com.dtflys.forest.interceptor.Interceptor;
import com.dtflys.forest.reflection.ForestMethod;
import com.dtflys.forest.utils.StringUtils;
import com.yuedu.ydsf.eduConnect.system.proxy.config.AgoraTokenGenerator;
import lombok.AllArgsConstructor;

/**
 * 声网录制接口拦截器
 *
 * @author: KL
 * @date: 2024/09/28
 **/
@AllArgsConstructor
public class AgoraRecordInterceptor implements Interceptor {

    private AgoraTokenGenerator agoraTokenGenerator;

    @Override
    public void onInvokeMethod(ForestRequest request, ForestMethod method, Object[] args) {
        if (StringUtils.isNotBlank(agoraTokenGenerator.getHostUrl())) {
            if (method.getMetaRequest().getUrl().contains("$appId")) {
                request.setUrl(agoraTokenGenerator.getHostUrl() + method.getMetaRequest().getUrl().replace("$appId", agoraTokenGenerator.getAppId()), args);
            } else {
                request.setUrl(agoraTokenGenerator.getHostUrl() + method.getMetaRequest().getUrl(), args);
            }

        }

        request.addHeader("Authorization", String.format("Basic %s",agoraTokenGenerator.getAgoraBasicAuthorization()));
    }
}
