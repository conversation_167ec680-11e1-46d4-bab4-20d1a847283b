package com.yuedu.ydsf.eduConnect.system.proxy.entity;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * TODO
 *
 * @author: KL
 * @date: 2025/03/19
 **/
@Data
public class DetectResp {

    @J<PERSON><PERSON>ield(name = "error_code")
    private Long errorCode;

    @JSONField(name = "error_msg")
    private String errorMsg;

    @J<PERSON>NField(name = "log_id")
    private Long logId;

    private Long timestamp;

    private Integer cached;

    private FaceInfo result;

    @Data
    public static class FaceInfo {

        @J<PERSON><PERSON>ield(name = "face_num")
        private Integer faceNum;

        @J<PERSON>NField(name = "face_list")
        private List<Face> faceList;
    }

    @Data
    public static  class Face {

        @JSONField(name = "face_probability")
        private BigDecimal faceProbability;

        private BigDecimal age;
    }


    public boolean isSuccess() {
        return errorCode == 0;
    }
}
