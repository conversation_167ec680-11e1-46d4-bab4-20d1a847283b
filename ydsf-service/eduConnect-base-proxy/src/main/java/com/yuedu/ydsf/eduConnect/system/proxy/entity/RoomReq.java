package com.yuedu.ydsf.eduConnect.system.proxy.entity;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Builder;
import lombok.Data;

/**
 * 创建房间
 *
 * @author: KL
 * @date: 2024/09/29
 **/
@Data
@Builder
public class RoomReq {

    /**
     * 房间名称 必填
     */
    private String roomName;
    /**
     * 房间类型 必填
     * 0: 一对一
     * 2: 大班课
     * 4: 小班课
     */
    @Builder.Default
    private Integer roomType = 4;
    /**
     * 房间属性
     */
    private RoomProperties roomProperties;

    @Data
    @Builder
    public static class RoomProperties {
        /**
         * 课程计划
         */
        private Schedule schedule;
        /**
         * 申请邀请流程
         */
        private Processes processes;

        private RoleConfig roleConfig;

        @Data
        @Builder
        public static class Schedule {
            /**
             * 课堂开始时间戳，单位为毫秒
             */
            private Long startTime;
            /**
             * 课堂持续时长，单位为秒
             */
            @Builder.Default
            private Integer duration = 24 * 60 * 60;
            /**
             * 拖堂时长，单位为秒。
             */
            @Builder.Default
            private Integer closeDelay = 10 * 60;
        }

        @Data
        @Builder
        public static class Processes {
            /**
             * 上台设置
             */
            private HandsUp handsUp;

            @Data
            @Builder
            public static class HandsUp {
                /**
                 * 上台人数上限
                 */
                @Builder.Default
                private Integer maxAccept = 1;

                /**
                 * 默认上台角色
                 */
                @Builder.Default
                private String defaultAcceptRole="";
            }
        }

        @Data
        @Builder
        public static class RoleConfig {
            /**
             * 学生角色配置
             */
            @JSONField(name = "2")
            private Two two;

            @Data
            @Builder
            public static class Two {

                /**
                 * 学生默认流配置
                 */
                @JSONField(name = "defaultStream")
                private DefaultStream defaultStream;
            }

            @Data
            @Builder
            public static class DefaultStream {
                /**
                 * 学生默认流音频状态。 可设为：
                 * 0: 禁用
                 * 1: 启用
                 */
                @JSONField(name = "audioState")
                @Builder.Default
                private Integer audioState = 0;
            }

        }
    }
}
