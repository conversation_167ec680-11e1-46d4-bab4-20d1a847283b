package com.yuedu.ydsf.eduConnect.system.proxy.entity;

import lombok.Builder;
import lombok.Data;

/**
 * 移除用户
 *
 * @author: KL
 * @date: 2024/09/29
 **/
@Data
@Builder
public class RemoveUserReq {

    private Dirty dirty;

    @Data
    @Builder
    public static class Dirty {

        /**
         * state: Integer 型，踢人状态：
         * 1: 被踢，无法进入课堂。
         * 0: 未被踢，可以进入课堂。
         */
        @Builder.Default
        private Integer state = 1;

        /**
         * duration: Number 型，临时踢人持续时间，单位为秒。该字段仅在 state 为 1 时生效。从被踢出时开始计时，
         * 过了 duration 设置的时长后，用户自动恢复为未被踢状态。若不传该参数，则用户被永久踢出。
         */
        @Builder.Default
        private Long duration = 5L;

    }
}
