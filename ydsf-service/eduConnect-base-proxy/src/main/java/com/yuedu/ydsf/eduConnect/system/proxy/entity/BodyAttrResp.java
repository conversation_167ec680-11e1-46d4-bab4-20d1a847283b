package com.yuedu.ydsf.eduConnect.system.proxy.entity;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Builder;
import lombok.Data;

import java.lang.ref.PhantomReference;
import java.math.BigDecimal;
import java.util.List;

/**
 * TODO
 *
 * @author: KL
 * @date: 2025/02/07
 **/
@Data
public class BodyAttrResp {

    @JSONField(name = "error_code")
    private Long errorCode;

    @J<PERSON>NField(name = "error_msg")
    private String errorMsg;

    @J<PERSON><PERSON>ield(name = "log_id")
    private Long logId;

    @JSONField(name = "person_num")
    private Integer personNum;

    @JSONField(name = "person_info")
    private List<PersonInfo> personInfo;


    @Data
    public static class PersonInfo {

        private PersonAttributes attributes;
    }

    @Data
    public static class PersonAttributes{
       private PersonAge age;
        @J<PERSON><PERSON>ield(name = "is_human")
        private PersonHuman human;
    }

    @Data
    public static class PersonHuman{

        private BigDecimal score;

        private String name;
    }

    @Data
    public static class PersonAge{

        private BigDecimal score;

        private String name;
    }


    public boolean isSuccess(){
        return errorCode == null;
    }
}
