package com.yuedu.ydsf.eduConnect.system.proxy.handler;

import com.dtflys.forest.annotation.*;
import com.yuedu.ydsf.eduConnect.system.proxy.entity.*;
import com.yuedu.ydsf.eduConnect.system.proxy.handler.interceptor.AgoraEducationInterceptor;

/**
 * 灵动课堂接口
 *
 * @author: KL
 * @date: 2024/09/28
 **/
@BaseRequest(interceptor = AgoraEducationInterceptor.class)
public interface AgoraEducationApi {


    /**
     *  获取流信息
     *
     * <AUTHOR>
     * @date 2024年09月28日 18时03分
     * @param roomUuid 房间id
     * @param userId 用户id
     */
    @Get(url = "/cn/edu/apps/$appId/v2/rooms/{roomUuid}/users/{userId}")
    StreamInfoResp streamInfo(@Var("roomUuid") String roomUuid, @Var("userId") String userId);


    /**
     *  创建房间
     *
     * <AUTHOR>
     * @date 2024年09月29日 08时48分
     * @param roomUuid 房间id
     * @param req 请求参数
     */
    @Post(url = "/cn/edu/apps/$appId/v2/rooms/{roomUuid}")
    RoomResp createRoom(@Var("roomUuid") String roomUuid, @JSONBody RoomReq req);



    /**
     *  查询房间信息
     *
     * <AUTHOR>
     * @date 2024年09月29日 08时48分
     * @param roomUuid 房间id
     */
    @Get(url = "/cn/edu/apps/$appId/v2/rooms/{roomUuid}")
    RoomResp roomInfo(@Var("roomUuid") String roomUuid);



    /**
     *  更新课堂自定义属性
     *
     * <AUTHOR>
     * @date 2024年09月29日 08时48分
     * @param roomUuid 房间id
     * @param req 请求参数
     */
    @Put(url = "/cn/edu/apps/$appId/v2/rooms/{roomUuid}/properties")
    RoomPropertiesResp roomProperties(@Var("roomUuid") String roomUuid,@JSONBody RoomPropertiesReq req);


    /**
     *  删除课堂自定义属性
     *
     * <AUTHOR>
     * @date 2024年09月29日 08时48分
     * @param roomUuid 房间id
     * @param req 请求参数
     */
    @Delete(url = "/cn/edu/apps/$appId/v2/rooms/{roomUuid}/properties")
    DelRoomPropertiesResp delRoomProperties(@Var("roomUuid") String roomUuid,@JSONBody DelRoomPropertiesReq req);



    /**
     *  点对点发消息到指定用户
     *
     * <AUTHOR>
     * @date 2024年09月29日 08时48分
     * @param roomUuid 房间id
     * @param req 请求参数
     */
    @Post(url = "/cn/edu/apps/$appId/v2/rooms/{roomUuid}/messages/peer")
    PeerMessageResp peerMessage(@Var("roomUuid") String roomUuid, @JSONBody PeerMessageReq req);



    /**
     *  踢人
     *
     * <AUTHOR>
     * @date 2024年09月29日 08时48分
     * @param roomUuid 房间id
     * @param userId 用户id
     * @param req 请求参数
     */
    @Post(url = "/cn/edu/apps/$appId/v2/rooms/{roomUuid}/users/{userId}/exit")
    RemoveUserResp removeUser(@Var("roomUuid") String roomUuid,@Var("userId") String userId,@JSONBody RemoveUserReq req);

}
