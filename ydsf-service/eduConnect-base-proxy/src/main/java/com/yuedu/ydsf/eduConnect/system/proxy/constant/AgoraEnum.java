package com.yuedu.ydsf.eduConnect.system.proxy.constant;


import com.dtflys.forest.annotation.Get;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 声网枚举
 *
 * @author: KL
 * @date: 2024/09/29
 **/
@Getter
@AllArgsConstructor
public enum AgoraEnum {

    AGORA_RECORDING_MODE_INDIVIDUAL("individual","单流录制模式"),
    AGORA_RECORDING_MODE_MIX("mix","合流录制模式"),
    AGORA_RECORDING_MODE_WEB("web","页面录制模式"),


    AGORA_EVENT_TYPE_SUCCESS("Success","成功"),
    AGORA_EVENT_TYPE_FAILD("FAILD","失败")


    ;




    private  String type;

    private  String desc;



}
