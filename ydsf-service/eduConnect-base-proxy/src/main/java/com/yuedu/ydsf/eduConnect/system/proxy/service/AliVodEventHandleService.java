package com.yuedu.ydsf.eduConnect.system.proxy.service;

import com.yuedu.ydsf.eduConnect.system.proxy.dto.AliEventDTO;
import com.yuedu.ydsf.eduConnect.system.proxy.dto.AliVodTranscodeEventDTO;
import java.util.function.Function;

/**
 * TODO
 *
 * @author: KL
 * @date: 2024/10/11
 **/
public interface AliVodEventHandleService {

    /**
     *  回调验签
     *
     * <AUTHOR>
     * @date 2024年10月11日 09时11分
     */
    boolean verifySign(AliEventDTO eventDTO, String timestamp, String sign);


    /**
     *  处理回调事件
     *
     * <AUTHOR>
     * @date 2024年10月11日 09时46分
     */
    boolean handlerEvent(AliEventDTO eventDTO, Function<AliVodTranscodeEventDTO,Boolean> function);
}
