package com.yuedu.ydsf.eduConnect.system.proxy.entity;

import lombok.Data;

/**
 * 流信息
 *
 * @author: KL
 * @date: 2024/09/28
 **/
@Data
public class StreamInfoResp extends AgoraEducationResp{

    /**
     * 流信息
     */
    private StreamInfo data;

    @Data
    public static class StreamInfo {

        /**
         * 用户名
         */
        private String userName;

        /**
         * 用户UserId
         */
        private String userUuid;

        /**
         * 用户角色
         * 1: 老师
         * 2: 学生
         * 3: 助教
         */
        private String role;


        /**
         * 更新时间
         */
        private Long updateTime;

        /**
         * 流 ID
         */
        private String streamUuid;

        /**
         * 该用户是否在线：
         * 0：不在线
         * 1：在线
         */
        private Integer state;
    }
}
