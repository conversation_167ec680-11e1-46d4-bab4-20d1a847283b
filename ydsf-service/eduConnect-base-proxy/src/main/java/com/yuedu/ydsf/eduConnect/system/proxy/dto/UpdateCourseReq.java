package com.yuedu.ydsf.eduConnect.system.proxy.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * 保存校管家编辑排课类
 * <AUTHOR>
 * @date 2024年01月19日 09时57分
 */
@Data
public class UpdateCourseReq {


    /**
     * 三方班级ID
     */
    @JsonProperty("tripartiteId")
    private String tripartiteId;

    /**
     * 老师ID
     */
    @JsonProperty("cTeacherUserID")
    private String cTeacherUserID;

    /**
     * 排课记录id
     */
    @JsonProperty("threeId")
    private String threeId;

    /**
     * 排课记录开始时间 (yyyy-MM-dd HH:mm:ss)  【可不填 不填不修改时间】
     */
    @JsonProperty("cStartTime")
    private String cStartTime;

    /**
     * 排课记录结束时间(yyyy-MM-dd HH:mm:ss)
     */
    @JsonProperty("cEndTime")
    private String cEndTime;

    /**
     * 课程ID
     */
    @JsonProperty("cShiftID")
    private String cShiftID;

    /**
     * 房间ID
     */
    @JsonProperty("cClassroomID")
    private String cClassroomID;

    /**
     * 上课开始时间(HH:mm:ss)
     */
    @JsonProperty("cWeekStartTime")
    private String cWeekStartTime;

    /**
     * 上课结束时间(HH:mm:ss)
     */
    @JsonProperty("cWeekEndTime")
    private String cWeekEndTime;

    /**
     * 周几
     */
    @JsonProperty("cWeekday")
    private Integer cWeekday;

    /**
     * 上课日期
     */
    @JsonProperty("cDate")
    private String cDate;



}
