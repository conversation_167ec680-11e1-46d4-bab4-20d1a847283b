package com.yuedu.ydsf.eduConnect.system.proxy.constant;

import lombok.AllArgsConstructor;

/**
 * @author: z<PERSON><PERSON><PERSON><PERSON>
 * @date: 2024/10/14
 **/
@AllArgsConstructor
public enum AgoraRoomType {
    ROOM_TYPE_0(0, "一对一"),
    ROOM_TYPE_2(2, "大班课"),
    ROOM_TYPE_4(4, "小班课");

    private final int code;

    private final String message;

    public Integer code() {
        return code;
    }

    public String message() {
        return message;
    }
}
