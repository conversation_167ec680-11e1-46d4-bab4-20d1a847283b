package com.yuedu.ydsf.eduConnect.system.proxy.config;

import lombok.Builder;
import lombok.Data;

/**
 * 阿里token生成器
 *
 * @author: KL
 * @date: 2024/10/11
 **/
@Data
@Builder
public class AliTokenGenerator {

    /**
     * 阿里云vod accessKeyId
     */
    private String accessKeyId;

    /**
     * 阿里云vod accessKeySecret
     */
    private String accessKeySecret;

    /**
     * 域名
     */
    private String endpoint = "vod.cn-beijing.aliyuncs.com";

    /**
     * 回调地址
     */
    private String callbackUrl;

    /**
     * 模板组id
     */
    private String templateGroupId;

    /**
     * 回调秘钥
     */
    private String callbackSecret;
}
