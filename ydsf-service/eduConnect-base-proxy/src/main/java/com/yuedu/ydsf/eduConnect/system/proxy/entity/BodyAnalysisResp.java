package com.yuedu.ydsf.eduConnect.system.proxy.entity;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

/**
 * TODO
 *
 * @author: KL
 * @date: 2025/03/19
 **/
@Data
public class BodyAnalysisResp {

    @JSONField(name = "error_code")
    private Long errorCode;

    @JSONField(name = "error_msg")
    private String errorMsg;

    @JSONField(name = "log_id")
    private Long logId;

    @JSONField(name = "person_num")
    private Integer personNum;
}
