package com.yuedu.ydsf.eduConnect.system.proxy.util;


public class ChatTokenBuilder2 {

    /**
     * Build the CHAT user token.
     *
     * @param appId:          The App ID issued to you by Agora. Apply for a new App ID from
     *                        Agora Dashboard if it is missing from your kit. See Get an App ID.
     * @param appCertificate: Certificate of the application that you registered in
     *                        the Agora Dashboard. See Get an App Certificate.
     * @param userId:         The user's id, must be unique.
     *                        optionalUid must be unique.
     * @param expire:         represented by the number of seconds elapsed since now. If, for example, you want to access the
     *                        Agora Service within 10 minutes after the token is generated, set expireTimestamp as 600(seconds).
     * @return The RTC token.
     */
    public String buildUserToken(String appId,
                                 String appCertificate,
                                 String userId,
                                 int expire)
    {
        AccessToken2 accessToken = new AccessToken2(appId, appCertificate, expire);
        AccessToken2.Service serviceChat = new AccessToken2.ServiceChat(userId);

        serviceChat.addPrivilegeChat(AccessToken2.PrivilegeChat.PRIVILEGE_CHAT_USER, expire);
        accessToken.addService(serviceChat);

        try {
            return accessToken.build();
        }
        catch (Exception e) {
            e.printStackTrace();
            return "";
        }
    }

    /**
     * Build the CHAT app token.
     *
     * @param appId:          The App ID issued to you by Agora. Apply for a new App ID from
     *                        Agora Dashboard if it is missing from your kit. See Get an App ID.
     * @param appCertificate: Certificate of the application that you registered in
     *                        the Agora Dashboard. See Get an App Certificate.
     * @param expire:         represented by the number of seconds elapsed since now. If, for example, you want to access the
     *                        Agora Service within 10 minutes after the token is generated, set expireTimestamp as 600(seconds).
     * @return The RTC token.
     */
    public String buildAppToken(String appId,
                                String appCertificate,
                                int expire)
    {
        AccessToken2 accessToken = new AccessToken2(appId, appCertificate, expire);
        AccessToken2.Service serviceChat = new AccessToken2.ServiceChat();

        serviceChat.addPrivilegeChat(AccessToken2.PrivilegeChat.PRIVILEGE_CHAT_APP, expire);
        accessToken.addService(serviceChat);

        try {
            return accessToken.build();
        }
        catch (Exception e) {
            e.printStackTrace();
            return "";
        }
    }
}
