package com.yuedu.ydsf.eduConnect.system.proxy.constant;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 排课校管家回调响应枚举
 * <AUTHOR>
 * @date 2025/2/24 14:56
 */
@Getter
@AllArgsConstructor
public enum XiaogjPushResponseCodeEnum {

    XIAOGJ_PUSH_RESPONSE_CODE_ENUM_200(200, "响应成功"),
    XIAOGJ_PUSH_RESPONSE_CODE_ENUM_400(400, "响应失败");

    private final Integer code;

    private final String msg;

}
