package com.yuedu.ydsf.eduConnect.system.proxy.handler.interceptor;

import com.dtflys.forest.http.ForestRequest;
import com.dtflys.forest.interceptor.Interceptor;
import com.dtflys.forest.reflection.ForestMethod;
import com.dtflys.forest.utils.StringUtils;
import com.yuedu.ydsf.eduConnect.system.proxy.config.AgoraTokenGenerator;
import lombok.AllArgsConstructor;

/**
 *  声网灵动课堂接口
 *
 * @author: KL
 * @date: 2024/09/28
 **/
@AllArgsConstructor
public class AgoraEducationInterceptor implements Interceptor {

    private AgoraTokenGenerator tokenGenerator;

    @Override
    public void onInvokeMethod(ForestRequest request, ForestMethod method, Object[] args) {
        if (StringUtils.isNotBlank(tokenGenerator.getHostUrl())) {
            if (method.getMetaRequest().getUrl().contains("$appId")) {
                request.setUrl(tokenGenerator.getHostUrl() + method.getMetaRequest().getUrl().replace("$appId", tokenGenerator.getAppId()), args);
            } else {
                request.setUrl(tokenGenerator.getHostUrl() + method.getMetaRequest().getUrl(), args);
            }

        }

        request.addHeader("Authorization", String.format("agora token=%s",tokenGenerator.getAgoraAppToken()));
    }
}
