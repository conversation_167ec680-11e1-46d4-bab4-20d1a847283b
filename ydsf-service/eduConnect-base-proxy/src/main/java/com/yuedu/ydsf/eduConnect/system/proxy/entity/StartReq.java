package com.yuedu.ydsf.eduConnect.system.proxy.entity;

import lombok.Builder;
import lombok.Data;

import java.util.List;

/**
 * 开始录制请求
 *
 * @author: KL
 * @date: 2024/09/28
 **/
@Data
@Builder
public class StartReq {


    /**
     * 录制服务所在频道的名称
     */
    private String cname;

    /**
     * RTC 频道内使用的 UID
     */
    private String uid;

    /**
     * 请求参数
     */
    private ClientRequest clientRequest;

    @Data
    @Builder
    public static class ClientRequest {

        private String token;

        /**
         * 录制配置
         */
        private RecordingConfig recordingConfig;
        /**
         * 录制文件配置
         */
        private RecordingFileConfig recordingFileConfig;
        /**
         * 存储配置
         */
        private StorageConfig storageConfig;

        @Data
        @Builder
        public static class RecordingConfig {
            /**
             * 频道场景。
             * 0：通信场景。
             * 1：直播场景。
             */
            @Builder.Default
            private Integer channelType = 1;

            /**
             * 媒体流的输出模式。
             */
            private String streamMode ;

            /**
             * 最大频道空闲时间。单位为秒。最大值不超过 30 天。超出最大频道空闲时间后，录制服务会自动退出。录制服务退出后，如果你再次发起 start 请求，会产生新的录制文件。
             */
            @Builder.Default
            private Integer maxIdleTime = 6;

            /**
             * 订阅的媒体流类型。
             * <p>
             * 0：仅订阅音频。适用于智能语音审核场景。
             * 1：仅订阅视频。
             * 2：订阅音频和视频。
             */
            @Builder.Default
            private Integer streamTypes = 2;

            /**
             * 设置输出音频的采样率、码率、编码模式和声道数。
             */
            @Builder.Default
            private Integer audioProfile = 0;

            /**
             * 设置订阅的视频流类型。如果你在 SDK 客户端开启了双流模式，你可以选择订阅视频大流或者小流。
             * <p>
             * 0：视频大流，即高分辨率高码率的视频流
             * 1：视频小流，即低分辨率低码率的视频流
             */
            @Builder.Default
            private Integer videoStreamType = 0;

            /**
             * 转码输出的视频配置项
             */
            private TranscodingConfig transcodingConfig;

            /**
             * 订阅UID录制
             */
            private List<String> subscribeVideoUids;

            /**
             * 订阅UID录制
             */
            private List<String> subscribeAudioUids;

            /**
             * 扩展参数
             */
            private ExtensionParams extensionParams;

            @Data
            @Builder
            public static class TranscodingConfig {
                /**
                 * 输出视频的码率。
                 */
                @Builder.Default
                private Integer bitrate = 3500;

                /**
                 * 输出视频的帧率，单位为 fps，范围为 [5, 60]，默认值为 15
                 */
                @Builder.Default
                private Integer fps = 30;

                /**
                 * 设置输出视频的宽度，单位为 pixel，范围为 [480, 1920]。默认为 1280。videoWidth 和 videoHeight 的乘积需小于等于 1920 x 1080。
                 */
                @Builder.Default
                private Integer width = 1920;

                /**
                 * 设置输出视频的高度，单位为 pixel，范围为 [480, 1920]。默认为 720。videoWidth 和 videoHeight 的乘积需小于等于 1920 × 1080。
                 */
                @Builder.Default
                private Integer height = 1080;

                /**
                 * 录制视频的编码格式new option: 0: CBR(default) 1: VBR
                 */
                @Builder.Default
                private Integer rcMode = 1;
            }


            @Data
            @Builder
            public static class ExtensionParams{

                /**
                 * 录制延时
                 */
                @Builder.Default
                private Integer jbDelayMs = 2000;
            }
        }


        @Data
        @Builder
        public static class RecordingFileConfig {
            /**
             * 录制生成的视频文件类型
             */
            @Builder.Default
            private List<String> avFileType = List.of("hls","mp4");
        }

        @Data
        @Builder
        public static class StorageConfig {
            /**
             * 第三方云存储平台。
             * 1：Amazon S3
             * 2：阿里云
             * 3：腾讯云
             * 5：Microsoft Azure
             * 6：谷歌云
             * 7：华为云
             * 8：百度智能云
             */
            @Builder.Default
            private Integer vendor = 2;

            /**
             * 第三方云存储指定的地区信息。
             */
            @Builder.Default
            private Integer region = 3;

            /**
             * 第三方云存储的 Bucket。Bucket 名称需要符合对应第三方云存储服务的命名规则。。
             */
            private String bucket;

            /**
             * 第三方云存储的 Access Key（访问密钥）。如需延时转码，则访问密钥必须具备读写权限；
             */
            private String accessKey;

            /**
             * 第三方云存储的 Secret Key。
             */
            private String secretKey;

            /**
             * 录制文件在第三方云存储中的存储位置，与录制文件名前缀有关。如果设为 ["directory1","directory2"]，那么录制文件名前缀为 "directory1/directory2/"，即录制文件名为 directory1/directory2/xxx.m3u8。前缀长度（包括斜杠）不得超过 128 个字符。字符串中不得出现斜杠、下划线、括号等符号字符。以下为支持的字符集范围：
             */
            private List<String> fileNamePrefix;
        }
    }


}
