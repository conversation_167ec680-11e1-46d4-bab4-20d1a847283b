package com.yuedu.ydsf.eduConnect.system.proxy.config;

import com.yuedu.ydsf.eduConnect.system.proxy.config.properties.AgoraProperties;
import com.yuedu.ydsf.eduConnect.system.proxy.handler.AgoraEducationApi;
import com.yuedu.ydsf.eduConnect.system.proxy.handler.AgoraRecordApi;
import com.yuedu.ydsf.eduConnect.system.proxy.handler.AgoraRtmApi;
import com.yuedu.ydsf.eduConnect.system.proxy.handler.interceptor.AgoraEducationInterceptor;
import com.yuedu.ydsf.eduConnect.system.proxy.handler.interceptor.AgoraRecordInterceptor;
import com.yuedu.ydsf.eduConnect.system.proxy.handler.interceptor.AgoraRtmInterceptor;
import com.yuedu.ydsf.eduConnect.system.proxy.service.AgoraService;
import com.yuedu.ydsf.eduConnect.system.proxy.service.impl.AgoraServiceImpl;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * 声网配置类
 *
 * @author: KL
 * @date: 2024/09/28
 **/
@EnableConfigurationProperties(AgoraProperties.class)
public class AgoraConfig {

    @Bean
    public AgoraService agoraService(AgoraTokenGenerator agoraTokenGenerator,
                                     AgoraEducationApi agoraEducationApi,
                                     AgoraRecordApi agoraRecordApi,
                                     AgoraRtmApi agoraRtmApi
    ) {
        return new AgoraServiceImpl(agoraTokenGenerator, agoraEducationApi, agoraRecordApi, agoraRtmApi);
    }


    @Bean
    public AgoraRecordInterceptor agoraRecordInterceptor(AgoraTokenGenerator agoraTokenGenerator) {
        return new AgoraRecordInterceptor(agoraTokenGenerator);
    }


    @Bean
    public AgoraEducationInterceptor agoraNimbleAppInterceptor(AgoraTokenGenerator agoraTokenGenerator) {
        return new AgoraEducationInterceptor(agoraTokenGenerator);
    }


    @Bean
    public AgoraRtmInterceptor agoraRtmInterceptor(AgoraTokenGenerator agoraTokenGenerator) {
        return new AgoraRtmInterceptor(agoraTokenGenerator);
    }


    @Bean
    public AgoraTokenGenerator agoraTokenGenerator(AgoraProperties properties) {
        AgoraTokenGenerator agoraTokenGenerator = new AgoraTokenGenerator();
        agoraTokenGenerator.setAppId(properties.getAgoraAppId());
        agoraTokenGenerator.setAppCertificate(properties.getAgoraAppCertificate());
        agoraTokenGenerator.setAppKey(properties.getAgoraRecordKey());
        agoraTokenGenerator.setAppSecret(properties.getAgoraRecordSecret());
        agoraTokenGenerator.setHostUrl(properties.getAgoraHost());
        agoraTokenGenerator.setStorageBucket(properties.getAgoraStorageBucket());
        agoraTokenGenerator.setStorageKey(properties.getAgoraStorageKey());
        agoraTokenGenerator.setStorageSecret(properties.getAgoraStorageSecret());
        agoraTokenGenerator.setAgoraStorageEndpoint(properties.getAgoraStorageEndpoint());
        agoraTokenGenerator.setAgoraSignSecret(properties.getAgoraSignSecret());
        agoraTokenGenerator.setAgoraVodTemplateId(properties.getAgoraVodTemplateId());
        agoraTokenGenerator.setAgoraRtmAppId(properties.getAgoraRtmAppId());
        agoraTokenGenerator.setAgoraRtmHost(properties.getAgoraRtmHost());
        agoraTokenGenerator.setAgoraRtmAppCertificate(properties.getAgoraRtmAppCertificate());
        agoraTokenGenerator.setExpireTime(60 * 60 * 24);
        return agoraTokenGenerator;
    }
}
