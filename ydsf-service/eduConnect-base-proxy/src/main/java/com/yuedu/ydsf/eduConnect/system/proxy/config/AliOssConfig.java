package com.yuedu.ydsf.eduConnect.system.proxy.config;

import com.aliyun.oss.ClientBuilderConfiguration;
import com.aliyun.oss.OSS;
import com.aliyun.oss.OSSClient;
import com.aliyun.oss.OSSClientBuilder;
import com.aliyun.oss.common.auth.CredentialsProviderFactory;
import com.aliyun.oss.common.comm.SignVersion;
import com.yuedu.ydsf.eduConnect.system.proxy.config.properties.BaiduProperties;
import com.yuedu.ydsf.eduConnect.system.proxy.config.properties.OssProperties;
import lombok.Data;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;

/**
 * TODO
 *
 * @author: KL
 * @date: 2025/02/07
 **/
@EnableConfigurationProperties(OssProperties.class)
public class AliOssConfig {

    @Bean
    public OSS ossClient(OssProperties properties) {
        ClientBuilderConfiguration clientBuilderConfiguration = new ClientBuilderConfiguration();
        clientBuilderConfiguration.setSignatureVersion(SignVersion.V4);
        return OSSClientBuilder.create()
                .endpoint(properties.getEndpoint())
                .credentialsProvider(CredentialsProviderFactory.newDefaultCredentialProvider(properties.getAccessKeyId(), properties.getAccessKeySecret()))
                .clientConfiguration(clientBuilderConfiguration)
                .region(properties.getRegion())
                .build();
    }
}
