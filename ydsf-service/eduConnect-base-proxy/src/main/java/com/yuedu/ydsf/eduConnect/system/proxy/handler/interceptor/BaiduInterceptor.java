package com.yuedu.ydsf.eduConnect.system.proxy.handler.interceptor;

import com.alibaba.fastjson.JSON;
import com.dtflys.forest.http.ForestRequest;
import com.dtflys.forest.interceptor.Interceptor;
import com.dtflys.forest.reflection.ForestMethod;
import com.dtflys.forest.utils.StringUtils;
import com.yuedu.ydsf.eduConnect.system.proxy.config.properties.BaiduProperties;
import com.yuedu.ydsf.eduConnect.system.proxy.handler.BaiduApi;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.logging.log4j.util.Strings;
import org.springframework.cache.annotation.Cacheable;

import java.net.URI;
import java.net.http.HttpClient;
import java.net.http.HttpRequest;
import java.net.http.HttpResponse;

/**
 * TODO
 *
 * @author: KL
 * @date: 2025/02/07
 **/
@Slf4j
@AllArgsConstructor
public class BaiduInterceptor implements Interceptor {

    private BaiduProperties properties;

    @Override
    public void onInvokeMethod(ForestRequest request, ForestMethod method, Object[] args) {
        if (StringUtils.isNotBlank(properties.getHostUrl())) {
            request.setUrl(String.format("%s%s?access_token=%s", properties.getHostUrl(), method.getMetaRequest().getUrl(), getAccessToken()), args);
        }
    }


    private String getAccessToken() {
        HttpRequest build = HttpRequest.newBuilder()
                .uri(URI.create(String.format("%s/oauth/2.0/token?grant_type=client_credentials&client_id=%s&client_secret=%s", properties.getHostUrl(), properties.getApiKey(), properties.getSecretKey())))
                .POST(HttpRequest.BodyPublishers.noBody())
                .build();
        try {
          String body =  HttpClient.newHttpClient().send(build, HttpResponse.BodyHandlers.ofString()).body();
          log.info("百度获得access_token：{}",body);
          return JSON.parseObject(body).getString("access_token");
        }catch (Exception e){
            log.error("百度获得access_token失败：",e.getMessage());
        }
        return Strings.EMPTY;
    }
}
