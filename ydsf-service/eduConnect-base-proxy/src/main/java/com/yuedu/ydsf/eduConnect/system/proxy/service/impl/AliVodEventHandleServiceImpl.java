package com.yuedu.ydsf.eduConnect.system.proxy.service.impl;

import com.yuedu.ydsf.eduConnect.system.proxy.config.AliTokenGenerator;
import com.yuedu.ydsf.eduConnect.system.proxy.constant.AliConstant;
import com.yuedu.ydsf.eduConnect.system.proxy.dto.AliEventDTO;
import com.yuedu.ydsf.eduConnect.system.proxy.dto.AliVodTranscodeEventDTO;
import com.yuedu.ydsf.eduConnect.system.proxy.service.AliVodEventHandleService;
import com.yuedu.ydsf.eduConnect.system.proxy.util.AliVodUtil;
import java.util.Objects;
import java.util.function.Function;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * TODO
 *
 * @author: KL
 * @date: 2024/10/11
 **/
@Slf4j
@AllArgsConstructor
public class AliVodEventHandleServiceImpl implements AliVodEventHandleService {


    private AliTokenGenerator aliTokenGenerator;


    @Override
    public boolean verifySign(AliEventDTO eventDTO, String timestamp, String sign) {
        log.info("=================阿里云信息推送========================");
        log.info("阿里云视频点播服务回调：body:{} timestamp:{} sign:{}", eventDTO, timestamp, sign);
        if (!AliVodUtil.verifySign(sign, aliTokenGenerator.getCallbackUrl(), timestamp,
            aliTokenGenerator.getCallbackSecret())) {
            log.info("阿里云视频点播服务回调签名验证失败");
            return false;
        }
        return true;
    }

    @Override
    public boolean handlerEvent(AliEventDTO eventDTO,
        Function<AliVodTranscodeEventDTO, Boolean> function) {
        log.info("处理阿里云视频点播服务回调事件: {}", eventDTO);
        try {
            return switch (eventDTO.getEventType()) {
                case AliConstant.VOD_TRANSCODE_COMPLETE -> {
                    log.info("处理阿里云视频转码完成事件: {}", eventDTO);
                    yield Objects.isNull(function) ? true :
                        function.apply(AliVodTranscodeEventDTO.builder()
                            .videoId(eventDTO.getVideoId())
                            .success(eventDTO.isSuccess())
                            .build());
                }
                default -> true;
            };
        } catch (Exception e) {
            log.error("处理阿里云视频点播服务回调事件异常:body:{} msg:{}", eventDTO,
                e.getMessage());
        }

        return false;
    }
}
