package com.yuedu.ydsf.eduConnect.system.proxy.entity;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Builder;
import lombok.Data;

/**
 * TODO
 *
 * @author: KL
 * @date: 2025/03/19
 **/
@Data
@Builder
public class DetectReq {

    /**
     * 图片信息(总数据大小应小于10M，分辨率应小于1920*1080)，图片上传方式根据image_type来判断
     */
    private String image;

    /**
     * 图片类型 BASE64 FACE_TOKEN
     */
    @JSONField(name = "image_type")
    @Builder.Default
    private String imageType = "BASE64";

    /**
     * 包括age,expression,face_shape,gender,glasses,landmark,landmark150,
     * quality,eye_status,emotion,face_type,mask,spoofing信息
     * 逗号分隔. 默认只返回face_token、人脸框、概率和旋转角度
     */
    @JSONField(name = "face_field")
    private String faceField;

    /**
     * 最多处理人脸的数目，默认值为1，根据人脸检测排序类型检测图片中排序第一的人脸（默认为人脸面积最大的人脸），最大值20
     */
    @JSONField(name = "max_face_num")
    private Integer maxFaceNum;
    /**
     * 人脸的类型
     * LIVE表示生活照：通常为手机、相机拍摄的人像图片、或从网络获取的人像图片等
     * IDCARD表示身份证芯片照：二代身份证内置芯片中的人像照片
     * WATERMARK表示带水印证件照：一般为带水印的小图，如公安网小图
     * CERT表示证件照片：如拍摄的身份证、工卡、护照、学生证等证件图片
     * 默认LIVE
     */
    @JSONField(name = "face_type")
    private String faceType;
    /**
     * 活体控制 检测结果中不符合要求的人脸会被过滤
     * NONE: 不进行控制
     * LOW:较低的活体要求(高通过率 低攻击拒绝率)
     * NORMAL: 一般的活体要求(平衡的攻击拒绝率, 通过率)
     * HIGH: 较高的活体要求(高攻击拒绝率 低通过率)
     * 默认NONE
     */
    @JSONField(name = "liveness_control")
    private String livenessControl;
    /**
     *人脸检测排序类型
     * 0:代表检测出的人脸按照人脸面积从大到小排列
     * 1:代表检测出的人脸按照距离图片中心从近到远排列
     * 默认为0
     */
    @JSONField(name = "face_sort_type")
    private Integer faceSortType;
    /**
     *是否显示检测人脸的裁剪图base64值
     * 0：不显示（默认）
     * 1：显示
     * 当取值为1时，max_face_num字段的取值上限按5计算，即最多可返回5张人脸的裁剪图
     */
    @JSONField(name = "display_corp_image")
    private Integer displayCorpImage;
}
