package com.yuedu.ydsf.eduConnect.system.proxy.dto;

import com.yuedu.ydsf.eduConnect.system.proxy.constant.MqTypeEnums;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * TODO
 *
 * <AUTHOR>
 * @Date 2022-10-11 15:13
 * @Version 1.0.0
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class MqReq<T> {

    /**
     * 消息类型
     */
    private String type;

    /**
     * 子类型
     */
    private String  topic;

    /**
     * 数据
     */
    private T data;





    public static MqTypeEnums getMqType(MqReq mqReq) {
        for (MqTypeEnums value : MqTypeEnums.values()) {
            if (value.TYPE.equals(mqReq.getType()) && value.TOPTIC.equals(mqReq.getTopic())) {
                return value;
            }
        }
        return null;
    }
}
