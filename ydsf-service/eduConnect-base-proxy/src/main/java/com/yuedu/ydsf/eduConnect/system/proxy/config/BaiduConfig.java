package com.yuedu.ydsf.eduConnect.system.proxy.config;

import com.yuedu.ydsf.eduConnect.system.proxy.config.properties.AgoraProperties;
import com.yuedu.ydsf.eduConnect.system.proxy.config.properties.BaiduProperties;
import com.yuedu.ydsf.eduConnect.system.proxy.handler.interceptor.BaiduInterceptor;
import lombok.Data;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;

/**
 * TODO
 *
 * @author: KL
 * @date: 2025/02/07
 **/
@EnableConfigurationProperties(BaiduProperties.class)
public class BaiduConfig {


    @Bean
    public BaiduInterceptor baiduInterceptor(BaiduProperties properties) {
        return new BaiduInterceptor(properties);
    }

}
