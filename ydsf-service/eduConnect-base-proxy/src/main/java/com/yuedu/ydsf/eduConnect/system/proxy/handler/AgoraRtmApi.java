package com.yuedu.ydsf.eduConnect.system.proxy.handler;

import com.dtflys.forest.annotation.*;
import com.yuedu.ydsf.eduConnect.system.proxy.entity.RtmChannelReq;
import com.yuedu.ydsf.eduConnect.system.proxy.entity.RtmChannelResp;
import com.yuedu.ydsf.eduConnect.system.proxy.entity.RtmPeerReq;
import com.yuedu.ydsf.eduConnect.system.proxy.entity.RtmPeerResp;
import com.yuedu.ydsf.eduConnect.system.proxy.handler.interceptor.AgoraRtmInterceptor;

/**
 * 声网rtm 接口
 *
 * @author: KL
 * @date: 2024/11/06
 **/
@BaseRequest(interceptor = AgoraRtmInterceptor.class)
public interface AgoraRtmApi {


    /**
     *  发送点对点消息
     *
     * <AUTHOR>
     * @date 2024年11月06日 16时13分
     */
    @Post(value = "/dev/v2/project/$appId/rtm/users/{userId}/peer_messages")
    RtmPeerResp peerToPeer(@Var("userId") String userId, @Query("wait_for_ack") Boolean ack , @JSONBody RtmPeerReq req);



    /**
     *  频道消息
     *
     * <AUTHOR>
     * @date 2024年11月06日 16时24分
     */
    @Post(value = "/dev/v2/project/$appId/rtm/users/{userId}/channel_messages")
    RtmChannelResp channelMessage(@Var("userId") String userId, @JSONBody RtmChannelReq req);

}
