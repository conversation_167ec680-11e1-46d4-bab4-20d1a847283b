package com.yuedu.ydsf.eduConnect.system.proxy.constant;

/**
 * TODO
 *
 * @author: KL
 * @date: 2024/09/30
 **/
public class AgoraConstant {

    /**
     * http signature
     */
    public static final String AGORA_SIGNATURE = "Agora-Signature";


    /**
     * 云端录制文件上传成功
     */
    public static final int CLOUD_FILE_UPLOAD_FINISH = 31;

    /**
     * 距离上课前时长(单位:分钟)
     */
    public static final long DEFAULT_TIME_MINUTE = 60 * 24;

    /**
     * 创建课堂
     */
    public static final String CREATE_ROOMS = "%s/%s/edu/apps/%s/v2/rooms/%s";

    /**
     * 返回成功状态码
     */
    public static final Integer RESULT_SUCCESS = 0;

    /**
     * 上台人数上限
     */
    public static final int MAX_ACCEPT = 16;

    /**
     * 学生默认流音频状态,1: 启用
     */
    public static final int AUDIO_STATE_DISABLED = 0;

    /**
     * 学生默认流音频状态,0: 禁用
     */
    public static final int AUDIO_STATE_ENABLE = 1;

    /**
     * 合流录制模式
     */
    public static final String AGORA_RECORDING_MODE_MIX = "mix";
}
