package com.yuedu.ydsf.eduConnect.system.proxy.handler;

import com.dtflys.forest.annotation.*;
import com.yuedu.ydsf.eduConnect.system.proxy.entity.*;
import com.yuedu.ydsf.eduConnect.system.proxy.handler.interceptor.AgoraRecordInterceptor;

/**
 * 声网云端录制接口
 *
 * @author: KL
 * @date: 2024/09/28
 **/
@BaseRequest(interceptor = AgoraRecordInterceptor.class)
public interface AgoraRecordApi {


    /**
     *  获得可用资源
     *
     * <AUTHOR>
     * @date 2024年09月28日 10时43分
     * @param req 请求参数
     */
    @Post(url = "/v1/apps/$appId/cloud_recording/acquire")
    AcquireResp acquire(@JSONBody AcquireReq req);



    /**
     *  开始录制
     *
     * <AUTHOR>
     * @date 2024年09月28日 10时43分
     * @param resourceid 资源Id
     * @param mode 录制模式
     * @param req 请求参数
     */
    @Post(url = "/v1/apps/$appId/cloud_recording/resourceid/{resourceid}/mode/{mode}/start")
    StartResp start(@Var("resourceid") String resourceid, @Var("mode") String mode, @JSONBody StartReq req);



    /**
     *  停止录制
     *
     * <AUTHOR>
     * @date 2024年09月28日 10时43分
     * @param resourceid 资源Id
     * @param mode 录制模式
     * @param sid 录制sid
     * @param req 请求参数
     */
    @Post(url = "/v1/apps/$appId/cloud_recording/resourceid/{resourceid}/sid/{sid}/mode/{mode}/stop")
    StopResp stop(@Var("resourceid") String resourceid, @Var("sid") String sid, @Var("mode") String mode, @JSONBody StopReq req);



    /**
     *  查询录制状态
     *
     * <AUTHOR>
     * @date 2024年09月28日 10时43分
     * @param resourceid 录制模式
     * @param mode 录制模式
     * @param sid 录制sid
     */
    @Get(url = "/v1/apps/$appId/cloud_recording/resourceid/{resourceid}/sid/{sid}/mode/{mode}/query")
    QueryResp query(@Var("resourceid") String resourceid,@Var("sid") String sid, @Var("mode") String mode);


}
