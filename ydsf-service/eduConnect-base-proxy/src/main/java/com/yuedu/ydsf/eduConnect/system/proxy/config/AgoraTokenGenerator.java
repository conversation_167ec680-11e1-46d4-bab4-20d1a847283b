package com.yuedu.ydsf.eduConnect.system.proxy.config;

import com.yuedu.ydsf.eduConnect.system.proxy.util.EducationTokenBuilder2;
import com.yuedu.ydsf.eduConnect.system.proxy.util.RtcTokenBuilder;
import com.yuedu.ydsf.eduConnect.system.proxy.util.RtmTokenBuilder2;
import lombok.Data;

import java.time.Instant;
import java.util.Base64;

/**
 * 声网秘钥生成器
 *
 * @author: KL
 * @date: 2024/09/28
 **/
@Data
public class AgoraTokenGenerator {

    private String appId;

    private String appCertificate;

    private String appKey;

    private String appSecret;

    private Integer expireTime;

    private String hostUrl;

    private String storageKey;

    private String storageSecret;

    private String storageBucket;

    private String agoraStorageEndpoint;

    private String agoraSignSecret;

    private String agoraVodTemplateId;

    private String agoraRtmAppId;

    private String agoraRtmAppCertificate;

    private String agoraRtmHost;


    /**
     * 基本http认证 用于原始对接RTC 接口
     *
     * <AUTHOR>
     * @date 2024年09月28日 16时08分
     */
    public String getAgoraBasicAuthorization() {
        return new String(Base64.getEncoder().encode(String.format("%s:%s", getAppKey(), getAppSecret()).getBytes()));
    }


    /**
     * 灵动课堂接口token 默认24小时
     *
     * <AUTHOR>
     * @date 2024年09月28日 17时53分
     */
    public String getAgoraAppToken() {
        return getAgoraAppToken(null);
    }

    /**
     * 动态rtc token 默认24小时
     *
     * @param roomUUID
     * @param uid
     * @param roomUUID
     * <AUTHOR>
     * @date 2024年09月28日 21时22分
     */
    public String getAgoraRtcToken(String roomUUID, String uid, RtcTokenBuilder.Role role) {
        return getAgoraRtcToken(roomUUID, uid, role, null);
    }

    /**
     * 获取云信令token
     *
     * <AUTHOR>
     * @date 2024年11月06日 16时45分
     */
    public String getAgoraRtmToken(String userId) {
        return getAgoraRtmToken(userId, null);
    }

    /**
     * pc端加入房间token 默认24小时
     *
     * @param roomUUID
     * @param uid
     * @param role
     * <AUTHOR>
     * @date 2024年09月28日 17时53分
     */
    public String getAgoraRtmToken(String roomUUID, String uid, Short role) {
        return getAgoraRtmToken(roomUUID, uid, role, null);
    }


    /**
     * 获取appToken  用于灵动课堂接口调用
     *
     * @param expire
     * <AUTHOR>
     * @date 2024年09月28日 17时53分
     */
    public String getAgoraAppToken(Long expire) {
        EducationTokenBuilder2 tokenBuilder = new EducationTokenBuilder2();
        return tokenBuilder.buildAppToken(getAppId(), getAppCertificate(), expire != null ? expire.intValue() : getExpireTime());
    }


    /**
     * 获取前端加入房间token
     *
     * @param expire
     * <AUTHOR>
     * @date 2024年09月28日 17时53分
     */
    public String getAgoraRtmToken(String roomUUID, String uid, Short role, Long expire) {
        EducationTokenBuilder2 tokenBuilder = new EducationTokenBuilder2();
        return tokenBuilder.buildRoomUserToken(getAppId(), getAppCertificate(), roomUUID, uid, role, expire != null ? expire.intValue() : getExpireTime());
    }


    /**
     * 获得rtc token
     *
     * @param roomUUID
     * @param uid
     * @param roomUUID
     * <AUTHOR>
     * @date 2024年09月28日 21时22分
     */
    public String getAgoraRtcToken(String roomUUID, String uid, RtcTokenBuilder.Role role, Long expire) {
        RtcTokenBuilder rtcTokenBuilder = new RtcTokenBuilder();
        return rtcTokenBuilder.buildTokenWithUid(getAppId(), getAppCertificate(), roomUUID, Integer.parseInt(uid), role,
                (int) Instant.now()
                        .getEpochSecond() + (expire != null ? expire.intValue() : getExpireTime()));
    }


    /**
     * 获取云信令token
     *
     * <AUTHOR>
     * @date 2024年11月06日 16时43分
     */
    public String getAgoraRtmToken(String userId, Long expire) {
        RtmTokenBuilder2 token = new RtmTokenBuilder2();
        return token.buildToken(getAgoraRtmAppId(), getAgoraRtmAppCertificate(), userId,
                expire != null ? expire.intValue() : getExpireTime());
    }


}
