package com.yuedu.ydsf.eduConnect.system.proxy.config.properties;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;

/**
 * TODO
 *
 * @author: KL
 * @date: 2024/09/28
 **/
@Data
@ConfigurationProperties(prefix = "agora")
public class AgoraProperties {


    /**
     * APPID
     */
    private String agoraAppId;

    /**
     * app certificate
     */
    private String agoraAppCertificate;


    /**
     * 云端录制 key
     */
    private String agoraRecordKey;


    /**
     * 云端录制Secret
     */
    private String agoraRecordSecret;

    /**
     * 声网云端录制接口地址
     */
    private String agoraHost;

    /**
     * 存储key
     */
    private String agoraStorageKey;
    /**
     * 存储秘钥
     */
    private String agoraStorageSecret;
    /**
     * 存储桶
     */
    private String agoraStorageBucket;


    /**
     * oss 文件路径
     */
    private String agoraStorageEndpoint;

    /**
     * 验签秘钥
     */
    private String agoraSignSecret;

    /**
     * 转码模板
     */
    private String agoraVodTemplateId;

    /**
     * 云信令appId
     */
    private String agoraRtmAppId;

    /**
     * 云信令证书
     */
    private String agoraRtmAppCertificate;

    /**
     * 云信令域名
     */
    private String agoraRtmHost;


}
