package com.yuedu.ydsf.eduConnect.system.proxy.entity;

import lombok.Builder;
import lombok.Data;

/**
 * TODO
 *
 * @author: KL
 * @date: 2025/02/07
 **/
@Data
@Builder
public class BodyAttrReq {

    /**
     * 图片base64
     */
    private String image;

    /**
     * 类型：
     * gender,
     * age,
     * lower_wear,
     * upper_wear,
     * headwear,
     * face_mask,
     * glasses,
     * upper_color,
     * lower_color,
     * cellphone,
     * upper_wear_fg,
     * upper_wear_texture,
     * lower_wear_texture,
     * orientation,
     * umbrella,
     * bag,
     * smoke,
     * vehicle,
     * carrying_item,
     * upper_cut,
     * lower_cut,
     * occlusion,
     * is_human
     */
    private String type;
}
