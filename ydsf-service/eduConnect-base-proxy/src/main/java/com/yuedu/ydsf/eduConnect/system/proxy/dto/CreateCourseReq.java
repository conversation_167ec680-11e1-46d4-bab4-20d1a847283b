package com.yuedu.ydsf.eduConnect.system.proxy.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Builder;
import lombok.Data;

import java.util.List;

/**
 * 保存校管家创建排课类
 * <AUTHOR>
 * @date 2024年01月19日 09时57分
 */
@Data
public class CreateCourseReq {


    /**
     * 三方班级ID
     */
    @JsonProperty("tripartiteId")
    private String tripartiteId;

    /**
     * 排课记录开始时间 (yyyy-MM-dd HH:mm:ss)
     */
    @JsonProperty("cStartTime")
    private String cStartTime;

    /**
     * 排课记录结束时间(yyyy-MM-dd HH:mm:ss)
     */
    @JsonProperty("cEndTime")
    private String cEndTime;

    /**
     * 排课数量
     */
    @JsonProperty("cCourseTimes")
    private Integer cCourseTimes;

    /**
     * 上课教室ID
     */
    @JsonProperty("cClassroomID")
    private String cClassroomID;

    /**
     * 排课方式: 0-按周排课;  1-按日期排课;
     */
    @JsonProperty("cCourseMode")
    private Integer cCourseMode;

    /**
     * 排课数据
     */
    @JsonProperty("cCourseData")
    private List<CourseData> cCourseData;

    @Data
    @Builder
    public static class CourseData {

        /**
         * 周几
         */
        @JsonProperty("cWeekday")
        private Integer cWeekday;

        /**
         * 按周排课 几点开始
         */
        @JsonProperty("cWeekStartTime")
        private String cWeekStartTime;

        /**
         * 按周排课 几点结束
         */
        @JsonProperty("cWeekEndTime")
        private String cWeekEndTime;

        /**
         * 时间
         */
        @JsonProperty("cDate")
        private String cDate;

        /**
         * 上课教室ID
         */
        @JsonProperty("cClassroomID")
        private String cClassroomID;

        /**
         * 排课开始时间
         */
        @JsonProperty("cStartTime")
        private String cStartTime;

        /**
         * 排课结束时间
         */
        @JsonProperty("cEndTime")
        private String cEndTime;

        /**
         * 排课记录ID
         */
        @JsonProperty("threeId")
        private String threeId;

        /**
         * 主讲
         */
        @JsonProperty("speaker")
        private String speaker;

        /**
         * 助教
         */
        @JsonProperty("assistant")
        private String assistant;

    }

}
