package com.yuedu.ydsf.eduConnect.system.proxy.constant;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

/**
 * 校管家回调事件分组
 * <AUTHOR>
 * @date 2024年05月10日 17时16分
 */
public class XiaogjEventKeyGroupConstant {


    /**
     * eventId为排课记录id监听事件分组
     */
    public static final List<String> courseIdEventKeyList = Arrays.asList(
            XiaogjPushEventEnum.EDU_COURSE_CREATE.getType(),
            XiaogjPushEventEnum.EDU_COURSE_MODIFY.getType(),
            XiaogjPushEventEnum.EDU_COURSE_REMOVE.getType(),
            XiaogjPushEventEnum.EDU_COURSE_CANCEL.getType(),
            XiaogjPushEventEnum.EDU_COURSE_ATTEND.getType(),
            XiaogjPushEventEnum.EDU_COURSE_ATTEND_CANCEL.getType(),
            XiaogjPushEventEnum.EDU_COURSE_STUDENT_MODIFY.getType()
            );

    /**
     * eventId为班级id监听事件分组
     */
    public static final List<String> classIdEventKeyList = Arrays.asList(
            XiaogjPushEventEnum.CUS_CLASS_CREATE.getType(),
            XiaogjPushEventEnum.CUS_CLASS_MODIFY.getType(),
            XiaogjPushEventEnum.CUS_CLASS_REMOVE.getType(),
            XiaogjPushEventEnum.CUS_CLASS_FINISH.getType(),
            XiaogjPushEventEnum.EDU_CLASS_STUDENT_MODIFY.getType()
    );

    /**
     * eventId为正式学员id监听事件分组
     */
    public static final List<String> studentIdEventKeyList = Arrays.asList(
            XiaogjPushEventEnum.USER_STUDENT_CREATE.getType(),
            XiaogjPushEventEnum.USER_STUDENT_MODIFY.getType()
    );

    /**
     * 百家云监听事件分组
     */
    public static final List<String> baijyEventKeyList = Arrays.asList(
            XiaogjPushEventEnum.EDU_COURSE_CREATE.getType(),
            XiaogjPushEventEnum.EDU_COURSE_MODIFY.getType(),
            XiaogjPushEventEnum.EDU_CLASS_STUDENT_MODIFY.getType(),
            XiaogjPushEventEnum.EDU_COURSE_STUDENT_MODIFY.getType()
    );

    /**
     * 补课监听事件分组
     */
    public static final List<String> makeUpALessonMemberEventKeyList = Arrays.asList(
            XiaogjPushEventEnum.EDU_COURSE_ATTEND.getType(),
            XiaogjPushEventEnum.EDU_COURSE_ATTEND_CANCEL.getType(),
            XiaogjPushEventEnum.EDU_COURSE_REMOVE.getType()

    );

    /**
     * 双师排课学生监听事件分组
     */
    public static final List<String> ssClassCourseStudentEventKeyList = Arrays.asList(
            XiaogjPushEventEnum.EDU_CLASS_STUDENT_MODIFY.getType(),
            XiaogjPushEventEnum.EDU_COURSE_STUDENT_MODIFY.getType()
    );

    /**
     * 学生点名上课更新会员级别监听事件分组
     */
    public static final List<String> memberLevelEventKeyList = Arrays.asList(
            XiaogjPushEventEnum.EDU_COURSE_ATTEND.getType()
    );

    /**
     * 校管家新增/编辑正式学员同步会员数据监听事件分组
     */
    public static final List<String> studentEventKeyList = Arrays.asList(
            XiaogjPushEventEnum.USER_STUDENT_CREATE.getType(),
            XiaogjPushEventEnum.USER_STUDENT_MODIFY.getType()
    );

    /**
     * 课程删除监听事件分组
     */
    public static final List<String> courseRemoveEventKeyList = Collections.singletonList(XiaogjPushEventEnum.EDU_COURSE_REMOVE.getType());

    /**
     * 班级删除监听事件分组
     */
    public static final List<String> classRemoveEventKeyList = Collections.singletonList(XiaogjPushEventEnum.CUS_CLASS_REMOVE.getType());

}
