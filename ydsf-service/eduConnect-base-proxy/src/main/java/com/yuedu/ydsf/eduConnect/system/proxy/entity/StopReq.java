package com.yuedu.ydsf.eduConnect.system.proxy.entity;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Builder;
import lombok.Data;

/**
 * 停止请求
 *
 * @author: KL
 * @date: 2024/09/28
 **/
@Data
@Builder
public class StopReq {

    /**
     * 频道名称
     */
    private String cname;

    /**
     * 用户id
     */
    private String uid;

    /**
     * 请求信息
     */
    private ClientRequest clientRequest;

    @Data
    @Builder
    public static class ClientRequest{

        /**
         * true：异步。调用 stop 后立即收到响应。
         * false：同步。调用 stop 后，你需等待所有录制文件上传至第三方云存储方后会收到
         */
        @JSONField(name = "async_stop")
        @Builder.Default
        private boolean asyncStop = true;
    }


}
