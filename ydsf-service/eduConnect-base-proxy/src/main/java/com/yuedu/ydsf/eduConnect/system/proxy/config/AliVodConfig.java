package com.yuedu.ydsf.eduConnect.system.proxy.config;

import com.aliyun.teaopenapi.models.Config;
import com.aliyun.vod20170321.Client;
import com.yuedu.ydsf.eduConnect.system.proxy.config.properties.VodProperties;
import com.yuedu.ydsf.eduConnect.system.proxy.service.AliVodEventHandleService;
import com.yuedu.ydsf.eduConnect.system.proxy.service.VodService;
import com.yuedu.ydsf.eduConnect.system.proxy.service.impl.AliVodEventHandleServiceImpl;
import com.yuedu.ydsf.eduConnect.system.proxy.service.impl.VodServiceImpl;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * 阿里云点播配置
 *
 * @author: KL
 * @date: 2024/09/27
 **/
@EnableConfigurationProperties(VodProperties.class)
public class AliVodConfig {

    @Bean
    public Client client(AliTokenGenerator aliTokenGenerator) throws Exception {
        Config config = new Config()
                .setAccessKeyId(aliTokenGenerator.getAccessKeyId())
                .setAccessKeySecret(aliTokenGenerator.getAccessKeySecret());
        config.endpoint = aliTokenGenerator.getEndpoint();
        return new Client(config);
    }

    @Bean
    public VodService vodService(Client client) {
        return new VodServiceImpl(client);
    }


    @Bean
    public AliVodEventHandleService aliVodEventHandleService(
            AliTokenGenerator aliTokenGenerator) {
        return new AliVodEventHandleServiceImpl(aliTokenGenerator);
    }

    @Bean
    public AliTokenGenerator aliTokenGenerator(VodProperties vodProperties) {
        return AliTokenGenerator.builder()
                .accessKeyId(vodProperties.getAccessKeyId())
                .accessKeySecret(vodProperties.getAccessKeySecret())
                .templateGroupId(vodProperties.getTemplateGroupId())
                .callbackUrl(vodProperties.getCallbackUrl())
                .callbackSecret(vodProperties.getCallbackSecret())
                .endpoint(vodProperties.getEndpoint())
                .build();
    }

}
