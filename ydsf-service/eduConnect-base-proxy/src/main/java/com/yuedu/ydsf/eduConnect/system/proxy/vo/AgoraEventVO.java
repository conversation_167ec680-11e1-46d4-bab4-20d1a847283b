package com.yuedu.ydsf.eduConnect.system.proxy.vo;

import com.yuedu.ydsf.eduConnect.system.proxy.constant.AgoraEnum;
import lombok.Data;

/**
 * @author: KL
 * @date: 2024/09/30
 **/
@Data
public class AgoraEventVO {

    /**
     * 状态
     */
    private String message;


    public static AgoraEventVO success() {
        AgoraEventVO agoraEventVO = new AgoraEventVO();
        agoraEventVO.setMessage(AgoraEnum.AGORA_EVENT_TYPE_SUCCESS.getType());
        return agoraEventVO;
    }
}
