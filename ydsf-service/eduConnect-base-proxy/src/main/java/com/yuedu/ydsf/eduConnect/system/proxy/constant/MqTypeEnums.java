package com.yuedu.ydsf.eduConnect.system.proxy.constant;

import lombok.AllArgsConstructor;

/**
 * TODO
 *
 * <AUTHOR>
 * @Date 2022-10-11 16:12
 * @Version 1.0.0
 */
@AllArgsConstructor
public enum MqTypeEnums {

    TANMARK_CALLBACK_MSG(MqConstant.TANMARK_EXCHANGE,MqConstant.TANMARK_CALLBACK_ROUTER,"TANMARK_CALLBACK_MSG", "探马消息推送"),
    ZS_BANK_CALLBACK_MSG(MqConstant.ZS_BANK_EXCHANGE,MqConstant.ZS_BANK_CALLBACK_ROUTER,"ZS_BANK_CALLBACK_MSG", "招商银行消息推送"),
    XIAOGJ_PUSH_MSG(MqConstant.XIAOGJ_PUSH_EXCHANGE, MqConstant.XIAOGJ_PUSH_EVENT_ROUTER, "XIAOGJ_PUSH_EVENT_MSG", "校管家消息推送"),
    AGORA_PUSH_MSG(MqConstant.AGORA_PUSH_EXCHANGE, MqConstant.AGORA_PUSH_EVENT_ROUTER, "AGORA_PUSH_EVENT_MSG", "声网消息推送"),
    VOD_TANS_MSG(MqConstant.VOD_EXCHANGE,MqConstant.VOD_TRANSCODING_ROUTER,"VOD_TANS_MSG", "点播转码消息"),
    VOD_TRANS_CALLBACK_MSG(MqConstant.VOD_EXCHANGE,MqConstant.VOD_TRANSCODING_CALLBACK_ROUTER,"VOD_TRANS_CALLBACK_MSG", "点播转码回调消息"),
    SS_XIAOGJ_PUSH_MSG(MqConstant.SS_XIAOGJ_PUSH_EXCHANGE, MqConstant.SS_XIAOGJ_PUSH_EVENT_ROUTER, "SS_XIAOGJ_PUSH_EVENT_MSG", "双师排课校管家消息推送"),
    SS_XIAOGJ_PUSH_CALLBACK_MSG(MqConstant.SS_XIAOGJ_PUSH_CALLBACK_EXCHANGE, MqConstant.SS_XIAOGJ_PUSH_CALLBACK_EVENT_ROUTER, "SS_XIAOGJ_PUSH_CALLBACK_EVENT_MSG", "双师排课校管家消息回调推送");
    /**
     * 消息类型
     */
    public String TYPE;


    /**
     * 订阅类型
     */
    public String TOPTIC;


    /**
     * 消息标识
     */
    public String TAG;

    /**
     * 描述
     */
    public String DESC;


}
