package com.yuedu.ydsf.eduConnect.system.proxy.entity;

import lombok.Data;

/**
 * 查询状态
 *
 * @author: KL
 * @date: 2024/09/28
 **/
@Data
public class QueryResp extends AgoraRecordResp {

    /**
     * 云端录制使用的 Resource ID。
     */
    private String resourceId;

    /**
     * 录制 ID。标识一次录制周期。
     */
    private String sid;

    /**
     * 录制的频道名。
     */
    private String cname;

    /**
     * 字符串内容为云端录制服务在 RTC 频道内使用的 UID，用于标识频道内的录制服务。。
     */
    private String uid;

    private ServerResponse serverResponse;

    @Data
    public static class ServerResponse {

        /**
         * 当前云服务的状态
         */
        private Integer status;
    }

}
