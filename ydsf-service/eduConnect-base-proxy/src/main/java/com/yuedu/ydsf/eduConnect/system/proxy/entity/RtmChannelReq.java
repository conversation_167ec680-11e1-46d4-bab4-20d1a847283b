package com.yuedu.ydsf.eduConnect.system.proxy.entity;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Builder;
import lombok.Data;

/**
 * TODO
 *
 * @author: KL
 * @date: 2024/11/06
 **/
@Data
@Builder
public class RtmChannelReq {

    /**
     * 接收频道消息的 RTM 频道名
     */
    @JSONField(name = "channel_name")
    private String channelName;
    /**
     * 是否保存为历史消息
     */
    @Builder.Default
    @JSONField(name = "enable_historical_messaging")
    private Boolean enableHistoricalMessaging = false;
    /**
     * 频道消息内容
     */
    private String payload;
}
