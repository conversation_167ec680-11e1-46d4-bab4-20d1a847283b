package com.yuedu.ydsf.eduConnect.system.proxy.service.impl;

import com.alibaba.fastjson.JSON;
import com.aliyun.vod20170321.Client;
import com.aliyun.vod20170321.models.*;
import com.dtflys.forest.utils.StringUtils;
import com.yuedu.ydsf.common.core.exception.BizException;
import com.yuedu.ydsf.eduConnect.system.proxy.config.properties.VodProperties;
import com.yuedu.ydsf.eduConnect.system.proxy.dto.RegisterMetaDataDTO;
import com.yuedu.ydsf.eduConnect.system.proxy.service.VodService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.logging.log4j.util.Strings;
import org.springframework.http.HttpStatus;

import java.util.List;
import java.util.Objects;

import static com.yuedu.ydsf.common.core.constant.enums.BizErrorCodeEnum.REQUEST_ERROR;

/**
 * 阿里云Vod对接
 *
 * @author: KL
 * @date: 2024/09/27
 **/
@Slf4j
@AllArgsConstructor
public class VodServiceImpl implements VodService {

    private Client vodClient;


    @Override
    public CreateUploadVideoResponseBody getUploadAuth(String fileName, String title) throws Exception {
        CreateUploadVideoRequest request = new CreateUploadVideoRequest();
        request.setFileName(fileName);
        request.setTitle(title);
        CreateUploadVideoResponse uploadVideo = vodClient.createUploadVideo(request);
        return uploadVideo.body;
    }

    @Override
    public GetVideoPlayAuthResponseBody getVideoPlayAuth(String videoId, Long expiredTime) throws Exception {
        GetVideoPlayAuthRequest request = new GetVideoPlayAuthRequest();
        request.setVideoId(videoId);
        request.setAuthInfoTimeout(expiredTime);
        GetVideoPlayAuthResponse response = vodClient.getVideoPlayAuth(request);
        return response.body;
    }

    @Override
    public RefreshUploadVideoResponseBody refreshUploadAuth(String videoId) throws Exception {
        RefreshUploadVideoRequest request = new RefreshUploadVideoRequest();
        request.setVideoId(videoId);
        RefreshUploadVideoResponse refreshUploadVideoResponse = vodClient.refreshUploadVideo(request);
        return refreshUploadVideoResponse.body;
    }

    @Override
    public SubmitTranscodeJobsResponseBody submitTranscodeTask(String videoId, String templateGroupId) throws Exception {
        SubmitTranscodeJobsRequest request = new SubmitTranscodeJobsRequest();
        request.setVideoId(videoId);
        request.setTemplateGroupId(templateGroupId);
        SubmitTranscodeJobsResponse response = vodClient.submitTranscodeJobs(request);
        return response.body;
    }

    @Override
    public String getMezzanineInfo(String videoId) throws Exception {
        GetMezzanineInfoRequest request = new GetMezzanineInfoRequest();
        request.setVideoId(videoId);
        GetMezzanineInfoResponse mezzanineInfo = vodClient.getMezzanineInfo(request);
        return mezzanineInfo.body.getMezzanine().getFileURL();
    }

    @Override
    public List<String> getPlayInfo(String videoId) throws Exception {
        GetPlayInfoRequest request = new GetPlayInfoRequest();
        request.setVideoId(videoId);
        GetPlayInfoResponse playInfo = vodClient.getPlayInfo(request);
        return playInfo.body.getPlayInfoList().getPlayInfo().stream().map(s -> s.getPlayURL()).toList();
    }


    @Override
    public List<RegisterMetaDataDTO> registerMedia(List<RegisterMetaDataDTO> registerMetadatas) {
        return registerMedia(registerMetadatas, Strings.EMPTY);
    }

    @Override
    public List<RegisterMetaDataDTO> registerMedia(List<RegisterMetaDataDTO> registerMetadatas, String templateGroupId) {
        try{
            RegisterMediaRequest request = new RegisterMediaRequest();
            request.setRegisterMetadatas(JSON.toJSONString(registerMetadatas));
            if(StringUtils.isNotBlank(templateGroupId)){
                request.setTemplateGroupId(templateGroupId);
            }
            RegisterMediaResponse registerMediaResponse = vodClient.registerMedia(request);
            if(!Integer.valueOf(HttpStatus.OK.value()).equals(registerMediaResponse.getStatusCode())){
                throw new BizException(REQUEST_ERROR, "阿里注册错误,错误码："+ JSON.toJSONString(registerMediaResponse));
            }

            if(Objects.isNull(registerMediaResponse.body) || Objects.isNull(registerMediaResponse.body.getRegisteredMediaList())){
                throw new BizException(REQUEST_ERROR, "阿里注册错误，返回值为空"+ registerMediaResponse);
            }

            return registerMediaResponse.body.getRegisteredMediaList().stream().map(s->{
                RegisterMetaDataDTO registerMetaDataDTO = new RegisterMetaDataDTO();
                registerMetaDataDTO.setVideoId(s.getMediaId());
                registerMetaDataDTO.setFileUrl(s.getFileURL());
                return registerMetaDataDTO;
            }).toList();
        }catch (Exception e){
            log.error("注册视频失败:{}", e.getMessage());
            throw new BizException(REQUEST_ERROR, "注册视频失败:"+e.getMessage());
        }
    }

    @Override
    public String getMezzanine(String videoId) {
        try{
            GetMezzanineInfoRequest request = new GetMezzanineInfoRequest();
            request.setVideoId(videoId);
            GetMezzanineInfoResponse mezzanineInfo = vodClient.getMezzanineInfo(request);
            if(!Integer.valueOf(HttpStatus.OK.value()).equals(mezzanineInfo.getStatusCode())){
                throw new BizException(REQUEST_ERROR, "阿里获取原始视频链接错误，错误码："+ JSON.toJSONString(mezzanineInfo));
            }

            if(Objects.isNull(mezzanineInfo.body) || Objects.isNull(mezzanineInfo.body.getMezzanine())){
                throw new BizException(REQUEST_ERROR, "阿里获取原始视频链接错误，返回值为空" + JSON.toJSONString(mezzanineInfo));
            }

            return mezzanineInfo.body.getMezzanine().getFileURL();
        }catch (Exception e){
            log.error("获得原始视频失败:{}", e.getMessage());
            throw new BizException(REQUEST_ERROR, "获得原始视频失败:"+e.getMessage());
        }
    }
}
