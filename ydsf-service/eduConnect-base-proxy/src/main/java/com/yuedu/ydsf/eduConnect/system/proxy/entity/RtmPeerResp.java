package com.yuedu.ydsf.eduConnect.system.proxy.entity;

import lombok.Data;

/**
 * TODO
 *
 * @author: KL
 * @date: 2024/11/06
 **/
@Data
public class RtmPeerResp {

    /**
     * 消息发送状态。
     * "message_sent": 消息已发送。
     * "message_delivered": 接收端已收到消息。
     * "message_offline": 接收端离线。
     */
    private String code;
    /**
     * 	请求结果。
     * "success"：请求成功。
     * "failed"：请求失败。
     */
    private String result;

    /**
     * 标识此次请求的唯一 ID。
     */
    private String request_id;

    public boolean isSuccess(){
        return "success".equals(result);
    }
}
