package com.yuedu.ydsf.eduConnect.system.proxy.constant;

/**
 * 常量
 *
 * <AUTHOR>
 * @date 2021/12/21 9:40
 */
public interface MqConstant {


    /**
     * VOD交换机
     */
    public static final String VOD_EXCHANGE = "vod_exchange";

    /**
     * 探马回调交换机
     */
    public static final String TANMARK_EXCHANGE = "tanmark_exchange";

    /**
     * 招商回调交换机
     */
    public static final String ZS_BANK_EXCHANGE = "zs_bank_exchange";

    /**
     * 校管家推送交换机
     */
    public static final String XIAOGJ_PUSH_EXCHANGE = "xiaogj_push_exchange";

    /**
     * 声网推送交换机
     */
    public static final String AGORA_PUSH_EXCHANGE = "agora_push_exchange";

    /**
     * 双师排课推送校管家交换机
     */
    public static final String SS_XIAOGJ_PUSH_EXCHANGE = "ss_xiaogj_push_exchange";

    /**
     * 双师排课推送校管家回调交换机
     */
    public static final String SS_XIAOGJ_PUSH_CALLBACK_EXCHANGE = "ss_xiaogj_push_callback_exchange";

    /**
     * VOD转码队列
     */
    public static final String VOD_BS_TRANSCODING_QUEUE = "vod_bs_transcoding_queue";

    public static final String VOD_OP_TRANSCODING_QUEUE = "vod_op_transcoding_queue";

    /**
     * VOD转码回调队列
     */
    public static final String VOD_BS_TRANSCODING_CALLBACK_QUEUE = "vod_bs_transcoding_callback_queue";

    public static final String VOD_OP_TRANSCODING_CALLBACK_QUEUE = "vod_op_transcoding_callback_queue";

    /**
     * VOD转码
     */
    public static final String VOD_TRANSCODING_ROUTER = "vod_transcoding_router_key";

    /**
     * 校管家默认队列
     */
    public static final String XIAOGJ_PUSH_DEFAULT_QUEUE = "xiaogj_push_default_queue";


    /**
     * 声网默认队列
     */
    public static final String AGORA_PUSH_DEFAULT_QUEUE = "agora_push_default_queue";

    /**
     * 双师排课推送校管家默认队列
     */
    public static final String SS_XIAOGJ_PUSH_DEFAULT_QUEUE = "ss_xiaogj_push_default_queue";

    /**
     * 双师排课推送校管家回调默认队列
     */
    public static final String SS_XIAOGJ_PUSH_CALLBACK_DEFAULT_QUEUE = "ss_xiaogj_push_callback_default_queue";

    /**
     * VOD转码回调
     */
    public static final String VOD_TRANSCODING_CALLBACK_ROUTER = "vod_transcoding_callback_router";

    /**
     * 探马回调路由
     */
    public static final String TANMARK_CALLBACK_ROUTER = "";

    /**
     * 招商回调路由
     */
    public static final String ZS_BANK_CALLBACK_ROUTER = "";

    /**
     * 校管家路由
     */
    public static final String XIAOGJ_PUSH_EVENT_ROUTER = "";

    /**
     * 声网路由
     */
    public static final String AGORA_PUSH_EVENT_ROUTER = "";

    /**
     * 双师排课推送校管家路由
     */
    public static final String SS_XIAOGJ_PUSH_EVENT_ROUTER = "";

    /**
     * 双师排课推送校管家回调路由
     */
    public static final String SS_XIAOGJ_PUSH_CALLBACK_EVENT_ROUTER = "";

}
