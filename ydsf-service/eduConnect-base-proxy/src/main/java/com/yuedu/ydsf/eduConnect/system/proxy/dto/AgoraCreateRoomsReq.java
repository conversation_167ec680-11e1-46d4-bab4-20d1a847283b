package com.yuedu.ydsf.eduConnect.system.proxy.dto;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Builder;
import lombok.Data;

/**
 * 声网-创建课堂请求类
 * <AUTHOR>
 * @date 2023年12月29日
 */
@Data
public class AgoraCreateRoomsReq {


    /**
     * 课堂 uuid
     */
    private String roomUuid;

    /**
     * 房间类型，可设为：
     * 0: 一对一
     * 2: 大班课
     * 4: 小班课
     */
    private Integer roomType;

    /**
     * 房间名称，用于用户显示目的。最长不可超过 64 字节。
     */
    private String roomName;

    /**
     * 房间属性。
     */
    private RoomProperties roomProperties;

    /**
     * 角色配置
     */
    @JSONField(name = "roleConfig")
    private RoleConfig roleConfig;

    /**
     * 课堂状态，可以设置为以下值：
     * 0: 未开始。
     * 1: 开始。
     * 2: 结束。课堂时间结束，但在拖堂时间内，用户可以加入课堂和在课堂内逗留。
     * 3: 关闭。拖堂时间结束，课堂关闭，所有用户被踢出并无法再进入。
     */
    private Integer state;

    @Data
    @Builder
    public static class RoomProperties {
        /**
         * 课程计划，包含开始时间，持续时长，拖堂时长等属性。
         */
        private Schedule schedule;

        @Data
        @Builder
        public static class Schedule {
            /**
             * 课堂开始时间戳，单位为毫秒。该字段不可更新。
             */
            private Long startTime;
            /**
             * 课堂持续时长，单位为秒。如果你设置了课堂持续时长和拖堂时长，当开启录制时，会按照二者之和向上取整设置最长录制时间 maxRecordingHour 参数。
             */
            private Integer duration;
            /**
             * 拖堂时长，单位为秒。当课堂持续时长结束后，课程会进入“结束”状态（state= 2），此时用户仍可以正常进入和逗留在教室。当拖堂时间结束时，课堂会进入“关闭”状态（state= 3），并踢出所有用户。
             * 不设置此值, 默认为: 600秒
             */
            private Integer closeDelay;
        }


        /**
         * 申请邀请流程，包含举手等功能。
         */
        private Processes processes;

        @Data
        @Builder
        public static class Processes {
            /**
             * 上台设置，包含上台人数上限等。
             */
            private HandsUp handsUp;

            @Data
            @Builder
            public static class HandsUp {
                /**
                 * 上台人数上限。
                 */
                private Integer maxAccept;

                /**
                 * 默认上台角色
                 */
                private String defaultAcceptRole;
            }

        }

    }

    @Data
    @Builder
    public static class RoleConfig {
        /**
         * 学生角色配置
         */
        @JSONField(name = "2")
        private Two two;

        @Data
        @Builder
        public static class Two {

            /**
             * 学生默认流配置
             */
            @JSONField(name = "defaultStream")
            private DefaultStream defaultStream;
        }

        @Data
        @Builder
        public static class DefaultStream {
            /**
             * 学生默认流音频状态。 可设为：
             * 0: 禁用
             * 1: 启用
             */
            @JSONField(name = "audioState")
            private Integer audioState = 0;
        }

    }

}
