package com.yuedu.ydsf.eduConnect.system.proxy.config.properties;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;

/**
 * TODO
 *
 * @author: KL
 * @date: 2024/09/27
 **/
@Data
@ConfigurationProperties(prefix = "vod")
public class VodProperties {

    /**
     * 阿里云vod accessKeyId
     */
    private String accessKeyId;

    /**
     * 阿里云vod accessKeySecret
     */
    private String accessKeySecret;

    /**
     * 域名
     */
    private String endpoint = "vod.cn-beijing.aliyuncs.com";

    /**
     * 回调地址
     */
    private String callbackUrl;

    /**
     * 模板组id
     */
    private String templateGroupId;

    /**
     * 回调秘钥
     */
    private String callbackSecret;

    /**
     * 默认模板组id
     */
    private String defaultTemplateGroupId;

}
