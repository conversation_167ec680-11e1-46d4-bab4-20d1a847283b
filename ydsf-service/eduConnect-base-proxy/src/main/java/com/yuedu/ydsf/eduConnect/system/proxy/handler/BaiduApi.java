package com.yuedu.ydsf.eduConnect.system.proxy.handler;

import com.dtflys.forest.annotation.BaseRequest;
import com.dtflys.forest.annotation.Body;
import com.dtflys.forest.annotation.Post;
import com.yuedu.ydsf.eduConnect.system.proxy.entity.*;
import com.yuedu.ydsf.eduConnect.system.proxy.handler.interceptor.BaiduInterceptor;

/**
 * TODO
 *
 * @author: KL
 * @date: 2025/02/07
 **/
@BaseRequest(interceptor = BaiduInterceptor.class)
public interface BaiduApi {

    /**
     *  人体检查
     *
     * <AUTHOR>
     * @date 2025年02月07日 09时55分
     */
    @Post(url = "/rest/2.0/image-classify/v1/body_attr",dataType = "json")
    BodyAttrResp bodyAttr(@Body BodyAttrReq req);


    /**
     *  人脸检测
     *
     * <AUTHOR>
     * @date 2025年03月19日 13时51分
     */
    @Post(url = "/rest/2.0/face/v3/detect",dataType = "json")
    DetectResp detect(@Body DetectReq req);


    /**
     *  人体关键点分析
     *
     * <AUTHOR>
     * @date 2025年03月19日 14时36分
     */
    @Post(url = "/rest/2.0/image-classify/v1/body_analysis",dataType = "json")
    BodyAnalysisResp bodyAnalysis(@Body BodyAnalysisReq req);
}
