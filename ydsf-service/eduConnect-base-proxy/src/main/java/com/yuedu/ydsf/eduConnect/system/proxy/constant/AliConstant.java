package com.yuedu.ydsf.eduConnect.system.proxy.constant;

/**
 * 阿里常量
 *
 * @author: KL
 * @date: 2024/10/11
 **/
public class AliConstant {

    /**
     * UNIX时间戳，整型正数，固定长度10，1970年1月1日以来的秒数，表示回调请求发起时间。
     */
    public static final String X_VOD_TIMESTAMP = "X-VOD-TIMESTAMP";

    /**
     * 签名字符串，为32位MD5值，详细说明请参见下文签名算法。
     */
    public static final String X_VOD_SIGNATURE = "X-VOD-SIGNATURE";


    /**
     *   视频上传完成
     */
    public static final String VOD_FILE_UPLOAD_COMPLETE = "FileUploadComplete";


    /**
     * 全部清晰度转码完成
     */
    public static final String VOD_TRANSCODE_COMPLETE = "TranscodeComplete";
}
