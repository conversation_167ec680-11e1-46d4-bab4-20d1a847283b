package com.yuedu.ydsf.eduConnect.jw.controller;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.lock.annotation.Lock4j;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yuedu.ydsf.common.idempotent.annotation.Idempotent;
import com.yuedu.ydsf.common.core.util.R;
import com.yuedu.ydsf.common.core.util.V_A;
import com.yuedu.ydsf.common.core.util.V_E;
import com.yuedu.ydsf.common.excel.annotation.ResponseExcel;
import com.yuedu.ydsf.common.feign.annotation.NoToken;
import com.yuedu.ydsf.common.log.annotation.SysLog;
import com.yuedu.ydsf.common.security.annotation.Inner;
import com.yuedu.ydsf.common.security.annotation.StorePermission;
import com.yuedu.ydsf.common.security.util.SecurityUtils;
import com.yuedu.ydsf.common.security.util.StoreContextHolder;
import com.yuedu.ydsf.eduConnect.api.constant.CheckInEntryTypeEnum;
import com.yuedu.ydsf.eduConnect.jw.api.query.TimetableChangeTargetQuery;
import com.yuedu.ydsf.eduConnect.jw.api.valid.BClassTimeStudentValidGroup;
import com.yuedu.ydsf.eduConnect.jw.api.vo.AttendanceConfirmationResultVO;
import com.yuedu.ydsf.eduConnect.jw.api.vo.AttendanceManagementVO;
import com.yuedu.ydsf.eduConnect.jw.api.vo.BatchBindClickerResultVO;
import com.yuedu.ydsf.eduConnect.jw.api.vo.CheckInStudentVO;
import com.yuedu.ydsf.eduConnect.jw.api.vo.CheckInStudentVO.StudentCheckInInfoVO;
import com.yuedu.ydsf.eduConnect.jw.api.vo.ClassStudentErrorVO;
import com.yuedu.ydsf.eduConnect.jw.api.vo.ClickerUnbindPromptVO;
import com.yuedu.ydsf.eduConnect.jw.api.vo.TimetableChangeTargetVO;
import com.yuedu.ydsf.eduConnect.jw.entity.BClassTimeStudent;
import com.yuedu.ydsf.eduConnect.jw.service.BInteractionReceiverClickerService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springdoc.core.annotations.ParameterObject;
import org.springframework.beans.BeanUtils;
import org.springframework.http.HttpHeaders;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import com.yuedu.ydsf.common.security.annotation.HasPermission;
import com.yuedu.ydsf.eduConnect.jw.service.BClassTimeStudentService;
import com.yuedu.ydsf.eduConnect.jw.api.query.BClassTimeStudentQuery;
import com.yuedu.ydsf.eduConnect.jw.api.dto.AttendanceConfirmationDTO;
import com.yuedu.ydsf.eduConnect.jw.api.dto.BClassTimeStudentDTO;
import com.yuedu.ydsf.eduConnect.jw.api.dto.StudentCheckInPreValidationDTO;
import com.yuedu.ydsf.eduConnect.jw.api.dto.TemporaryStudentDTO;
import com.yuedu.ydsf.eduConnect.jw.api.vo.BClassTimeStudentVO;


import java.io.Serializable;
import java.util.List;

/**
 * 课次学生表控制层
 *
 * <AUTHOR>
 * @date 2025/02/24
 */
@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping(value = "/bClassTimeStudent")
@Tag(description = "b_class_time_student", name = "课次学生表")
@SecurityRequirement(name = HttpHeaders.AUTHORIZATION)
public class BClassTimeStudentController {

  private final BClassTimeStudentService classTimeStudentService;

  private final BInteractionReceiverClickerService bInteractionReceiverClickerService;

  /**
   * 课次学生表分页查询
   *
   * @param page 分页对象
   * @param bClassTimeStudentQuery 课次学生表
   * @return R
   */
  @GetMapping("/page")
  @Operation(summary = "分页查询", description = "课次学生表分页查询")
  @StorePermission
  public R page(
      @ParameterObject Page page, @ParameterObject BClassTimeStudentQuery bClassTimeStudentQuery) {
    return R.ok(classTimeStudentService.page(page, bClassTimeStudentQuery));
  }

  /**
   * 通过id查询课次学生表
   *
   * @param id id
   * @return R
   */
  @Operation(summary = "通过id查询", description = "通过id查询课次学生表")
  @GetMapping("/{id}")
  @StorePermission
  public R getById(@PathVariable Serializable id) {
    return R.ok(classTimeStudentService.getById(id));
  }

  /**
   * 通过id查询课次学生表（内部调用）
   *
   * @param id id
   * @return R<BClassTimeStudentDTO>
   */
  @GetMapping("/inner/{id}")
  @Inner
  public R<BClassTimeStudentDTO> getByIdInner(@PathVariable Long id) {
    BClassTimeStudent entity = classTimeStudentService.getById(id);
    if (entity == null) {
      return R.failed("未找到对应的学生课次记录");
    }

    BClassTimeStudentDTO dto = new BClassTimeStudentDTO();
    BeanUtils.copyProperties(entity, dto);
    return R.ok(dto);
  }

  /**
   * 新增课次学生表
   *
   * @param bClassTimeStudentDTO 课次学生表
   * @return R
   */
  @PostMapping
  @Operation(summary = "新增课次学生表", description = "新增课次学生表")
  @StorePermission
  public R add(@Validated(V_A.class) @RequestBody BClassTimeStudentDTO bClassTimeStudentDTO) {
    return R.ok(classTimeStudentService.add(bClassTimeStudentDTO));
  }

  /**
   * 修改课次学生表
   *
   * @param bClassTimeStudentDTO 课次学生表
   * @return R
   */
  @PutMapping
  @Operation(summary = "修改课次学生表", description = "修改课次学生表")
  @StorePermission
  public R edit(@Validated(V_E.class) @RequestBody BClassTimeStudentDTO bClassTimeStudentDTO) {
    return R.ok(classTimeStudentService.edit(bClassTimeStudentDTO));
  }

  /**
   * 通过id删除课次学生表
   *
   * @param ids id列表
   * @return R
   */
  @DeleteMapping
  @Operation(summary = "删除课次学生表", description = "删除课次学生表")
  @StorePermission
  public R delete(@RequestBody Long[] ids) {
    return R.ok(classTimeStudentService.removeBatchByIds(CollUtil.toList(ids)));
  }

  /**
   * 课表签到考勤入口获取学生列表
   *
   * @param lessonno
   * @return com.yuedu.ydsf.common.core.util.R<com.yuedu.ydsf.eduConnect.jw.api.vo.CheckInStudentVO>
   * <AUTHOR>
   * @date 2025/2/25 10:38
   */
  @Operation(summary = "课表签到考勤入口获取学生列表", description = "课表签到考勤入口获取学生列表")
  @GetMapping("/getCheckInStudent/{lessonno}/{entryType}")
  @StorePermission
  @Lock4j(keys = {"#lessonno"})
  public R<CheckInStudentVO> getCheckInStudent(
      @PathVariable Serializable lessonno,
      @PathVariable @Schema(description = "入口类型:1-课表签到考勤入口;2-考勤管理入口") Integer entryType) {
    return R.ok(
        classTimeStudentService.getCheckInStudent(
            lessonno, entryType, StoreContextHolder.getStoreId()));
  }


    /**
     * Live获取出勤学生列表
     * <AUTHOR>
     * @date 2025/7/9 15:00
     * @param lessonno
     * @param entryType
     * @return com.yuedu.ydsf.common.core.util.R<com.yuedu.ydsf.eduConnect.jw.api.vo.CheckInStudentVO>
     */
    @GetMapping("/getCheckInStudent/{lessonno}/{entryType}/{storeId}")
    @Inner
    public R<CheckInStudentVO> getCheckInStudentLive(
        @PathVariable Serializable lessonno,
        @PathVariable Integer entryType,
        @PathVariable Long storeId) {
        return R.ok(
            classTimeStudentService.getCheckInStudentByTimeableId(
                lessonno, entryType, storeId));
    }

  /**
   * 正常考勤签到
   *
   * @param bClassTimeStudentDTO
   * @return com.yuedu.ydsf.common.core.util.R
   * <AUTHOR>
   * @date 2025/2/26 9:45
   */
  @Operation(summary = "签到考勤", description = "签到考勤")
  @PostMapping("/chckin")
  @StorePermission
  public R chckin(
      @Validated(BClassTimeStudentValidGroup.CheckIn.class) @RequestBody
          BClassTimeStudentDTO bClassTimeStudentDTO) {
    return classTimeStudentService.checkIn(bClassTimeStudentDTO);
  }

  /**
   * 补签考勤
   *
   * @param bClassTimeStudentDTO
   * @return com.yuedu.ydsf.common.core.util.R
   * <AUTHOR>
   * @date `2025/2/26` 10:57
   */
  @Operation(summary = "补签考勤", description = "补签考勤")
  @PostMapping("/chckinReissue")
  @StorePermission
  public R chckinReissue(
      @Validated(BClassTimeStudentValidGroup.CheckIn.class) @RequestBody
          BClassTimeStudentDTO bClassTimeStudentDTO) {
    return classTimeStudentService.chckinReissue(bClassTimeStudentDTO, true);
  }

  /** 取消考勤 */
  @Operation(summary = "取消考勤", description = "取消考勤")
  @PostMapping("/cancelCheckIn")
  @StorePermission
  public R cancelCheckIn(
      @Validated(BClassTimeStudentValidGroup.CheckIn.class) @RequestBody
          BClassTimeStudentDTO bClassTimeStudentDTO) {
    log.info("取消考勤参数:{}", bClassTimeStudentDTO);
    return classTimeStudentService.cancelCheckIn(bClassTimeStudentDTO);
  }

  /**
   * 添加补签考勤
   *
   * @param bClassTimeStudentDTO
   * @return com.yuedu.ydsf.common.core.util.R
   * <AUTHOR>
   * @date 2025/2/26 20:02
   */
  @Operation(summary = "添加补签考勤", description = "添加补签考勤")
  @PostMapping("/addChckinReissue")
  @StorePermission
  public R addChckinReissue(
      @Validated(BClassTimeStudentValidGroup.AddChckinReissue.class) @RequestBody
          BClassTimeStudentDTO bClassTimeStudentDTO) {
    return classTimeStudentService.addChckinReissue(bClassTimeStudentDTO);
  }

  /**
   * 考勤管理列表
   *
   * @param
   * @return
   *     com.yuedu.ydsf.common.core.util.R<com.baomidou.mybatisplus.extension.plugins.pagination.Page
   *     < com.yuedu.ydsf.eduConnect.jw.api.vo.AttendanceManagementVO>>
   * <AUTHOR>
   * @date 2025/2/26 17:42
   */
  @Operation(summary = "考勤管理列表", description = "考勤管理列表")
  @GetMapping("/attendanceManagementList")
  @StorePermission
  public R<Page<AttendanceManagementVO>> attendanceManagementList(
      @ParameterObject Page page, @ParameterObject BClassTimeStudentQuery classTimeStudentQuery) {
    Page<AttendanceManagementVO> targetVOPage =
        classTimeStudentService.attendanceManagementList(page, classTimeStudentQuery);
    return R.ok(targetVOPage);
  }

  /**
   * 根据课次编号获取已出勤学生列表
   *
   * @param lessonNo 课次编号
   * @return 已出勤学生列表
   */
  @GetMapping("/getCheckedInStudentsByLessonNo/{lessonNo}/{storeId}")
  @Inner
  R<List<StudentCheckInInfoVO>> getCheckedInStudentsByLessonNo(
      @PathVariable("lessonNo") Long lessonNo, @PathVariable("storeId") Long storeId) {
    return R.ok(classTimeStudentService.getBoundClickerStudentsByLessonNo(lessonNo, storeId));
  }

  /**
   * 批量根据lessonNo获取对应的出勤人数
   *
   * @param bClassTimeStudentDTO
   * @return com.yuedu.ydsf.common.core.util.R<java.util.List <
   *     com.yuedu.ydsf.eduConnect.jw.api.vo.CheckInStudentVO.StudentCheckInInfoVO>>
   * <AUTHOR>
   * @date 2025/3/19 9:12
   */
  @PostMapping("/getCheckedInStudentsByLessonNoBatch")
  @Inner
  R<List<StudentCheckInInfoVO>> getCheckedInStudentsByLessonNoBatch(
      @Validated(BClassTimeStudentValidGroup.GetCheckedInStudentsByLessonNoBatch.class) @RequestBody
          BClassTimeStudentDTO bClassTimeStudentDTO) {
    return R.ok(classTimeStudentService.getCheckedInStudentsByLessonNoBatch(bClassTimeStudentDTO));
  }

  /**
   * 根据课次编号获取学生详情列表包含总人数（未删除未调出）、已出勤人数、总人数（未删除未调出）List
   *
   * @param lessonNo 课次编号
   * @return 已出勤学生列表
   */
  @GetMapping("/getStudentsInfoByLessonNo/{lessonNo}/{storeId}")
  @Inner
  R<CheckInStudentVO> getStudentsInfoByLessonNo(
      @PathVariable("lessonNo") Long lessonNo, @PathVariable("storeId") Long storeId) {
    return R.ok(
        classTimeStudentService.getCheckInStudent(
            lessonNo, CheckInEntryTypeEnum.CHECK_IN_ENTRY_TYPE_2.code, storeId));
  }

  /**
   * 更新学生绑定的答题器
   *
   * <AUTHOR>
   * @date 2025/4/11 9:53
   * @return com.yuedu.ydsf.common.core.util.R
   */
  @PutMapping("/updateStudentClicker")
  @Inner
  R updateStudentClicker(@RequestBody BClassTimeStudentDTO bClassTimeStudentDTO) {
    bInteractionReceiverClickerService.updateStudentClicker(bClassTimeStudentDTO);
    return R.ok();
  }

  /**
   * 绑定答题器
   *
   * <AUTHOR>
   * @date 2025/4/11 14:35
   * @param bClassTimeStudentDTO
   * @return com.yuedu.ydsf.common.core.util.R
   */
  @PostMapping("/bindClicker")
  @Operation(summary = "绑定答题器", description = "绑定答题器")
  @StorePermission
  R bindClicker(
      @Validated(BClassTimeStudentValidGroup.BindClicker.class) @RequestBody
          BClassTimeStudentDTO bClassTimeStudentDTO) {
    return classTimeStudentService.bindClicker(bClassTimeStudentDTO);
  }

  /**
   * 解绑答题器
   *
   * <AUTHOR>
   * @date 2025/4/11 14:35
   * @param bClassTimeStudentDTO
   * @return com.yuedu.ydsf.common.core.util.R
   */
  @PostMapping("/unBindClicker")
  @Operation(summary = "解绑答题器", description = "解绑答题器")
  @StorePermission
  R unBindClicker(
      @Validated(BClassTimeStudentValidGroup.UnBindClicker.class) @RequestBody
          BClassTimeStudentDTO bClassTimeStudentDTO) {
    return classTimeStudentService.unBindClicker(bClassTimeStudentDTO);
  }

  /**
   * 批量解绑答题器
   *
   * <AUTHOR>
   * @date 2025/5/7 8:53
   * @param bClassTimeStudentDTO
   * @return com.yuedu.ydsf.common.core.util.R
   */
  @PostMapping("/batchUnBindClicker")
  @Operation(summary = "批量解绑答题器", description = "批量解绑答题器")
  @StorePermission
  @Idempotent(key = "'batchUnBindClicker:' + #bClassTimeStudentDTO.lessonNo", expireTime = 10, info = "批量解绑答题器操作进行中，请稍后再试")
  R batchUnBindClicker(
      @Validated(BClassTimeStudentValidGroup.BatchUnBindClicker.class) @RequestBody
          BClassTimeStudentDTO bClassTimeStudentDTO) {
    classTimeStudentService.batchUnBindClicker(bClassTimeStudentDTO);
    return R.ok();
  }

  /**
   * 批量绑定答题器
   *
   * <AUTHOR>
   * @date 2025/7/8
   * @param bClassTimeStudentDTO
   * @return com.yuedu.ydsf.common.core.util.R<BatchBindClickerResultVO>
   */
  @PostMapping("/batchBindClicker")
  @Operation(summary = "批量绑定答题器", description = "批量绑定答题器")
  @StorePermission
  @Idempotent(key = "'batchBindClicker:' + #bClassTimeStudentDTO.lessonNo", expireTime = 10, info = "答题器批量绑定中，请稍后重试")
  R<BatchBindClickerResultVO> batchBindClicker(
      @Validated(BClassTimeStudentValidGroup.BatchBindClicker.class) @RequestBody
          BClassTimeStudentDTO bClassTimeStudentDTO) {
    bClassTimeStudentDTO.setStoreId(StoreContextHolder.getStoreId());
    return classTimeStudentService.batchBindClicker(bClassTimeStudentDTO);
  }

  /**
   * 学生签到预校验
   *
   * @param preValidationDTO 预校验请求参数
   * @return 预校验结果
   * <AUTHOR>
   * @date 2025/12/26
   */
  @PostMapping("/preValidateCheckIn")
  @Operation(summary = "学生签到预校验", description = "在学生签到前进行课程报名和剩余课次校验")
  @StorePermission
  public R<ClassStudentErrorVO> preValidateCheckIn(
      @Validated(BClassTimeStudentValidGroup.PreValidateCheckIn.class) @RequestBody
          StudentCheckInPreValidationDTO preValidationDTO) {
    return classTimeStudentService.preValidateCheckIn(preValidationDTO);
  }

  /**
   * 检查答题器解绑提示
   * 判断课次中是否有签到过的学员，没有则返回是否需要弹框提示解绑答题器
   *
   * @param lessonNo 课次号
   * @return 答题器解绑提示信息
   * <AUTHOR>
   * @date 2025/06/17
   */
  @GetMapping("/checkClickerUnbindPrompt/{lessonNo}")
  @Operation(summary = "检查答题器解绑提示", description = "判断课次中是否有签到过的学员，没有则返回是否需要弹框提示解绑答题器")
  @StorePermission
  public R<ClickerUnbindPromptVO> checkClickerUnbindPrompt(@PathVariable Long lessonNo) {
    log.info("检查答题器解绑提示, lessonNo: {}", lessonNo);
    return R.ok(classTimeStudentService.checkClickerUnbindPrompt(lessonNo));
  }



  /**
   * 添加临加学员
   *
   * @param temporaryStudentDTO 临加学员参数
   * @return 操作结果
   * <AUTHOR>
   * @date 2025/06/19
   */
  @PostMapping("/addTemporaryStudents")
  @Operation(summary = "添加临加学员", description = "将选择的在读学员添加到指定课次的签到考勤列表中")
  @StorePermission
  public R addTemporaryStudents(
      @Validated(BClassTimeStudentValidGroup.AddTemporaryStudents.class) @RequestBody
          TemporaryStudentDTO temporaryStudentDTO) {
    log.info("添加临加学员, 参数: {}", temporaryStudentDTO);
    return classTimeStudentService.addTemporaryStudents(temporaryStudentDTO);
  }

  /**
   * 移除临加学员
   *
   * @param temporaryStudentDTO 临加学员参数
   * @return 操作结果
   * <AUTHOR>
   * @date 2025/06/19
   */
  @DeleteMapping("/removeTemporaryStudent")
  @Operation(summary = "移除临加学员", description = "移除未签到的临加学员")
  @StorePermission
  public R removeTemporaryStudent(
      @Validated(BClassTimeStudentValidGroup.RemoveTemporaryStudent.class) @RequestBody
          TemporaryStudentDTO temporaryStudentDTO) {
    log.info("移除临加学员, 参数: {}", temporaryStudentDTO);
    return classTimeStudentService.removeTemporaryStudent(temporaryStudentDTO);
  }

  /**
   * 考勤确认列表
   *
   * @param lessonno 课次编号
   * @return 考勤确认列表，包含预考勤状态
   * <AUTHOR>
   * @date 2025/07/08
   */
  @Operation(summary = "考勤确认列表", description = "获取课次下出勤学生列表，并判断每个学生的预考勤状态（是否产生互动数据且课消校验正常）")
  @GetMapping("/getAttendanceConfirmationList/{lessonno}")
  @StorePermission
  @Lock4j(keys = {"#lessonno"})
  public R<CheckInStudentVO> getAttendanceConfirmationList(@PathVariable Serializable lessonno) {
    return R.ok(
        classTimeStudentService.getAttendanceConfirmationList(
            lessonno, StoreContextHolder.getStoreId()));
  }
  /**
   * 批量确认考勤
   *
   * @param confirmationDTO 考勤确认请求参数
   * @return 考勤确认结果
   * <AUTHOR>
   * @date 2025/07/09
   */
  @PostMapping("/confirmAttendance")
  @Operation(summary = "批量确认考勤", description = "批量处理学员考勤确认操作，支持签到、取消考勤、补签等操作")
  @StorePermission
  @Idempotent(key = "#confirmationDTO.lessonNo", expireTime = 5, info = "确认考勤操作进行中，请稍后再试")
  public R<AttendanceConfirmationResultVO> confirmAttendance(
      @Validated(BClassTimeStudentValidGroup.AttendanceConfirmation.class) @RequestBody
          AttendanceConfirmationDTO confirmationDTO) {
    log.info("批量确认考勤请求, 参数: {}", confirmationDTO);
    return classTimeStudentService.confirmAttendanceBatch(confirmationDTO);
  }
  /**
   * 向已出勤学生发送通知
   *
   * @param lessonno 课次编号
   * @return 发送结果
   * @date 2025/07/14
   */
  @PostMapping("/sendNotificationToAttendedStudents/{lessonno}")
  @Operation(summary = "向已出勤学生发送通知", description = "查询指定课次的已出勤学生并发送微信通知，防止重复发送")
  @StorePermission
  @Idempotent(key = "#lessonno", expireTime = 3, info = "发送通知操作进行中，请稍后再试")
  public R sendNotificationToAttendedStudents(@PathVariable Long lessonno) {
    log.info("向已出勤学生发送通知请求, lessonno: {}", lessonno);
    try {
      return classTimeStudentService.sendNotificationToAttendedStudents(lessonno, StoreContextHolder.getStoreId());
    } catch (Exception e) {
      log.error("向已出勤学生发送通知失败, lessonno: {}, error: {}", lessonno, e.getMessage(), e);
      return R.failed("发送通知失败: " + e.getMessage());
    }
  }


}
