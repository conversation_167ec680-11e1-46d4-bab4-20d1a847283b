package com.yuedu.ydsf.eduConnect.jw.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ArrayUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.nacos.shaded.com.google.common.collect.Maps;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yuedu.permission.api.vo.LecturerInfoVO;
import com.yuedu.store.api.feign.RemoteClassStudentService;
import com.yuedu.store.vo.ClassRoomVO;
import com.yuedu.store.vo.ClassStudentVO;
import com.yuedu.store.vo.ClassVO;
import com.yuedu.store.vo.EmployeeVO;
import com.yuedu.teaching.api.feign.RemoteCourseAuthStoreService;
import com.yuedu.teaching.api.feign.RemoteCourseService;
import com.yuedu.teaching.entity.LessonEntity;
import com.yuedu.teaching.vo.CourseVO;
import com.yuedu.teaching.vo.LessonVO;
import com.yuedu.teaching.vo.StageVO;
import com.yuedu.ydsf.common.core.exception.BizException;
import com.yuedu.ydsf.common.core.util.R;
import com.yuedu.ydsf.common.operatelog.annotation.OperateLog;
import com.yuedu.ydsf.common.security.util.StoreContextHolder;
import com.yuedu.ydsf.eduConnect.api.constant.CheckInStatusEnum;
import com.yuedu.ydsf.eduConnect.api.constant.OperateCategoryEnum;
import com.yuedu.ydsf.eduConnect.api.constant.OperateTypeEnum;
import com.yuedu.ydsf.eduConnect.jw.api.dto.TimetableChangeDTO;
import com.yuedu.ydsf.eduConnect.jw.api.query.TimetableChangeQuery;
import com.yuedu.ydsf.eduConnect.jw.api.query.TimetableChangeSourceQuery;
import com.yuedu.ydsf.eduConnect.jw.api.query.TimetableChangeTargetQuery;
import com.yuedu.ydsf.eduConnect.jw.api.vo.StudentTimetableChangeVO;
import com.yuedu.ydsf.eduConnect.jw.api.vo.TimetableChangeSourceVO;
import com.yuedu.ydsf.eduConnect.jw.api.vo.TimetableChangeTargetVO;
import com.yuedu.ydsf.eduConnect.jw.api.vo.TimetableChangeVO;
import com.yuedu.ydsf.eduConnect.jw.entity.BClassTimeStudent;
import com.yuedu.ydsf.eduConnect.jw.entity.Timetable;
import com.yuedu.ydsf.eduConnect.jw.entity.TimetableChange;
import com.yuedu.ydsf.eduConnect.jw.manager.ClassTimeStudentManager;
import com.yuedu.ydsf.eduConnect.jw.manager.TimetableChangeManager;
import com.yuedu.ydsf.eduConnect.jw.mapper.TimetableChangeMapper;
import com.yuedu.ydsf.eduConnect.jw.mapper.TimetableMapper;
import com.yuedu.ydsf.eduConnect.jw.service.TimetableChangeService;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.time.format.TextStyle;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.CompletableFuture;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * 调课记录表服务层
 *
 * <AUTHOR>
 * @date 2025/02/12
 */
@Service
@Slf4j
@AllArgsConstructor
public class TimetableChangeServiceImpl extends ServiceImpl<TimetableChangeMapper, TimetableChange>
    implements TimetableChangeService {

    private final TimetableMapper timetableMapper;
    private final TimetableChangeMapper timetableChangeMapper;
    private final RemoteClassStudentService remoteClassStudentService;
    private final ClassTimeStudentManager classTimeStudentManager;
    private final TimetableChangeManager timetableChangeManager;
    private final RemoteCourseAuthStoreService remoteCourseAuthStoreService;
    private final RemoteCourseService remoteCourseService;

    /**
     * 调课记录表分页查询
     *
     * @param page                  分页对象
     * @param bTimetableChangeQuery 调课记录表
     * @return IPage 分页结果
     */
    @Override
    public IPage page(Page page, TimetableChangeQuery bTimetableChangeQuery) {
        return page(page, Wrappers.lambdaQuery());
    }

    /**
     * 新增调课记录表
     *
     * @param bTimetableChangeDTO 调课记录表
     * @return boolean 执行结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean add(TimetableChangeDTO bTimetableChangeDTO) {
        log.info("开始新增调课记录, 请求参数: {}", JSON.toJSONString(bTimetableChangeDTO));
        try {
            // 校验调课的课程学生有没有再原课程签过到
            classTimeStudentManager.checkStudentCheckInStatus(bTimetableChangeDTO);
            // 1. 保存调课记录
            TimetableChange bTimetableChange = new TimetableChange();
            BeanUtils.copyProperties(bTimetableChangeDTO, bTimetableChange);
            boolean saveResult = save(bTimetableChange);
            if (!saveResult) {
                log.error("保存调课记录失败");
                return false;
            }
            // 调课处理课次与学生的关系
            classTimeStudentManager.handleTimetableChangeData(bTimetableChangeDTO);
            return true;

        } catch (BizException bizException) {
            throw bizException;
        } catch (Exception e) {
            log.error("新增调课记录异常", e);
            throw new RuntimeException("新增调课记录失败", e);
        }
    }

    /**
     * 修改调课记录表
     *
     * @param bTimetableChangeDTO 调课记录表
     * @return boolean 执行结果
     */
    @Override
    public boolean edit(TimetableChangeDTO bTimetableChangeDTO) {
        TimetableChange bTimetableChange = new TimetableChange();
        BeanUtils.copyProperties(bTimetableChangeDTO, bTimetableChange);
        return updateById(bTimetableChange);
    }

    /**
     * 导出excel 调课记录表表格
     *
     * @param bTimetableChangeQuery 查询条件
     * @param ids                   导出指定ID
     * @return List<BTimetableChangeVO> 结果集合
     */
    @Override
    public List<TimetableChangeVO> export(TimetableChangeQuery bTimetableChangeQuery, Long[] ids) {
        return list(
            Wrappers.<TimetableChange>lambdaQuery()
                .in(ArrayUtil.isNotEmpty(ids), TimetableChange::getId, ids))
            .stream()
            .map(
                entity -> {
                    TimetableChangeVO bTimetableChangeVO = new TimetableChangeVO();
                    BeanUtils.copyProperties(entity, bTimetableChangeVO);
                    return bTimetableChangeVO;
                })
            .toList();
    }

    @Override
    public Page<TimetableChangeSourceVO> getSourceLessonList(
        Page page, TimetableChangeSourceQuery query) {
        log.info(
            "开始获取源课程列表, 请求参数: {}, 分页参数: current={}, size={}",
            JSON.toJSONString(query),
            page.getCurrent(),
            page.getSize());

        try {
            List<Long> validLessonNos = new ArrayList<>();
            Map<Long, LocalDateTime> enrollmentDateByClassId = new HashMap<>();
            List<Integer> classIdsForStudent = Lists.newArrayList();
            Map<Long, Boolean> lessonNoValidityMap = Maps.newHashMap();

            List<Long> authCourseIdList = List.of();
            if (Objects.nonNull(query.getCourseType())) {
                R<List<Long>> listAuthCourseIds = remoteCourseAuthStoreService.listAuthCourseIds(
                    query.getCourseType().longValue(), StoreContextHolder.getStoreId());
                if (listAuthCourseIds.isOk()) {
                    if (CollUtil.isNotEmpty(listAuthCourseIds.getData())) {
                        authCourseIdList = listAuthCourseIds.getData();
                    } else {
                        return new Page<>(page.getCurrent(), page.getSize());
                    }
                } else {
                    log.warn("获取授权课程ID失败,降级处理: {}", listAuthCourseIds.getMsg());
                }

            }
            // 1.1 获取学员所在班级的课程
            R<List<ClassStudentVO>> classIdsResult =
                remoteClassStudentService.getClassByStudentId(query.getStudentId().intValue());
            if (classIdsResult.isOk() && CollUtil.isNotEmpty(classIdsResult.getData())) {

                List<ClassStudentVO> classStudentVOs = classIdsResult.getData();
                for (ClassStudentVO csVO : classStudentVOs) {
                    Integer classId = csVO.getClassId();
                    LocalDateTime enrollmentDate = csVO.getCreateTime();

                    if (classId != null && enrollmentDate != null) {
                        enrollmentDateByClassId.put(classId.longValue(), enrollmentDate);
                        classIdsForStudent.add(classId);

                        List<Timetable> timetablesInClass = timetableMapper.selectList(
                            Wrappers.<Timetable>lambdaQuery().eq(Timetable::getClassId, classId)
                                .in(CollectionUtils.isNotEmpty(authCourseIdList),
                                    Timetable::getCourseId,
                                    authCourseIdList));

                        List<Timetable> relevantTimetables = timetablesInClass.stream()
                            .filter(
                                tt -> tt.getClassEndDateTime() != null && !tt.getClassEndDateTime()
                                    .isBefore(enrollmentDate))
                            .toList();

                        validLessonNos.addAll(
                            relevantTimetables.stream().map(Timetable::getLessonNo).toList());
                        log.info("学员 {}, 班级 {}(入班时间 {}): 筛选后加入 {} 个课次号 (原 {} 个)",
                            query.getStudentId(), classId, enrollmentDate,
                            relevantTimetables.size(), timetablesInClass.size());

                    } else {
                        log.warn(
                            "学员 {} 的班级信息不完整 (classId: {}, enrollmentDate: {}), 跳过处理",
                            query.getStudentId(), classId, enrollmentDate);
                    }
                }
                log.info("学员 {} 根据入班时间初步筛选后, validLessonNos 数量: {}, 涉及班级IDs: {}",
                    query.getStudentId(), validLessonNos.size(),
                    JSON.toJSONString(classIdsForStudent));
            } else {
                log.info("学员不在任何班级中或获取班级信息失败, studentId: {}",
                    query.getStudentId());
            }

            // 1.2 获取该学员的所有调课记录并分析有效课次
            List<TimetableChange> allChanges =
                timetableChangeMapper.selectList(
                    Wrappers.lambdaQuery(TimetableChange.class)
                        .eq(TimetableChange::getStudentId, query.getStudentId())
                        .orderByAsc(TimetableChange::getCreateTime));
            log.info("查询到学员调课记录数量: {}", allChanges.size());

            if (!allChanges.isEmpty()) {
                // 分析调课链，找出有效的课次号
                lessonNoValidityMap = analyzeTimetableChanges(allChanges);

                // 添加有效的调课课次号
                lessonNoValidityMap.forEach(
                    (lessonNo, isValid) -> {
                        if (isValid) {
                            validLessonNos.add(lessonNo);
                        } else {
                            validLessonNos.remove(lessonNo); // 移除无效的课次号
                        }
                    });
                log.info("分析调课记录后的有效课次号数量: {}", validLessonNos.size());
            }

            // 如果既不在班级中也没有有效的调课记录
            if (validLessonNos.isEmpty()) {
                log.info("学员没有任何有效课程, studentId: {}", query.getStudentId());
                return new Page<>(page.getCurrent(), page.getSize());
            }

            // 4. 获取学生所在班级的所有课程
            List<Timetable> allClassTimetables = Lists.newArrayList();
            if (CollectionUtils.isNotEmpty(classIdsForStudent)) {
                List<Timetable> rawAllClassTimetables =
                    timetableMapper.selectList(
                        Wrappers.<Timetable>lambdaQuery()
                            .in(Timetable::getClassId, classIdsForStudent)
                            .in(CollectionUtils.isNotEmpty(authCourseIdList),
                                Timetable::getCourseId,
                                authCourseIdList));

                allClassTimetables = rawAllClassTimetables.stream().filter(tt -> {
                    LocalDateTime enrollmentDate = enrollmentDateByClassId.get(tt.getClassId());
                    if (enrollmentDate == null) {
                        log.warn(
                            "在构建 allClassTimetables 时未找到班级 {} 的学员入班时间, 学员ID: {}. 课表ID {} (课次号 {}) 可能未被正确过滤.",
                            tt.getClassId(), query.getStudentId(), tt.getId(), tt.getLessonNo());
                        return true;
                    }
                    return tt.getClassEndDateTime() != null && !tt.getClassEndDateTime()
                        .isBefore(enrollmentDate);
                }).collect(Collectors.toList());
                log.info(
                    "为学员 {} 构建 allClassTimetables: 筛选后 {} 条 (原始 {} 条), 班级IDs: {}",
                    query.getStudentId(), allClassTimetables.size(), rawAllClassTimetables.size(),
                    JSON.toJSONString(classIdsForStudent));
            }

            // 4.5. 获取学员临时加入的课次
            List<Long> temporaryJoinedLessonNos = classTimeStudentManager.selectTemporaryJoinedLessonNos(
                query.getStudentId(), StoreContextHolder.getStoreId());
            log.info("学员临时加入的课次数量: {}, lessonNos: {}", temporaryJoinedLessonNos.size(), temporaryJoinedLessonNos);

            // 查询临时加入课次的课表信息
            List<Timetable> temporaryTimetables = Lists.newArrayList();
            if (CollectionUtils.isNotEmpty(temporaryJoinedLessonNos)) {
                temporaryTimetables = timetableMapper.selectList(
                    Wrappers.<Timetable>lambdaQuery()
                        .in(Timetable::getLessonNo, temporaryJoinedLessonNos)
                        .in(CollectionUtils.isNotEmpty(authCourseIdList),
                            Timetable::getCourseId,
                            authCourseIdList));
                log.info("查询到临时加入课次的课表数量: {}", temporaryTimetables.size());
            }

            // 合并班级课程和临时加入的课程
            List<Timetable> allTimetables = Lists.newArrayList();
            allTimetables.addAll(allClassTimetables);
            allTimetables.addAll(temporaryTimetables);
            log.info("合并后的总课程数量: {} (班级课程: {}, 临时加入课程: {})",
                allTimetables.size(), allClassTimetables.size(), temporaryTimetables.size());

            // 5. 筛选有效的课程
            List<Timetable> validTimetables =
                filterValidTimetables(allTimetables, lessonNoValidityMap,
                    query.getLessonName());
            log.info("筛选后的有效课程数量: {}", validTimetables.size());

            // 6. 进一步筛选：只保留学生未考勤签到的课程
            List<Timetable> unCheckedInTimetables = filterUnCheckedInTimetables(validTimetables,
                query.getStudentId());
            log.info("筛选未考勤课程后的数量: {}", unCheckedInTimetables.size());

            // 7. 手动分页
            int start = (int) ((page.getCurrent() - 1) * page.getSize());
            int end = Math.min(start + (int) page.getSize(), unCheckedInTimetables.size());
            List<Timetable> pagedTimetables =
                unCheckedInTimetables.subList(
                    Math.min(start, unCheckedInTimetables.size()),
                    Math.min(end, unCheckedInTimetables.size()));

            // 8. 获取关联数据
            Map<Integer, CourseVO> courseVOMap = timetableChangeManager.fetchCourseVOMap(
                pagedTimetables);
            Map<Integer, StageVO> stageVOMap = timetableChangeManager.fetchStageVOMap();
            Map<String, LessonVO> lessonVOMap = timetableChangeManager.fetchLessonVOMap(
                pagedTimetables);
            Map<Long, ClassRoomVO> classroomVOMap =
                timetableChangeManager.fetchClassroomVOMap(pagedTimetables);
            Map<Long, EmployeeVO> teacherVOMap =
                timetableChangeManager.fetchTeacherVOMap(pagedTimetables);
            Map<Long, LecturerInfoVO> lecturerInfoVOMap = timetableChangeManager.fetchLecturerInfoVOMap();
            Map<Long, ClassVO> classVOMap = timetableChangeManager.fetchClassVOMap(pagedTimetables);

            // 9. 转换为VO对象
            List<TimetableChangeSourceVO> resultList =
                pagedTimetables.stream()
                    .map(
                        timetable ->
                            populateTimetableChangeSourceVO(
                                timetable,
                                courseVOMap,
                                stageVOMap,
                                lessonVOMap,
                                classroomVOMap,
                                teacherVOMap,
                                lecturerInfoVOMap,
                                classVOMap))
                    .collect(Collectors.toList());

            // 10. 构建分页结果
            Page<TimetableChangeSourceVO> resultPage =
                new Page<>(page.getCurrent(), page.getSize(), unCheckedInTimetables.size());
            resultPage.setRecords(resultList);

            log.info("源课程列表获取完成，总记录数: {}, 当前页记录数: {}", resultPage.getTotal(),
                resultList.size());
            return resultPage;

        } catch (Exception e) {
            log.error("获取源课程列表异常", e);
            return new Page<>(page.getCurrent(), page.getSize());
        }
    }

    /**
     * 分析调课记录，返回课次号的有效性映射
     *
     * @param changes 调课记录列表
     * @return Map<课次号, 是否有效>
     */
    private Map<Long, Boolean> analyzeTimetableChanges(List<TimetableChange> changes) {
        Map<Long, Boolean> lessonNoValidityMap = new HashMap<>();

        // 按照时间顺序处理每条调课记录
        for (TimetableChange change : changes) {
            // 原课程标记为无效（被调出）
            lessonNoValidityMap.put(change.getSourceLessonNo(), false);
            // 目标课程标记为有效（被调入）
            lessonNoValidityMap.put(change.getTargetLessonNo(), true);
        }

        return lessonNoValidityMap;
    }

    /**
     * 筛选有效的课程
     *
     * @param allTimetables       班级课程列表
     * @param lessonNoValidityMap 课次号的有效性映射
     * @param lessonName          课节名称条件
     * @return 筛选后的有效课程列表
     */
    private List<Timetable> filterValidTimetables(
        List<Timetable> allTimetables, Map<Long, Boolean> lessonNoValidityMap, String lessonName) {
        log.info(
            "开始筛选有效课程, 班级课程数: {}, 调课记录数: {}, 课节名称条件: {}",
            allTimetables.size(),
            lessonNoValidityMap.size(),
            lessonName);

        try {
            // 1. 获取所有需要查询的课程
            List<Timetable> allValidTimetables = new ArrayList<>(allTimetables);

            // 2. 如果有调课记录，查询相关的课程信息
            if (!lessonNoValidityMap.isEmpty()) {
                List<Long> validLessonNos =
                    lessonNoValidityMap.entrySet().stream()
                        .filter(Entry::getValue) // 只获取有效的课次号
                        .map(Entry::getKey)
                        .collect(Collectors.toList());

                if (!validLessonNos.isEmpty()) {
                    List<Timetable> timetablesFromChanges =
                        timetableMapper.selectList(
                            Wrappers.<Timetable>lambdaQuery()
                                .in(Timetable::getLessonNo, validLessonNos));

                    log.info("从调课记录中获取到的有效课程数: {}", timetablesFromChanges.size());
                    allValidTimetables.addAll(timetablesFromChanges);
                }
            }

            // 3. 如果有课节名称搜索条件，获取符合条件的课节信息
            List<LessonEntity> matchedLessons = Collections.emptyList();
            if (StringUtils.isNotBlank(lessonName)) {
                R<List<LessonEntity>> lessonResult = timetableChangeManager.getLessonListByName(
                    lessonName);
                if (!lessonResult.isOk() || CollUtil.isEmpty(lessonResult.getData())) {
                    log.info("未找到包含名称[{}]的课节", lessonName);
                    return Collections.emptyList();
                }
                matchedLessons = lessonResult.getData();
                log.info("查询到符合名称[{}]的课节数量: {}", lessonName, matchedLessons.size());
            }

            // 4. 构建课节匹配条件的Map
            final Map<String, Boolean> lessonMatchMap = new HashMap<>();
            if (!matchedLessons.isEmpty()) {
                matchedLessons.forEach(
                    lesson ->
                        lessonMatchMap.put(lesson.getCourseId() + "_" + lesson.getLessonOrder(),
                            true));
            }

            // 5. 进行过滤和去重
            return allValidTimetables.stream()
                .filter(
                    timetable -> {
                        // 检查课次号的有效性
                        Boolean isValid = lessonNoValidityMap.get(timetable.getLessonNo());
                        if (isValid != null && !isValid) {
                            return false;
                        }

                        // 如果有课节名称搜索条件，检查是否匹配
                        if (!lessonMatchMap.isEmpty()) {
                            String key = timetable.getCourseId() + "_" + timetable.getLessonOrder();
                            return lessonMatchMap.containsKey(key);
                        }

                        return true;
                    })
                // 使用groupingBy根据lessonNo分组，然后获取每组的第一个元素
                .collect(
                    Collectors.groupingBy(
                        Timetable::getLessonNo,
                        Collectors.collectingAndThen(Collectors.toList(), list -> list.get(0))))
                .values()
                .stream()
                .sorted(Comparator.comparing(Timetable::getClassStartDateTime))
                .collect(Collectors.toList());

        } catch (Exception e) {
            log.error("筛选有效课程异常", e);
            return Collections.emptyList();
        }
    }

    /**
     * 筛选学生未考勤签到的课程
     *
     * @param timetables 课程列表
     * @param studentId  学生ID
     * @return 未考勤签到的课程列表
     */
    private List<Timetable> filterUnCheckedInTimetables(List<Timetable> timetables,
        Long studentId) {
        log.info("开始筛选学生未考勤签到的课程, 课程数量: {}, 学生ID: {}", timetables.size(),
            studentId);

        if (CollUtil.isEmpty(timetables)) {
            return Collections.emptyList();
        }

        try {
            // 获取所有课次号
            List<Long> lessonNos = timetables.stream()
                .map(Timetable::getLessonNo)
                .collect(Collectors.toList());

            // 查询学生在这些课次中的考勤记录
            List<BClassTimeStudent> attendanceRecords = classTimeStudentManager.selectListByLessonNo(
                studentId, lessonNos);

            // 构建已签到课次号的Set
            Set<Long> checkedInLessonNos = attendanceRecords.stream()
                .filter(record -> CheckInStatusEnum.CHECK_IN_STATUS_1.code.equals(
                    record.getCheckInStatus()))
                .map(BClassTimeStudent::getLessonNo)
                .collect(Collectors.toSet());

            log.info("学生已签到的课次数量: {}", checkedInLessonNos.size());

            // 筛选出未签到的课程
            List<Timetable> unCheckedInTimetables = timetables.stream()
                .filter(timetable -> !checkedInLessonNos.contains(timetable.getLessonNo()))
                .collect(Collectors.toList());

            log.info("筛选出未签到的课程数量: {}", unCheckedInTimetables.size());
            return unCheckedInTimetables;

        } catch (Exception e) {
            log.error("筛选学生未考勤签到的课程异常, studentId: {}", studentId, e);
            return Collections.emptyList();
        }
    }

    @Override
    public Page<TimetableChangeTargetVO> getTargetLessonList(
        Page page, TimetableChangeTargetQuery query) {
        log.info(
            "开始获取目标课程列表, 请求参数: {}, 分页参数: current={}, size={}",
            JSON.toJSONString(query),
            page.getCurrent(),
            page.getSize());

        try {
            // 1. 查询符合条件的未开始课程 - 使用分页查询
            LocalDateTime now = LocalDateTime.now();

            // 创建分页对象用于数据库查询
            Page<Timetable> timetablePage = new Page<>(page.getCurrent(), page.getSize());

            Page<Timetable> timetablePageResult =
                timetableMapper.selectPage(
                    timetablePage,
                    Wrappers.<Timetable>lambdaQuery()
                        .eq(Timetable::getStoreId, StoreContextHolder.getStoreId())
                        .eq(Timetable::getCourseId, query.getSourceCourseId())
                        .eq(Timetable::getLessonOrder, query.getSourceLessonOrder())
                        .gt(Timetable::getClassEndDateTime, now)
                        .ne(
                            query.getSourceLessonNo() != null,
                            Timetable::getLessonNo,
                            query.getSourceLessonNo()) // 直接在查询中排除源课程
                        .orderByAsc(Timetable::getClassStartDateTime));

            List<Timetable> timetables = timetablePageResult.getRecords();

            if (CollUtil.isEmpty(timetables)) {
                log.info("未查询到符合条件的课程");
                return new Page<>(page.getCurrent(), page.getSize());
            }

            // 2. 获取关联数据 (一次性获取，并转换为 Map)
            Map<Integer, CourseVO> courseVOMap = timetableChangeManager.fetchCourseVOMap(
                timetables);
            Map<Integer, StageVO> stageVOMap = timetableChangeManager.fetchStageVOMap();
            Map<String, LessonVO> lessonVOMap = timetableChangeManager.fetchLessonVOMap(timetables);
            Map<Long, ClassRoomVO> classroomVOMap =
                timetableChangeManager.fetchClassroomVOMap(timetables);
            Map<Long, EmployeeVO> teacherVOMap = timetableChangeManager.fetchTeacherVOMap(
                timetables);
            Map<Long, LecturerInfoVO> longLecturerInfoVOMap =
                timetableChangeManager.fetchLecturerInfoVOMap();
            Map<Long, ClassVO> classVOMap = timetableChangeManager.fetchClassVOMap(timetables);

            // 3. 转换为VO对象并设置属性
            List<TimetableChangeTargetVO> resultList =
                timetables.stream()
                    .map(
                        timetable ->
                            populateTimetableChangeTargetVO(
                                timetable,
                                courseVOMap,
                                stageVOMap,
                                lessonVOMap,
                                classroomVOMap,
                                teacherVOMap,
                                longLecturerInfoVOMap,
                                classVOMap))
                    .collect(Collectors.toList());

            // 5. 构建分页结果
            Page<TimetableChangeTargetVO> resultPage =
                new Page<>(
                    timetablePageResult.getCurrent(),
                    timetablePageResult.getSize(),
                    timetablePageResult.getTotal());
            resultPage.setRecords(resultList);

            log.info("目标课程列表获取完成，总记录数: {}, 当前页记录数: {}", resultPage.getTotal(),
                resultList.size());
            return resultPage;

        } catch (Exception e) {
            log.error("获取目标课程列表异常", e);
            return new Page<>(page.getCurrent(), page.getSize());
        }
    }

    private TimetableChangeSourceVO populateTimetableChangeSourceVO(
        Timetable timetable,
        Map<Integer, CourseVO> courseVOMap,
        Map<Integer, StageVO> stageVOMap,
        Map<String, LessonVO> lessonVOMap,
        Map<Long, ClassRoomVO> classroomVOMap,
        Map<Long, EmployeeVO> teacherVOMap,
        Map<Long, LecturerInfoVO> lecturerInfoVOMap,
        Map<Long, ClassVO> classVOMap) {

        TimetableChangeSourceVO vo = new TimetableChangeSourceVO();
        BeanUtils.copyProperties(timetable, vo);
        populateCommonVOProperties(
            vo,
            timetable,
            courseVOMap,
            stageVOMap,
            lessonVOMap,
            classroomVOMap,
            teacherVOMap,
            lecturerInfoVOMap,
            classVOMap);
        return vo;
    }

    private TimetableChangeTargetVO populateTimetableChangeTargetVO(
        Timetable timetable,
        Map<Integer, CourseVO> courseVOMap,
        Map<Integer, StageVO> stageVOMap,
        Map<String, LessonVO> lessonVOMap,
        Map<Long, ClassRoomVO> classroomVOMap,
        Map<Long, EmployeeVO> teacherVOMap,
        Map<Long, LecturerInfoVO> longLecturerInfoVOMap,
        Map<Long, ClassVO> classVOMap) {

        TimetableChangeTargetVO vo = new TimetableChangeTargetVO();
        BeanUtils.copyProperties(timetable, vo);
        populateCommonVOProperties(
            vo,
            timetable,
            courseVOMap,
            stageVOMap,
            lessonVOMap,
            classroomVOMap,
            teacherVOMap,
            longLecturerInfoVOMap,
            classVOMap);
        return vo;
    }

    private <T> void populateCommonVOProperties(
        T vo, // 使用泛型，可以是 TimetableChangeSourceVO 或 TimetableChangeTargetVO
        Timetable timetable,
        Map<Integer, CourseVO> courseVOMap,
        Map<Integer, StageVO> stageVOMap,
        Map<String, LessonVO> lessonVOMap,
        Map<Long, ClassRoomVO> classroomVOMap,
        Map<Long, EmployeeVO> teacherVOMap,
        Map<Long, LecturerInfoVO> lecturerInfoVOMap,
        Map<Long, ClassVO> classVOMap) {

        // 格式化上课时间为 "2024-11-02 (周五) 08:00-10:00" 格式
        String fullClassTimeStr = null;
        try {
            LocalDateTime classStartDateTime = timetable.getClassStartDateTime();
            LocalDateTime classEndDateTime = timetable.getClassEndDateTime();

            if (classStartDateTime != null && classEndDateTime != null) {
                // 获取日期部分 (2024-11-02)
                String dateStr = classStartDateTime.format(
                    DateTimeFormatter.ofPattern("yyyy-MM-dd"));

                // 获取星期几 (周五)
                String dayOfWeek =
                    classStartDateTime.getDayOfWeek()
                        .getDisplayName(TextStyle.SHORT, Locale.CHINESE);

                // 获取时间部分 (08:00-10:00)
                String startTimeStr = classStartDateTime.format(
                    DateTimeFormatter.ofPattern("HH:mm"));
                String endTimeStr = classEndDateTime.format(DateTimeFormatter.ofPattern("HH:mm"));

                // 组合完整的时间字符串
                fullClassTimeStr =
                    String.format("%s (%s) %s-%s", dateStr, dayOfWeek, startTimeStr, endTimeStr);
            } else {
                log.warn("课程开始或结束时间为空，无法格式化上课时间, timetableId: {}",
                    timetable.getId());
            }
        } catch (Exception e) {
            log.error("格式化上课时间异常, timetableId: {}", timetable.getId(), e);
        }

        // 设置格式化后的上课时间
        if (fullClassTimeStr != null) {
            if (vo instanceof TimetableChangeSourceVO) {
                ((TimetableChangeSourceVO) vo).setFullClassTimeStr(fullClassTimeStr);
            } else if (vo instanceof TimetableChangeTargetVO) {
                ((TimetableChangeTargetVO) vo).setFullClassTimeStr(fullClassTimeStr);
            }
        }

        CourseVO courseVO =
            courseVOMap.getOrDefault(timetable.getCourseId().intValue(), new CourseVO());
        if (vo instanceof TimetableChangeSourceVO) {
            ((TimetableChangeSourceVO) vo)
                .setCourseName(courseVO.getCourseName()); // 假设 CourseVO 中有 courseName 字段
        } else if (vo instanceof TimetableChangeTargetVO) {
            ((TimetableChangeTargetVO) vo).setCourseName(courseVO.getCourseName());
        }

        StageVO stageVO = stageVOMap.getOrDefault(courseVO.getStageId(), new StageVO());
        if (vo instanceof TimetableChangeSourceVO) {
            ((TimetableChangeSourceVO) vo).setStageName(stageVO.getStageName());
        } else if (vo instanceof TimetableChangeTargetVO) {
            ((TimetableChangeTargetVO) vo).setStageName(stageVO.getStageName());
        }

        LessonVO lessonVO =
            lessonVOMap.get(
                timetableChangeManager.generateLessonKey(
                    timetable.getCourseId(), timetable.getLessonOrder()));
        if (lessonVO != null) {
            if (vo instanceof TimetableChangeSourceVO) {
                ((TimetableChangeSourceVO) vo).setLessonName(lessonVO.getLessonName());
            } else if (vo instanceof TimetableChangeTargetVO) {
                ((TimetableChangeTargetVO) vo).setLessonName(lessonVO.getLessonName());
            }
        }

        ClassRoomVO classRoomVO = classroomVOMap.get(timetable.getClassroomId());
        if (classRoomVO != null) {
            if (vo instanceof TimetableChangeSourceVO) {
                ((TimetableChangeSourceVO) vo).setClassroomName(classRoomVO.getClassRoomName());
            } else if (vo instanceof TimetableChangeTargetVO) {
                ((TimetableChangeTargetVO) vo).setClassroomName(classRoomVO.getClassRoomName());
            }
        }

        EmployeeVO teacherVO = teacherVOMap.get(timetable.getTeacherId());
        if (teacherVO != null) {
            if (vo instanceof TimetableChangeSourceVO) {
                ((TimetableChangeSourceVO) vo).setTeacherName(teacherVO.getName());
            } else if (vo instanceof TimetableChangeTargetVO) {
                ((TimetableChangeTargetVO) vo).setTeacherName(teacherVO.getName());
            }
        }

        LecturerInfoVO lecturerInfoVO = lecturerInfoVOMap.get(timetable.getLectureId());
        if (lecturerInfoVO != null) {
            if (vo instanceof TimetableChangeSourceVO) {
                ((TimetableChangeSourceVO) vo).setLecturerName(lecturerInfoVO.getName());
            } else if (vo instanceof TimetableChangeTargetVO) {
                ((TimetableChangeTargetVO) vo).setLecturerName(lecturerInfoVO.getName());
            }
        }
        // 设置班级名称
        ClassVO classVO = classVOMap.get(timetable.getClassId());
        if (classVO != null) {
            if (vo instanceof TimetableChangeSourceVO) {
                ((TimetableChangeSourceVO) vo).setClassName(classVO.getCName());
            } else if (vo instanceof TimetableChangeTargetVO) {
                ((TimetableChangeTargetVO) vo).setClassName(classVO.getCName());
            }
        }
    }

    @Override
    public Page<StudentTimetableChangeVO> getStudentTimetableChangeRecords(
        Page page, Long studentId) {
        log.info(
            "开始查询学生调课记录, studentId: {}, 分页参数: current={}, size={}",
            studentId,
            page.getCurrent(),
            page.getSize());

        try {
            // 1. 查询调课记录
            Page<TimetableChange> changesPage =
                this.page(
                    page,
                    Wrappers.<TimetableChange>lambdaQuery()
                        .eq(TimetableChange::getStudentId, studentId)
                        .orderByDesc(TimetableChange::getCreateTime));

            List<TimetableChange> changes = changesPage.getRecords();
            if (CollUtil.isEmpty(changes)) {
                log.info("未查询到学生调课记录, studentId: {}", studentId);
                return new Page<>(page.getCurrent(), page.getSize(), 0);
            }

            // 2. 获取课表信息
            List<Long> lessonNos =
                changes.stream()
                    .flatMap(
                        change -> Stream.of(change.getSourceLessonNo(), change.getTargetLessonNo()))
                    .collect(Collectors.toList());

            List<Timetable> timetables = getTimetables(lessonNos);
            if (CollUtil.isEmpty(timetables)) {
                return new Page<>(page.getCurrent(), page.getSize(), changesPage.getTotal());
            }

            // 3. 获取关联数据
            Map<String, Object> relatedData = fetchRelatedDataAsync(timetables);

            // 4. 转换为VO对象
            List<StudentTimetableChangeVO> resultList =
                changes.stream()
                    .map(
                        change ->
                            convertToStudentTimetableChangeVO(
                                change,
                                createTimetableMap(timetables),
                                (Map<Integer, CourseVO>) relatedData.get("courseMap"),
                                (Map<String, LessonVO>) relatedData.get("lessonMap"),
                                (Map<Long, ClassRoomVO>) relatedData.get("classroomMap"),
                                (Map<Long, EmployeeVO>) relatedData.get("teacherMap"),
                                (Map<Long, LecturerInfoVO>) relatedData.get("lecturerInfoMap")))
                    .collect(Collectors.toList());

            // 5. 构建分页结果
            Page<StudentTimetableChangeVO> resultPage =
                new Page<>(changesPage.getCurrent(), changesPage.getSize(), changesPage.getTotal());
            resultPage.setRecords(resultList);

            log.info("学生调课记录查询完成, 返回记录数: {}, 总记录数: {}", resultList.size(),
                resultPage.getTotal());
            return resultPage;

        } catch (Exception e) {
            log.error("查询学生调课记录异常, studentId: {}", studentId, e);
            return new Page<>(page.getCurrent(), page.getSize());
        }
    }

    /**
     * 创建课表映射关系
     *
     * @param timetables 课表列表
     * @return 以lessonNo为key的课表Map
     */
    private Map<Long, Timetable> createTimetableMap(List<Timetable> timetables) {
        if (CollUtil.isEmpty(timetables)) {
            return new HashMap<>();
        }

        return timetables.stream()
            .collect(
                Collectors.toMap(
                    Timetable::getLessonNo,
                    Function.identity(),
                    (existing, replacement) -> existing // 保留第一个遇到的值，处理可能的重复键
                ));
    }

    /**
     * 封装获取关联数据的异步任务
     */
    private Map<String, Object> fetchRelatedDataAsync(List<Timetable> timetables) {
        log.info("开始异步获取关联数据");
        try {
            // 创建所有异步任务
            CompletableFuture<Map<Integer, CourseVO>> courseMapFuture =
                CompletableFuture.supplyAsync(
                    () -> timetableChangeManager.fetchCourseVOMap(timetables));
            CompletableFuture<Map<String, LessonVO>> lessonMapFuture =
                CompletableFuture.supplyAsync(
                    () -> timetableChangeManager.fetchLessonVOMap(timetables));
            CompletableFuture<Map<Long, ClassRoomVO>> classroomMapFuture =
                CompletableFuture.supplyAsync(
                    () -> timetableChangeManager.fetchClassroomVOMap(timetables));
            CompletableFuture<Map<Long, EmployeeVO>> teacherMapFuture =
                CompletableFuture.supplyAsync(
                    () -> timetableChangeManager.fetchTeacherVOMap(timetables));
            CompletableFuture<Map<Long, LecturerInfoVO>> lecturerInfoMapFuture =
                CompletableFuture.supplyAsync(
                    () -> timetableChangeManager.fetchLecturerInfoVOMap());
            CompletableFuture<Map<Long, ClassVO>> classMapFuture =
                CompletableFuture.supplyAsync(
                    () -> timetableChangeManager.fetchClassVOMap(timetables));

            // 等待所有异步任务完成
            CompletableFuture.allOf(
                    courseMapFuture,
                    lessonMapFuture,
                    classroomMapFuture,
                    teacherMapFuture,
                    lecturerInfoMapFuture,
                    classMapFuture)
                .join();

            // 获取所有结果并放入Map
            Map<String, Object> resultMap = new HashMap<>();
            resultMap.put("courseMap", courseMapFuture.join());
            resultMap.put("lessonMap", lessonMapFuture.join());
            resultMap.put("classroomMap", classroomMapFuture.join());
            resultMap.put("teacherMap", teacherMapFuture.join());
            resultMap.put("lecturerInfoMap", lecturerInfoMapFuture.join());
            resultMap.put("classMap", classMapFuture.join());

            // 记录日志
            log.info(
                "获取关联数据完成: 课程数={}, 课节数={}, 教室数={}, 教师数={}, 讲师数={}, 班级数={}",
                ((Map<?, ?>) resultMap.get("courseMap")).size(),
                ((Map<?, ?>) resultMap.get("lessonMap")).size(),
                ((Map<?, ?>) resultMap.get("classroomMap")).size(),
                ((Map<?, ?>) resultMap.get("teacherMap")).size(),
                ((Map<?, ?>) resultMap.get("lecturerInfoMap")).size(),
                ((Map<?, ?>) resultMap.get("classMap")).size());

            return resultMap;
        } catch (Exception e) {
            log.error("异步获取关联数据失败", e);
            throw new RuntimeException("获取关联数据失败", e);
        }
    }

    /**
     * 获取课表信息的公共方法
     */
    private List<Timetable> getTimetables(List<Long> lessonNos) {
        log.info("开始查询课表信息, lessonNos数量: {}", lessonNos.size());
        List<Timetable> timetables =
            timetableMapper.selectList(
                Wrappers.<Timetable>lambdaQuery().in(Timetable::getLessonNo, lessonNos));

        if (CollUtil.isEmpty(timetables)) {
            log.warn("未查询到课表信息, lessonNos: {}", JSON.toJSONString(lessonNos));
            return Collections.emptyList();
        }
        log.info("查询到相关课表数量: {}", timetables.size());
        return timetables;
    }

    /**
     * 将调课记录转换为VO对象
     */
    private StudentTimetableChangeVO convertToStudentTimetableChangeVO(
        TimetableChange change,
        Map<Long, Timetable> timetableMap,
        Map<Integer, CourseVO> courseMap,
        Map<String, LessonVO> lessonMap,
        Map<Long, ClassRoomVO> classroomMap,
        Map<Long, EmployeeVO> teacherMap,
        Map<Long, LecturerInfoVO> lecturerInfoVOMap) {

        StudentTimetableChangeVO vo = new StudentTimetableChangeVO();
        vo.setId(change.getId());
        vo.setCreateTime(change.getCreateTime());
        vo.setCreateBy(change.getCreateBy());

        // 设置源课程信息
        Timetable sourceTimetable = timetableMap.get(change.getSourceLessonNo());
        if (sourceTimetable != null) {
            vo.setSourceClassTime(sourceTimetable.getClassStartDateTime());

            // 设置源课程名称
            CourseVO sourceCourse = courseMap.get(sourceTimetable.getCourseId().intValue());
            LessonVO sourceLesson =
                lessonMap.get(
                    timetableChangeManager.generateLessonKey(
                        sourceTimetable.getCourseId(), sourceTimetable.getLessonOrder()));
            if (sourceCourse != null && sourceLesson != null) {
                vo.setSourceLessonName(sourceLesson.getLessonName());
            } else {
                log.warn("源课程或课节信息缺失, courseId: {}", sourceTimetable.getCourseId());
            }

            // 设置源课程教室和老师信息
            ClassRoomVO sourceClassroom = classroomMap.get(sourceTimetable.getClassroomId());
            if (sourceClassroom != null) {
                vo.setSourceClassroomName(sourceClassroom.getClassRoomName());
            } else {
                log.warn("源课程教室信息缺失, classroomId: {}", sourceTimetable.getClassroomId());
            }

            EmployeeVO sourceTeacher = teacherMap.get(sourceTimetable.getTeacherId());
            if (sourceTeacher != null) {
                vo.setSourceTeacherName(sourceTeacher.getName());
            } else {
                log.warn("源课程教师信息缺失, teacherId: {}", sourceTimetable.getTeacherId());
            }

            LecturerInfoVO lecturerInfoVO = lecturerInfoVOMap.get(sourceTimetable.getLectureId());
            if (lecturerInfoVO != null) {
                vo.setSourceLecturerName(lecturerInfoVO.getName());
            } else if (sourceTimetable.getLectureId() != null) {
                log.warn("源课程讲师信息缺失, lectureId: {}", sourceTimetable.getLectureId());
            }
        } else {
            log.warn("未找到源课程信息, sourceLessonNo: {}", change.getSourceLessonNo());
        }

        // 设置目标课程信息
        Timetable targetTimetable = timetableMap.get(change.getTargetLessonNo());
        if (targetTimetable != null) {
            vo.setTargetClassTime(targetTimetable.getClassStartDateTime());

            // 设置目标课程名称
            CourseVO targetCourse = courseMap.get(targetTimetable.getCourseId().intValue());
            LessonVO targetLesson =
                lessonMap.get(
                    timetableChangeManager.generateLessonKey(
                        targetTimetable.getCourseId(), targetTimetable.getLessonOrder()));
            if (targetCourse != null && targetLesson != null) {
                vo.setTargetLessonName(targetLesson.getLessonName());
            } else {
                log.warn("目标课程或课节信息缺失, courseId: {}", targetTimetable.getCourseId());
            }

            // 设置目标课程教室和老师信息
            ClassRoomVO targetClassroom = classroomMap.get(targetTimetable.getClassroomId());
            if (targetClassroom != null) {
                vo.setTargetClassroomName(targetClassroom.getClassRoomName());
            } else {
                log.warn("目标课程教室信息缺失, classroomId: {}", targetTimetable.getClassroomId());
            }

            EmployeeVO targetTeacher = teacherMap.get(targetTimetable.getTeacherId());
            if (targetTeacher != null) {
                vo.setTargetTeacherName(targetTeacher.getName());
            } else {
                log.warn("目标课程教师信息缺失, teacherId: {}", targetTimetable.getTeacherId());
            }

            LecturerInfoVO lecturerInfoVO = lecturerInfoVOMap.get(targetTimetable.getLectureId());
            if (lecturerInfoVO != null) {
                vo.setTargetLecturerName(lecturerInfoVO.getName());
            } else if (targetTimetable.getLectureId() != null) {
                log.warn("目标课程讲师信息缺失, lectureId: {}", targetTimetable.getLectureId());
            }
        } else {
            log.warn("未找到目标课程信息, targetLessonNo: {}", change.getTargetLessonNo());
        }
        return vo;
    }

    @Override
    public StudentTimetableChangeVO getStudentTimetableChangeRecordsDetail(Long id) {
        log.info("开始查询学生调课记录详情, id: {}", id);
        try {
            // 1. 查询调课记录
            List<TimetableChange> changes =
                this.list(Wrappers.<TimetableChange>lambdaQuery().eq(TimetableChange::getId, id));

            if (CollUtil.isEmpty(changes)) {
                log.info("未查询到学生调课记录, id: {}", id);
                return null;
            }

            // 2. 获取课表信息
            List<Long> lessonNos =
                changes.stream()
                    .flatMap(
                        change -> Stream.of(change.getSourceLessonNo(), change.getTargetLessonNo()))
                    .collect(Collectors.toList());

            List<Timetable> timetables = getTimetables(lessonNos);
            if (CollUtil.isEmpty(timetables)) {
                return null;
            }

            // 3. 获取关联数据
            Map<String, Object> relatedData = fetchRelatedDataAsync(timetables);

            // 4. 转换为VO对象
            List<StudentTimetableChangeVO> resultList =
                changes.stream()
                    .map(
                        change ->
                            convertToStudentTimetableChangeVO(
                                change,
                                createTimetableMap(timetables),
                                (Map<Integer, CourseVO>) relatedData.get("courseMap"),
                                (Map<String, LessonVO>) relatedData.get("lessonMap"),
                                (Map<Long, ClassRoomVO>) relatedData.get("classroomMap"),
                                (Map<Long, EmployeeVO>) relatedData.get("teacherMap"),
                                (Map<Long, LecturerInfoVO>) relatedData.get("lecturerInfoMap")))
                    .toList();

            return resultList.isEmpty() ? null : resultList.get(0);

        } catch (Exception e) {
            log.error("查询学生调课记录详情异常, id: {}", id, e);
            return null;
        }
    }

    /**
     * 获取学员未上课的调课数量
     *
     * @param studentId
     * @param storeId
     * @return java.lang.Integer
     * <AUTHOR>
     * @date 2025/3/11 14:42
     */
    @Override
    public Integer missedClassReschedule(Long studentId, Long storeId) {
        log.info("开始查询学员未上课的调课数量, studentId: {}, storeId: {}", studentId, storeId);
        try {
            List<Long> longList = missedClassRescheduleResult(studentId);
            if (longList == null) {
                return 0;
            }

            log.info("学员未上课的调课数量: {}", CollectionUtils.size(longList));
            return CollectionUtils.size(longList);

        } catch (Exception e) {
            log.error("查询学员未上课的调课数量异常", e);
            return 0;
        }
    }

    /**
     * 获取学员未上课的调课的课程
     *
     * @param studentId
     * @return java.util.List<java.lang.Long>
     * <AUTHOR>
     * @date 2025/3/11 14:59
     */
    private List<Long> missedClassRescheduleResult(Long studentId) {
        // 1. 获取该学员的所有调课记录
        List<TimetableChange> changes =
            this.list(
                Wrappers.<TimetableChange>lambdaQuery()
                    .eq(TimetableChange::getStudentId, studentId));

        if (CollUtil.isEmpty(changes)) {
            log.info("未查询到学员调课记录");
            return null;
        }

        // 2. 获取目标课次号列表
        List<Long> targetLessonNos =
            changes.stream().map(TimetableChange::getTargetLessonNo).collect(Collectors.toList());

        // 3. 查询课表信息,筛选未开始的课程
        List<Timetable> futureTimetables =
            timetableMapper.selectList(
                Wrappers.<Timetable>lambdaQuery()
                    .in(Timetable::getLessonNo, targetLessonNos)
                    .gt(Timetable::getClassStartDateTime, LocalDateTime.now()));

        if (CollUtil.isEmpty(futureTimetables)) {
            log.info("未查询到未开始的调课课程");
            return null;
        }

        // 4. 获取这些课程的考勤记录
        List<Long> futureLessonNos =
            futureTimetables.stream().map(Timetable::getLessonNo).collect(Collectors.toList());

        List<BClassTimeStudent> attendanceRecords =
            classTimeStudentManager.selectListByLessonNo(studentId, futureLessonNos);

        // 5. 统计未签到的课程数量
        Set<Long> checkedInLessonNos =
            attendanceRecords.stream()
                .filter(
                    record ->
                        CheckInStatusEnum.CHECK_IN_STATUS_1.code.equals(record.getCheckInStatus()))
                .map(BClassTimeStudent::getLessonNo)
                .collect(Collectors.toSet());

        return futureTimetables.stream()
            .map(Timetable::getLessonNo)
            .filter(lessonNo -> !checkedInLessonNos.contains(lessonNo))
            .collect(Collectors.toList());
    }

    /**
     * 清除学员未上课的调课课程
     *
     * @param studentId
     * @param storeId
     * @return void
     * <AUTHOR>
     * @date 2025/3/11 14:42
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    @OperateLog(
        operateType = OperateTypeEnum.OPERATE_TYPE_ENUM_3,
        operateCategory = OperateCategoryEnum.CATEGORY_ENUM_6,
        objectId = "#studentId",
        oldVal = "正常状态",
        newVal = "转班删除调课",
        name = "转班删除调课")
    public void missedClassRescheduleClear(Long studentId, Long storeId) {
        log.info("开始清除学员未上课的调课记录, studentId: {}, storeId: {}", studentId, storeId);

        try {
            // 1. 获取学员未上课的调课课次号列表
            List<Long> unattendedLessonNos = missedClassRescheduleResult(studentId);
            if (CollUtil.isEmpty(unattendedLessonNos)) {
                log.info("学员没有未上课的调课记录, studentId: {}", studentId);
                return;
            }

            log.info(
                "查询到学员未上课的调课课次数量: {}, lessonNos: {}",
                unattendedLessonNos.size(),
                JSON.toJSONString(unattendedLessonNos));

            // 2. 查询这些课次对应的调课记录
            List<TimetableChange> changes =
                this.list(
                    Wrappers.<TimetableChange>lambdaQuery()
                        .eq(TimetableChange::getStudentId, studentId)
                        .in(TimetableChange::getTargetLessonNo, unattendedLessonNos));

            if (CollUtil.isEmpty(changes)) {
                log.warn(
                    "未找到对应的调课记录, studentId: {}, lessonNos: {}",
                    studentId,
                    JSON.toJSONString(unattendedLessonNos));
                return;
            }
            for (TimetableChange change : changes) {
                int updateResult =
                    classTimeStudentManager.updateAdjustStatus(studentId,
                        change.getTargetLessonNo());

                if (updateResult <= 0) {
                    log.warn(
                        "更新学员课次调课状态失败, studentId: {}, lessonNo: {}", studentId,
                        change.getTargetLessonNo());
                }
            }
            log.info("更新学员课次调课状态成功, 更新记录数: {}", changes.size());

            // 3. 删除调课记录
            List<Long> changeIds =
                changes.stream().map(TimetableChange::getId).collect(Collectors.toList());

            boolean removeResult = this.removeByIds(changeIds);

            if (!removeResult) {
                log.error(
                    "删除调课记录失败, studentId: {}, changeIds: {}", studentId,
                    JSON.toJSONString(changeIds));
                throw new BizException("删除调课记录失败");
            }

            log.info("清除学员未上课的调课记录成功, 删除调课记录数: {}, studentId: {}",
                changeIds.size(), studentId);

        } catch (Exception e) {
            log.error("清除学员未上课的调课记录异常, studentId: {}", studentId, e);
            throw new BizException("清除调课记录失败: " + e.getMessage());
        }
    }

    @Override
    public Map<Long, List<Long>> getBySourceLessonNoList(List<Long> sourceLessonNoList) {
        return timetableChangeMapper.selectList(Wrappers.<TimetableChange>lambdaQuery()
            .in(TimetableChange::getSourceLessonNo, sourceLessonNoList))
            .stream()
            .collect(Collectors.groupingBy(TimetableChange::getSourceLessonNo,
                Collectors.mapping(TimetableChange::getStudentId, Collectors.toList())));
    }
}
