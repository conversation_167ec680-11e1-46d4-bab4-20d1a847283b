package com.yuedu.ydsf.eduConnect.jw.controller;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yuedu.ydsf.common.core.util.R;
import com.yuedu.ydsf.common.core.util.V_A;
import com.yuedu.ydsf.common.core.util.V_E;
import com.yuedu.ydsf.common.excel.annotation.ResponseExcel;
import com.yuedu.ydsf.common.feign.annotation.NoToken;
import com.yuedu.ydsf.common.log.annotation.SysLog;
import com.yuedu.ydsf.common.security.annotation.Inner;
import com.yuedu.ydsf.common.security.annotation.StorePermission;
import com.yuedu.ydsf.eduConnect.jw.api.query.TimetableChangeSourceQuery;
import com.yuedu.ydsf.eduConnect.jw.api.query.TimetableChangeTargetQuery;
import com.yuedu.ydsf.eduConnect.jw.api.vo.StudentTimetableChangeVO;
import com.yuedu.ydsf.eduConnect.jw.api.vo.TimetableChangeSourceVO;
import com.yuedu.ydsf.eduConnect.jw.api.vo.TimetableChangeTargetVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import org.springdoc.core.annotations.ParameterObject;
import org.springframework.http.HttpHeaders;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import com.yuedu.ydsf.common.security.annotation.HasPermission;
import com.yuedu.ydsf.eduConnect.jw.service.TimetableChangeService;
import com.yuedu.ydsf.eduConnect.jw.api.query.TimetableChangeQuery;
import com.yuedu.ydsf.eduConnect.jw.api.dto.TimetableChangeDTO;
import com.yuedu.ydsf.eduConnect.jw.api.vo.TimetableChangeVO;

import java.io.Serializable;
import java.util.List;

/**
 * 调课记录表控制层
 *
 * <AUTHOR>
 * @date 2025/02/12
 */
@RestController
@RequiredArgsConstructor
@RequestMapping(value = "/bTimetableChange")
@Tag(description = "b_timetable_change", name = "调课记录表")
@SecurityRequirement(name = HttpHeaders.AUTHORIZATION)
public class TimetableChangeController {

  private final TimetableChangeService timetableChangeService;

  /**
   * 调课记录表分页查询
   *
   * @param page 分页对象
   * @param bTimetableChangeQuery 调课记录表
   * @return R
   */
  @GetMapping("/page")
  @Operation(summary = "分页查询", description = "调课记录表分页查询")
  @StorePermission
  public R page(
      @ParameterObject Page page, @ParameterObject TimetableChangeQuery bTimetableChangeQuery) {
    return R.ok(timetableChangeService.page(page, bTimetableChangeQuery));
  }

  /**
   * 通过id查询调课记录表
   *
   * @param id id
   * @return R
   */
  @Operation(summary = "通过id查询", description = "通过id查询调课记录表")
  @GetMapping("/{id}")
  @StorePermission
  public R getById(@PathVariable Serializable id) {
    return R.ok(timetableChangeService.getById(id));
  }

  /**
   * 修改调课记录表
   *
   * @param bTimetableChangeDTO 调课记录表
   * @return R
   */
  @PutMapping
  @SysLog("修改调课记录表")
  @Operation(summary = "修改调课记录表", description = "修改调课记录表")
  @StorePermission
  public R edit(@Validated(V_E.class) @RequestBody TimetableChangeDTO bTimetableChangeDTO) {
    return R.ok(timetableChangeService.edit(bTimetableChangeDTO));
  }

  /**
   * 通过id删除调课记录表
   *
   * @param ids id列表
   * @return R
   */
  @DeleteMapping
  @SysLog("通过id删除调课记录表")
  @Operation(summary = "删除调课记录表", description = "删除调课记录表")
  @StorePermission
  public R delete(@RequestBody Long[] ids) {
    return R.ok(timetableChangeService.removeBatchByIds(CollUtil.toList(ids)));
  }

  /**
   * 导出excel 调课记录表表格
   *
   * @param bTimetableChangeQuery 查询条件
   * @param ids 导出指定ID
   * @return excel 文件流
   */
  @ResponseExcel
  @GetMapping("/export")
  @Operation(summary = "导出调课记录表表格", description = "导出调课记录表表格")
  @StorePermission
  public List<TimetableChangeVO> export(TimetableChangeQuery bTimetableChangeQuery, Long[] ids) {
    return timetableChangeService.export(bTimetableChangeQuery, ids);
  }

  /**
   * 获取调课课程列表
   *
   * <AUTHOR>
   * @date 2025/2/12 10:31
   * @param query
   * @return
   *     com.yuedu.ydsf.common.core.util.R<java.util.List<com.yuedu.ydsf.eduConnect.jw.api.vo.TimetableChangeSourceVO>>
   */
  @GetMapping("/sourceLessonList")
  @Operation(summary = "获取调课课程列表", description = "获取调课课程列表")
  @StorePermission
  public R<Page<TimetableChangeSourceVO>> getSourceLessonList(
      @ParameterObject Page page, @ParameterObject TimetableChangeSourceQuery query) {
    Page<TimetableChangeSourceVO> result = timetableChangeService.getSourceLessonList(page, query);
    return R.ok(result);
  }

  /**
   * 获取调课目标排课列表
   *
   * <AUTHOR>
   * @date 2025/2/12 10:31
   * @param query
   * @return
   *     com.yuedu.ydsf.common.core.util.R<java.util.List<com.yuedu.ydsf.eduConnect.jw.api.vo.TimetableChangeTargetVO>>
   */
  @GetMapping("/targetLessonList")
  @Operation(summary = "获取目标排课列表", description = "获取可调课的目标排课列表")
  @StorePermission
  public R<Page<TimetableChangeTargetVO>> getTargetLessonList(
      @ParameterObject Page page, @ParameterObject TimetableChangeTargetQuery query) {
    Page<TimetableChangeTargetVO> result = timetableChangeService.getTargetLessonList(page, query);
    return R.ok(result);
  }

  /**
   * 新增调课记录表
   *
   * @param bTimetableChangeDTO 调课记录表
   * @return R
   */
  @PostMapping
  @SysLog("新增调课记录表")
  @Operation(summary = "新增调课记录表", description = "新增调课记录表")
  @StorePermission
  public R add(@RequestBody TimetableChangeDTO bTimetableChangeDTO) {
    return R.ok(timetableChangeService.add(bTimetableChangeDTO));
  }

  /**
   * 学生获取调课记录
   *
   * <AUTHOR>
   * @date 2025/2/13 9:30
   * @return com.yuedu.ydsf.common.core.util.R
   */
  @GetMapping("/student/records")
  @SysLog("学生获取调课记录")
  @Operation(summary = "学生获取调课记录", description = "获取学生的所有调课记录,按调课时间倒序排序")
  @StorePermission
  public R<Page<StudentTimetableChangeVO>> getStudentTimetableChangeRecords(
      @ParameterObject Page page, @ParameterObject @NotNull(message = "学生ID不能为空") Long studentId) {
    Page<StudentTimetableChangeVO> records =
        timetableChangeService.getStudentTimetableChangeRecords(page, studentId);
    return R.ok(records);
  }

  /**
   * 学生获取调课记录详情
   *
   * <AUTHOR>
   * @date 2025/3/4 10:18
   * @param id
   * @return
   *     com.yuedu.ydsf.common.core.util.R<com.yuedu.ydsf.eduConnect.jw.api.vo.StudentTimetableChangeVO>
   */
  @GetMapping("/student/records/{id}")
  @SysLog("学生获取调课记录详情")
  @Operation(summary = "学生获取调课记录详情", description = "获取学生的所有调课记录,按调课时间倒序排序")
  @StorePermission
  public R<StudentTimetableChangeVO> getStudentTimetableChangeRecordsDetail(
      @PathVariable("id") Long id) {
    StudentTimetableChangeVO info =
        timetableChangeService.getStudentTimetableChangeRecordsDetail(id);
    return R.ok(info);
  }

  /**
   * 获取学员未上课的调课数量
   *
   * <AUTHOR>
   * @date 2025/3/11 14:27
   * @param studentId
   * @param storeId
   * @return com.yuedu.ydsf.common.core.util.R<java.lang.Integer>
   */
  @GetMapping("/missedClassReschedule/{studentId}/{storeId}")
  @Operation(summary = "获取学员未上课的调课数量", description = "获取学员未上课的调课数量")
  @StorePermission
  R<Integer> missedClassReschedule(
      @PathVariable("studentId") Long studentId, @PathVariable("storeId") Long storeId) {
    Integer num = timetableChangeService.missedClassReschedule(studentId, storeId);
    return R.ok(num);
  }

  /**
   * 清除学员未上课的调课课程
   *
   * <AUTHOR>
   * @date 2025/3/11 14:31
   * @param studentId
   * @param storeId
   * @return com.yuedu.ydsf.common.core.util.R<java.lang.Integer>
   */
  @PutMapping("/missedClassReschedule/clear/{studentId}/{storeId}")
  @Inner
  R missedClassRescheduleClear(
      @PathVariable("studentId") Long studentId, @PathVariable("storeId") Long storeId) {
    timetableChangeService.missedClassRescheduleClear(studentId, storeId);
    return R.ok();
  }
}
