package com.yuedu.ydsf.eduConnect.jw.api.dto;

import com.yuedu.ydsf.eduConnect.jw.api.valid.CourseVodValidGroup;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;
import lombok.Data;

/**
 * 门店已约点播课 传输类
 *
 * <AUTHOR>
 * @date 2024-12-09 10:24:34
 */
@Data
@Schema(description = "门店已约点播课传输类")
public class CourseVodDTO implements Serializable {


	/**
	* 主键ID
	*/
    @Schema(description="主键ID")
    @NotNull(groups = {CourseVodValidGroup.AddOrderedStoreCourseVodGroup.class,
        CourseVodValidGroup.EditStoreCourseVodGroup.class,
        CourseVodValidGroup.SaveStoreCourseVodPlanGroup.class}, message = "门店已约点播课ID不能为空")
    private Long id;

	/**
	* 门店ID
	*/
    @Schema(description="门店ID")
    @NotNull(groups = {CourseVodValidGroup.AddUnStoreCourseVodGroup.class}, message = "门店ID不能为空")
    private Long storeId;

	/**
	* 排课名称
	*/
    @Schema(description="排课名称")
    @NotBlank(groups = {CourseVodValidGroup.AddUnStoreCourseVodGroup.class,
        CourseVodValidGroup.AddOrderedStoreCourseVodGroup.class}, message = "排课名称不能为空")
    @Size(groups = {CourseVodValidGroup.AddUnStoreCourseVodGroup.class,
        CourseVodValidGroup.AddOrderedStoreCourseVodGroup.class}, max = 20, message = "排课名称字符长度不能大于20")
    private String name;

	/**
	* 课程ID
	*/
    @Schema(description="课程ID")
    @NotNull(groups = {CourseVodValidGroup.AddUnStoreCourseVodGroup.class,
        CourseVodValidGroup.AddOrderedStoreCourseVodGroup.class,
        CourseVodValidGroup.EditStoreCourseVodGroup.class}, message = "课程ID不能为空")
    private Long courseId;

	/**
	* 阶段
	*/
    @Schema(description="阶段")
    @NotNull(groups = {CourseVodValidGroup.AddUnStoreCourseVodGroup.class,
        CourseVodValidGroup.AddOrderedStoreCourseVodGroup.class,
        CourseVodValidGroup.EditStoreCourseVodGroup.class}, message = "阶段不能为空")
    private Integer stage;

	/**
	* 主讲老师ID
	*/
    @Schema(description="主讲老师ID")
    @NotNull(groups = {CourseVodValidGroup.AddUnStoreCourseVodGroup.class,
        CourseVodValidGroup.AddOrderedStoreCourseVodGroup.class,
        CourseVodValidGroup.EditStoreCourseVodGroup.class}, message = "主讲老师ID不能为空")
    private Long lectureId;

	/**
	* 班级ID
	*/
    @Schema(description="班级ID")
    @NotNull(groups = {CourseVodValidGroup.AddUnStoreCourseVodGroup.class,
        CourseVodValidGroup.AddOrderedStoreCourseVodGroup.class,
        CourseVodValidGroup.EditStoreCourseVodGroup.class}, message = "班级ID不能为空")
    private Long classId;

	/**
	* 教室ID
	*/
    @Schema(description="教室ID")
    @NotNull(groups = {CourseVodValidGroup.AddUnStoreCourseVodGroup.class,
        CourseVodValidGroup.AddOrderedStoreCourseVodGroup.class,
        CourseVodValidGroup.EditStoreCourseVodGroup.class}, message = "教室ID不能为空")
    private Long classroomId;

	/**
	* 指导老师ID
	*/
    @Schema(description="指导老师ID")
    private Long teacherId;

	/**
	* 是否已排课: 0-未排课; 1-已排课;
	*/
    @Schema(description="是否已排课: 0-未排课; 1-已排课;")
    private Integer scheduled;

	/**
	* 创建人
	*/
    @Schema(description="创建人")
    private String createBy;

	/**
	* 创建时间
	*/
    @Schema(description="创建时间")
    private LocalDateTime createTime;

	/**
	* 修改人
	*/
    @Schema(description="修改人")
    private String updateBy;

	/**
	* 修改时间
	*/
    @Schema(description="修改时间")
    private LocalDateTime updateTime;

	/**
	* 是否删除: 0-否; 1-是;
	*/
    @Schema(description="是否删除: 0-否; 1-是;")
    private Integer delFlag;

    /**
     * 点播课排期
     */
    @Schema(description="点播课排期")
    @Valid
//    @NotEmpty(groups = {CourseVodValidGroup.AddOrderedStoreCourseVodGroup.class,
//        CourseVodValidGroup.SaveStoreCourseVodPlanGroup.class}, message = "点播课排期不能为空")
    private List<CourseVodPlanDTO> courseVodPlanDTOList;


}
