package com.yuedu.ydsf.eduConnect.jw.api.feign;

import com.yuedu.ydsf.common.core.constant.ServiceNameConstants;
import com.yuedu.ydsf.common.core.util.R;
import com.yuedu.ydsf.common.feign.annotation.NoToken;
import com.yuedu.ydsf.eduConnect.jw.api.dto.BClassTimeStudentDTO;
import com.yuedu.ydsf.eduConnect.jw.api.vo.CheckInStudentVO;
import java.io.Serializable;
import java.util.List;
import com.yuedu.ydsf.eduConnect.jw.api.vo.CheckInStudentVO.StudentCheckInInfoVO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * 课次学生远程服务
 *
 * <AUTHOR> href="https://61666.top">刘艺</a>
 * @date 2025/3/11 9:44
 * @project @Title: RemoteClassTimeStudentService.java
 */
@FeignClient(
    contextId = "remoteClassTimeStudentsService",
    value = ServiceNameConstants.EDU_CONNECT_JW_SERVICE)
public interface RemoteClassTimeStudentsService {

  /**
   * 根据课次编号获取已出勤学生列表
   *
   * @param lessonNo 课次编号
   * @return 已出勤学生列表
   */
  @GetMapping("/bClassTimeStudent/getCheckedInStudentsByLessonNo/{lessonNo}/{storeId}")
  @NoToken
  R<List<StudentCheckInInfoVO>> getCheckedInStudentsByLessonNo(
      @PathVariable("lessonNo") Long lessonNo, @PathVariable("storeId") Long storeId);


  /**
   * 根据lessons批量获取出勤人数
   * <AUTHOR>
   * @date 2025/3/19 9:24
   * @param bClassTimeStudentDTO
   * @return com.yuedu.ydsf.common.core.util.R<java.util.List<com.yuedu.ydsf.eduConnect.jw.api.vo.CheckInStudentVO.StudentCheckInInfoVO>>
   */
  @PostMapping("/bClassTimeStudent/getCheckedInStudentsByLessonNoBatch")
  @NoToken
  R<List<StudentCheckInInfoVO>> getCheckedInStudentsByLessonNoBatch(
      @RequestBody BClassTimeStudentDTO bClassTimeStudentDTO);

  /**
   * 根据课次编号获取学生详情列表包含总人数（未删除未调出）、已出勤人数、总人数（未删除未调出）List
   *
   * @param lessonNo 课次编号
   * @return 已出勤学生列表
   */
  @GetMapping("/bClassTimeStudent/getStudentsInfoByLessonNo/{lessonNo}/{storeId}")
  @NoToken
  R<CheckInStudentVO> getStudentsInfoByLessonNo(
      @PathVariable("lessonNo") Long lessonNo, @PathVariable("storeId") Long storeId);

  /**
   * 根据ID查询学生课次信息
   *
   * <AUTHOR>
   * @date 2025/4/11 10:30
   * @param id 学生课次ID
   * @return com.yuedu.ydsf.common.core.util.R<com.yuedu.ydsf.eduConnect.jw.api.dto.BClassTimeStudentDTO>
   */
  @GetMapping("/bClassTimeStudent/inner/{id}")
  @NoToken
  R<BClassTimeStudentDTO> getById(@PathVariable("id") Long id);

  /**
   * 更新学生绑定的答题器
   *
   * <AUTHOR>
   * @date 2025/4/11 9:50
   * @param bClassTimeStudentDTO
   * @return com.yuedu.ydsf.common.core.util.R
   */
  @PutMapping("/bClassTimeStudent/updateStudentClicker")
  @NoToken
  R updateStudentClicker(@RequestBody BClassTimeStudentDTO bClassTimeStudentDTO);


    /**
     * 远程调用获取出勤学生
     *
     * @return com.yuedu.ydsf.common.core.util.R<com.yuedu.ydsf.eduConnect.jw.api.vo.CheckInStudentVO>
     * <AUTHOR>
     * @date 2025/7/9 14:58
     */
    @GetMapping("/bClassTimeStudent/getCheckInStudent/{lessonno}/{entryType}/{storeId}")
    @NoToken
  R<CheckInStudentVO> getCheckInStudentLive(@PathVariable Serializable lessonno,
        @PathVariable Integer entryType,
        @PathVariable Long storeId);
}
