package com.yuedu.ydsf.eduConnect.jw.api.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import java.time.LocalDateTime;
import java.util.List;
import lombok.Data;

/**
 * 门店已约点播课 视图类
 *
 * <AUTHOR>
 * @date 2024-12-09 10:24:34
 */
@Data
@Schema(description = "门店已约点播课视图类")
public class CourseVodVO {


	/**
	* 主键ID
	*/
    @Schema(description="主键ID")
    private Long id;

	/**
	* 门店ID
	*/
    @Schema(description="门店ID")
    private Long storeId;

	/**
	* 排课名称
	*/
    @Schema(description="排课名称")
    private String name;

	/**
	* 课程ID
	*/
    @Schema(description="课程ID")
    private Long courseId;

	/**
	* 阶段
	*/
    @Schema(description="阶段")
    private Integer stage;

	/**
	* 主讲老师ID
	*/
    @Schema(description="主讲老师ID")
    private Long lectureId;

	/**
	* 班级ID
	*/
    @Schema(description="班级ID")
    private Long classId;

	/**
	* 教室ID
	*/
    @Schema(description="教室ID")
    private Long classroomId;

	/**
	* 指导老师ID
	*/
    @Schema(description="指导老师ID")
    private Long teacherId;

	/**
	* 是否已排课: 0-未排课; 1-已排课;
	*/
    @Schema(description="是否已排课: 0-未排课; 1-已排课;")
    private Integer scheduled;

	/**
	* 创建人
	*/
    @Schema(description="创建人")
    private String createBy;

	/**
	* 创建时间
	*/
    @Schema(description="创建时间")
    private LocalDateTime createTime;

	/**
	* 修改人
	*/
    @Schema(description="修改人")
    private String updateBy;

	/**
	* 修改时间
	*/
    @Schema(description="修改时间")
    private LocalDateTime updateTime;

	/**
	* 是否删除: 0-否; 1-是;
	*/
    @Schema(description="是否删除: 0-否; 1-是;")
    private Integer delFlag;

    /**
     * 课程名称
     */
    @Schema(description="课程名称")
    private String courseName;

    /**
     * 门店已约点播课排期
     */
    @Schema(description="门店已约点播课排期")
    private List<CourseVodPlanVO> courseVodPlanVOList;

    /**
     * 主讲老师名称
     */
    @Schema(description="主讲老师名称")
    private String lectureName;

    /**
     * 主讲老师昵称
     */
    @Schema(description="主讲老师昵称")
    private String lectureNickName;

    /**
     * 班级名称
     */
    @Schema(description="班级名称")
    private String className;

    /**
     * 教室名称
     */
    @Schema(description="教室名称")
    private String classroomName;

    /**
     * 指导老师名称
     */
    @Schema(description="指导老师名称")
    private String teacherName;

    /**
     * 课程, 主讲老师最早教学计划时间
     */
    @Schema(description="课程, 主讲老师最早教学计划时间")
    private LocalDateTime earliestClassStartDateTime;

    /**
     * 校区名称
     */
    private String campusName;

    /**
     * 上课状态: 0-未结束; 1-已结束;
     */
    @Schema(description="上课状态: 0-未结束; 1-已结束;")
    private Integer courseVodStatus;

    /**
     * 阶段名称
     */
    @Schema(description="阶段名称")
    private String stageName;

    /**
     * 阿里云视频播放地址
     */
    @Schema(description="阿里云视频播放地址")
    private String aliyunPlayUrl;

    /**
     * mp4视频地址
     */
    @Schema(description="mp4视频地址")
    private String mp4Url;

    /**
     * 声网云端录制ID
     */
    @Schema(description="声网云端录制ID")
    private String agoraCloudRecordId;

}

