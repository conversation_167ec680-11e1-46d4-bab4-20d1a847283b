package com.yuedu.ydsf.eduConnect.jw.api.dto;

import com.yuedu.ydsf.common.core.util.V_A_E;
import com.yuedu.ydsf.common.core.util.V_E;
import com.yuedu.ydsf.eduConnect.jw.api.valid.BClassTimeStudentValidGroup;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import org.hibernate.validator.constraints.Length;
import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 课次学生表
 *
 * <AUTHOR>
 * @date 2025/02/24
 */
@Data
@Schema(description = "课次学生表传输对象")
public class BClassTimeStudentDTO implements Serializable {

  /** 主键ID */
  @NotNull(
      groups = {V_E.class},
      message = "主键ID不能为空")
  private Long id;

  /** 门店ID */
  @Schema(description = "门店ID")
  @NotNull(
      groups = {
        V_A_E.class,
        BClassTimeStudentValidGroup.GetCheckedInStudentsByLessonNoBatch.class,
        BClassTimeStudentValidGroup.BindClicker.class,
        BClassTimeStudentValidGroup.UnBindClicker.class,
        BClassTimeStudentValidGroup.BatchUnBindClicker.class
      },
      message = "门店ID不能为空")
  private Long storeId;

  /** 学生id */
  @Schema(description = "学生id")
  @NotNull(
      groups = {
        BClassTimeStudentValidGroup.CheckIn.class,
        BClassTimeStudentValidGroup.BindClicker.class,
        BClassTimeStudentValidGroup.UnBindClicker.class
      },
      message = "学生id不能为空")
  private Long studentId;

  /** 学生课次类型:1-班级学生;2-调课学生;3-添加考勤学生 */
  @Schema(description = "学生课次类型:1-班级学生;2-调课学生;3-添加考勤学生")
  private Integer studentType;

  /** 课次编号 */
  @Schema(description = "课次编号")
  @NotNull(
      groups = {
        BClassTimeStudentValidGroup.CheckIn.class,
        BClassTimeStudentValidGroup.AddChckinReissue.class,
        BClassTimeStudentValidGroup.BindClicker.class,
        BClassTimeStudentValidGroup.BatchUnBindClicker.class,
        BClassTimeStudentValidGroup.BatchBindClicker.class
      },
      message = "课次编号不能为空")
  private Long lessonNo;

  /** 考勤状态:0-未出勤(缺勤);1-已考勤 */
  @Schema(description = "考勤状态:0-未出勤(缺勤);1-已考勤")
  private Integer checkInStatus;

  /** 考勤类型:0-正常签到;1-补签 */
  @Schema(description = "考勤类型:0-正常签到;1-补签")
  private Integer checkInType;

  /** 考勤时间 */
  @Schema(description = "考勤时间")
  private LocalDateTime checkInTime;

  /** 考勤操作人 */
  @Schema(description = "考勤操作人")
  @Length(
      groups = {V_A_E.class},
      max = 255,
      message = "考勤操作人长度不能大于255")
  private String checkInCreateBy;

  /** 创建人 */
  @Schema(description = "创建人")
  @Length(
      groups = {V_A_E.class},
      max = 255,
      message = "创建人长度不能大于255")
  private String createBy;

  /** 创建时间 */
  @Schema(description = "创建时间")
  @NotNull(
      groups = {V_A_E.class},
      message = "创建时间不能为空")
  private LocalDateTime createTime;

  /** 修改人 */
  @Schema(description = "修改人")
  @Length(
      groups = {V_A_E.class},
      max = 255,
      message = "修改人长度不能大于255")
  private String updateBy;

  /** 修改时间 */
  @Schema(description = "修改时间")
  @NotNull(
      groups = {V_A_E.class},
      message = "修改时间不能为空")
  private LocalDateTime updateTime;

  /** 是否删除: 0-否; 1-是; */
  @Schema(description = "是否删除: 0-否; 1-是;")
  @NotNull(
      groups = {V_A_E.class},
      message = "是否删除不能为空")
  private Byte delFlag;

  /** 是否强制签到: 0-否; 1-是; */
  @Schema(description = "是否强制签到")
  private Integer forceCheckIn;

  /** 课程类型ID（用于指定扣减的课程类型） */
  @Schema(description = "课程类型ID")
  private Integer courseTypeId;

  /** 添加签到考勤学生id集合 */
  @Schema(description = "学生id集合")
  @NotNull(
      groups = {BClassTimeStudentValidGroup.AddChckinReissue.class},
      message = "学生id不能为空！")
  private List<Long> studentIds;

  /** 课程编号集合 */
  @NotNull(
      groups = {BClassTimeStudentValidGroup.GetCheckedInStudentsByLessonNoBatch.class},
      message = "课程编号集合不能为空！")
  @NotEmpty(
      groups = {BClassTimeStudentValidGroup.GetCheckedInStudentsByLessonNoBatch.class},
      message = "课程编号集合不能为空！")
  private List<Long> lessonNos;


    /**
     * 接收器SN码
     */
    private String receiverSnNumber;

    /**
     * 答题器SN码
     */
    private String clickerSnNumber;
}
