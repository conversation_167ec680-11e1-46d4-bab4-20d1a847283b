package com.yuedu.ydsf.eduConnect.jw.api.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDate;
import java.time.LocalTime;

/**
 * 课时消费记录详情
 */
@Schema(description = "课时消费记录详情")
@Data
public class CourseHoursConsumerDetailDTO {

    /**
     * 上课日期
     */
    @Schema(description = "上课日期")
    private String date;

    /**
     * 上课时间
     */
    @Schema(description = "上课时间")
    private String datetime;

    /**
     * 校区编号
     */
    @Schema(description = "校区编号")
    private String schoolNo;

    /**
     * 上课校区
     */
    @Schema(description = "上课校区")
    private String schoolName;

    /**
     * 上课课程
     */
    @Schema(description = "上课课程")
    private String className;

    /**
     * 班名
     */
    @Schema(description = "班名")
    private String courseName;

    /**
     * 主讲老师
     */
    @Schema(description = "主讲老师")
    private String teacher;

    /**
     * 出勤人数合计
     */
    @Schema(description = "出勤人数合计")
    private Integer attendanceNum = 0;

    /**
     * 试听课消耗
     */
    @Schema(description = "试听课消耗")
    private Integer auditionNum = 0;

    /**
     * 常规消耗
     */
    @Schema(description = "常规消耗")
    private Integer normalNum = 0;

    /**
     * 赠课消耗
     */
    @Schema(description = "赠课消耗")
    private Integer presentNum = 0;

    /**
     * 精品消耗
     */
    @Schema(description = "精品消耗")
    private Integer qualityNum = 0;

    @Schema(description="课程类型: 1-直播课; 2-点播课; 3-到店补课; 4-线上补课")
    private String courseType;

    @Schema(description="课程ID")
    private Long courseId;

    /**
     * 课时费单价
     */
    @Schema(description = "课时费单价")
    private Double courseFee;

    /**
     * 课次ID
     */
    @Schema(description = "课次ID")
    private Long timetableId;

}
