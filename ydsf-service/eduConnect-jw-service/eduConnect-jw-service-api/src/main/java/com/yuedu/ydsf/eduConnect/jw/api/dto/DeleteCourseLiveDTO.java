package com.yuedu.ydsf.eduConnect.jw.api.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 门店已约直播课 传输类
 *
 * <AUTHOR>
 * @date 2024-12-09 10:20:15
 */

@Data
@Schema(description = "门店已约直播课传输类")
public class DeleteCourseLiveDTO {

    /**
     * 门店ID
     */
    @Schema(description="门店ID")
    private Long storeId;

    /**
     * 主键ID
     */
    @Schema(description = "主键ID")
    private Long courseLiveId;

}
