package com.yuedu.ydsf.eduConnect.jw.api.valid;

/**
 * 课次学生教研组
 *
 * @date 2025/2/26 9:42
 * @project @Title: BClassTimeStudentValidGroup.java
 */
public interface BClassTimeStudentValidGroup {

  interface CheckIn {}

  interface AddChckinReissue {}

  interface GetCheckedInStudentsByLessonNoBatch {}

  interface BindClicker {}

  interface UnBindClicker {}

  interface BatchUnBindClicker {}

  interface BatchBindClicker {}

  interface PreValidateCheckIn {}

  interface AddTemporaryStudents {}

  interface RemoveTemporaryStudent {}

  interface AttendanceConfirmation {}
}
