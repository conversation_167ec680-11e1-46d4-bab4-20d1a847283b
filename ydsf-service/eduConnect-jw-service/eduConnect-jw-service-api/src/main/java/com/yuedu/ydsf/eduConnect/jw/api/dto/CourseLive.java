package com.yuedu.ydsf.eduConnect.jw.api.dto;

import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 门店已约直播课 实体类
 *
 * <AUTHOR>
 * @date 2024-12-09 10:20:15
 */
@Data
@TableName("b_course_live")
@Schema(description = "门店已约直播课实体类")
public class CourseLive {

    /**
     * 门店ID
     */
    @Schema(description = "门店ID")
    private Long storeId;

    /**
     * 教学计划ID
     */
    @Schema(description = "教学计划ID")
    private Long teachingPlanId;

    /**
     * 班级ID
     */
    @Schema(description = "班级ID")
    private Long classId;

    /**
     * 教室ID
     */
    @Schema(description = "教室ID")
    private Long classroomId;

    /**
     * 指导老师ID
     */
    @Schema(description = "指导老师ID")
    private Long teacherId;
}
