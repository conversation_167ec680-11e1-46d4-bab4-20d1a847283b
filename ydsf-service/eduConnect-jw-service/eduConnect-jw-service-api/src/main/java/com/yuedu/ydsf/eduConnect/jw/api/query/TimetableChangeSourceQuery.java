package com.yuedu.ydsf.eduConnect.jw.api.query;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import java.time.LocalDateTime;

@Data
@Schema(description = "调课课程列表查询对象")
public class TimetableChangeSourceQuery {

    @Schema(description = "学员ID")
    @NotNull(message = "学员ID不能为空")
    private Long studentId;

    @Schema(description = "课程ID")
    private Long courseId;

    @Schema(description = "课节序号")
    private Integer lessonOrder;

    @Schema(description = "课节名称")
    private String lessonName;

    @Schema(description = "开始时间")
    private LocalDateTime startTime;

    @Schema(description = "结束时间")
    private LocalDateTime endTime;

    @Schema(description = "课程类型编号")
    private Integer courseType;
}
