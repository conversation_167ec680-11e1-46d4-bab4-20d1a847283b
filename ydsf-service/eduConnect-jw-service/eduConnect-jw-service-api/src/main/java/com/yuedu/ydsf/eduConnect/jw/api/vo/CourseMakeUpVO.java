package com.yuedu.ydsf.eduConnect.jw.api.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import lombok.Data;

/**
 * 门店补课表 视图类
 *
 * <AUTHOR>
 * @date 2024-12-09 10:23:17
 */
@Data
@Schema(description = "门店补课表视图类")
public class CourseMakeUpVO {


	/**
	* 主键ID
	*/
    @Schema(description="主键ID")
    private Long id;

	/**
	* 门店ID
	*/
    @Schema(description="门店ID")
    private Long storeId;

	/**
	* 已约直播课/点播课课表ID
	*/
    @Schema(description="已约直播课/点播课课表ID")
    private Long timetableId;

	/**
	* 课程ID
	*/
    @Schema(description="课程ID")
    private Long courseId;

	/**
	* 第几节课
	*/
    @Schema(description="第几节课")
    private Integer lessonOrder;

	/**
	* 上课时段ID
	*/
    @Schema(description="上课时段ID")
    private Long timeSlotId;

	/**
	* 主讲老师ID
	*/
    @Schema(description="主讲老师ID")
    private Long lectureId;

	/**
	* 上课教室ID
	*/
    @Schema(description="上课教室ID")
    private Long classRoomId;

    /**
     * 班级ID
     */
    @Schema(description="班级ID")
    private Long classId;

	/**
	* 上课日期
	*/
    @Schema(description="上课日期")
    private LocalDate classDate;

	/**
	* 上课开始时间
	*/
    @Schema(description="上课开始时间")
    private LocalTime classStartTime;

	/**
	* 上课结束时间
	*/
    @Schema(description="上课结束时间")
    private LocalTime classEndTime;

	/**
	* 创建人
	*/
    @Schema(description="创建人")
    private String createBy;

	/**
	* 创建时间
	*/
    @Schema(description="创建时间")
    private LocalDateTime createTime;

	/**
	* 修改人
	*/
    @Schema(description="修改人")
    private String updateBy;

	/**
	* 修改时间
	*/
    @Schema(description="修改时间")
    private LocalDateTime updateTime;

	/**
	* 是否删除: 0-否; 1-是;
	*/
    @Schema(description="是否删除: 0-否; 1-是;")
    private Integer delFlag;

    /**
     * 主讲老师名称
     */
    @Schema(description="主讲老师名称")
    private String lectureName;

    /**
     * 主讲老师昵称
     */
    @Schema(description="主讲老师昵称")
    private String lectureNickName;

    /**
     * 班级名称
     */
    @Schema(description="班级名称")
    private String className;

    /**
     * 教室名称
     */
    @Schema(description="教室名称")
    private String classroomName;

    /**
     * 封面(全路径)
     */
    @Schema(description = "封面")
    private String imgUrl;

    /**
     * 补课类型
     */
    @Schema(description = "补课类型")
    private Integer makeUpType;

    /**
     * 补课有效期开始时间（仅线上补课有效）
     */
    @Schema(description="补课有效期开始时间（仅线上补课有效）")
    private LocalDateTime validityStartTime;

    /**
     * 补课有效期结束时间（仅线上补课有效）
     */
    @Schema(description="补课有效期结束时间（仅线上补课有效）")
    private LocalDateTime validityEndTime;

    /**
     * 课节名字
     */
    @Schema(description="课节名字")
    private String lessonName;

    /**
     * 阶段名称
     */
    @Schema(description="阶段名称")
    private String stageName;

    /**
     * 上课时段名称
     */
    @Schema(description="上课时段名称")
    private String timeSlotName;

    /**
     * 课程版本(教室端进入课程时存储)
     */
    @Schema(description="课程版本(教室端进入课程时存储)")
    private Integer courseVersion;

    /**
     * 课件版本(教室端进入课程时存储)
     */
    @Schema(description="课件版本(教室端进入课程时存储)")
    private Integer coursewareVersion;

}

