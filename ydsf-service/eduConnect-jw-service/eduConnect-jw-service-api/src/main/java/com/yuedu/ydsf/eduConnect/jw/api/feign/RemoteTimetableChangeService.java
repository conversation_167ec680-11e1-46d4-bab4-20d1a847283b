package com.yuedu.ydsf.eduConnect.jw.api.feign;

import com.yuedu.ydsf.common.core.constant.ServiceNameConstants;
import com.yuedu.ydsf.common.core.util.R;
import com.yuedu.ydsf.common.feign.annotation.NoToken;
import com.yuedu.ydsf.eduConnect.jw.api.dto.TimetableDTO;
import com.yuedu.ydsf.eduConnect.jw.api.vo.TimetableClassTimeVO;
import com.yuedu.ydsf.eduConnect.jw.api.vo.TimetableVO;
import java.util.List;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.Mapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * 课表调课记录
 *
 * @date 2025/3/11 14:16
 * @project @Title: RemoteTimetableChangeService.java
 */
@FeignClient(
    contextId = "remoteTimetableChangeService",
    value = ServiceNameConstants.EDU_CONNECT_JW_SERVICE)
public interface RemoteTimetableChangeService {

  /**
   * 获取学员未上课的调课数量
   *
   * <AUTHOR>
   * @date 2025/3/11 14:27
   * @param studentId
   * @param storeId
   * @return com.yuedu.ydsf.common.core.util.R<java.lang.Integer>
   */
  @GetMapping("/bTimetableChange/missedClassReschedule/{studentId}/{storeId}")
  @NoToken
  R<Integer> missedClassReschedule(
      @PathVariable("studentId") Long studentId, @PathVariable("storeId") Long storeId);

  /**
   * 清除学员未上课的调课课程
   *
   * <AUTHOR>
   * @date 2025/3/11 14:31
   * @param studentId
   * @param storeId
   * @return com.yuedu.ydsf.common.core.util.R<java.lang.Integer>
   */
  @PutMapping("/bTimetableChange/missedClassReschedule/clear/{studentId}/{storeId}")
  @NoToken
  R missedClassRescheduleClear(
      @PathVariable("studentId") Long studentId, @PathVariable("storeId") Long storeId);
}
