package com.yuedu.ydsf.eduConnect.jw.api.dto;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 门店已约直播课 传输类
 *
 * <AUTHOR>
 * @date 2024-12-09 10:20:15
 */
@Data
@Schema(description = "门店已约直播课传输类")
public class CourseLiveEditDTO {

    /**
     * 主键ID
     */
    @TableId(type = IdType.AUTO)
    @Schema(description = "主键ID")
    private Long id;

    /**
     * 班级ID
     */
    @Schema(description = "班级ID")
    private Long classId;

    /**
     * 教室ID
     */
    @Schema(description = "教室ID")
    private Long classroomId;

    /**
     * 指导老师ID
     */
    @Schema(description = "指导老师ID")
    private Long teacherId;
    /**
     * 门店已约直播课Id
     */
    @Schema(description = "门店已约直播课Id")
    private Long coursePlanId;


    @Schema(description = "课程类型")
    private Integer courseType;
    /**
     * 删除标识,0:未删除 1:删除
     */
    private Integer delFlag;
}
