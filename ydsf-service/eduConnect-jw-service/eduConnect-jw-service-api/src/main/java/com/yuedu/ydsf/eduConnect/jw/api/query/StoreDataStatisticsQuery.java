package com.yuedu.ydsf.eduConnect.jw.api.query;

import com.yuedu.ydsf.eduConnect.jw.api.valid.StoreDataStatisticsValidGroup;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import java.time.LocalDateTime;
import lombok.Data;

/**
 * 门店运营数据统计 查询类
 * <AUTHOR>
 * @date 2025/3/10 15:03
 */
@Data
@Schema(description = "门店运营数据统计 查询类")
public class StoreDataStatisticsQuery {

	/**
	* 门店ID
	*/
    @Schema(description="门店ID")
    @NotNull(groups = {StoreDataStatisticsValidGroup.GetStoreStudentDataStatisticsGroup.class,
        StoreDataStatisticsValidGroup.GetStoreAttendanceDataStatisticsGroup.class,
        StoreDataStatisticsValidGroup.GetStoreCourseHoursDataStatisticsGroup.class}, message = "门店ID不能为空")
    private Long storeId;

	/**
	* 开始时间yyyy-MM-dd HH:mm:ss
	*/
    @Schema(description="开始时间yyyy-MM-dd HH:mm:ss")
    @NotNull(groups = {StoreDataStatisticsValidGroup.GetStoreStudentDataStatisticsGroup.class,
        StoreDataStatisticsValidGroup.GetStoreAttendanceDataStatisticsGroup.class,
        StoreDataStatisticsValidGroup.GetStoreCourseHoursDataStatisticsGroup.class}, message = "开始时间不能为空")
    private LocalDateTime selectDateStart;

	/**
	* 结束时间yyyy-MM-dd HH:mm:ss
	*/
    @Schema(description="结束时间yyyy-MM-dd HH:mm:ss")
    @NotNull(groups = {StoreDataStatisticsValidGroup.GetStoreStudentDataStatisticsGroup.class,
        StoreDataStatisticsValidGroup.GetStoreAttendanceDataStatisticsGroup.class,
        StoreDataStatisticsValidGroup.GetStoreCourseHoursDataStatisticsGroup.class}, message = "结束时间不能为空")
    private LocalDateTime selectDateEnd;

    /**
     * 是否调出:0-未调出;1-已调出
     */
    private Integer adjustStatus;

    /**
     * 是否是正式学员: 1-是; 0-否;
     */
    private Integer isRegularStudents;

    /**
     * 上课类型: 0-未上课; 1-已上课;
     */
    private Integer attendClassType;

    /**
     * 考勤状态:0-未出勤(缺勤);1-已考勤
     */
    private Integer checkInStatus;

}
