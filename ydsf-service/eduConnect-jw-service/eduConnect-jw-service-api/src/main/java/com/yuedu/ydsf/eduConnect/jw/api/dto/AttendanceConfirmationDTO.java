package com.yuedu.ydsf.eduConnect.jw.api.dto;

import com.yuedu.ydsf.eduConnect.jw.api.valid.BClassTimeStudentValidGroup;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 考勤确认请求DTO
 *
 * <AUTHOR>
 * @date 2025/07/09
 */
@Data
@Schema(description = "考勤确认请求DTO")
public class AttendanceConfirmationDTO implements Serializable {

    /** 课次编号 */
    @Schema(description = "课次编号")
    @NotNull(
        groups = {BClassTimeStudentValidGroup.AttendanceConfirmation.class},
        message = "课次编号不能为空")
    private Long lessonNo;

    /** 学员考勤操作列表 */
    @Schema(description = "学员考勤操作列表")
    @NotEmpty(
        groups = {BClassTimeStudentValidGroup.AttendanceConfirmation.class},
        message = "学员考勤操作列表不能为空")
    @Valid
    private List<StudentAttendanceOperationDTO> studentOperations;

    /** 是否为二次确认（处理错误后重新提交） */
    @Schema(description = "是否为二次确认")
    private Boolean isSecondConfirmation = false;

    @Data
    @Schema(description = "学员考勤操作DTO")
    public static class StudentAttendanceOperationDTO implements Serializable {

        /** 学生ID */
        @Schema(description = "学生ID")
        @NotNull(
            groups = {BClassTimeStudentValidGroup.AttendanceConfirmation.class},
            message = "学生ID不能为空")
        private Long studentId;

        /** 原始考勤状态 */
        @Schema(description = "原始考勤状态：0-未考勤；1-已考勤；2-预考勤")
        @NotNull(
            groups = {BClassTimeStudentValidGroup.AttendanceConfirmation.class},
            message = "原始考勤状态不能为空")
        private Integer originalStatus;

        /** 目标操作类型 */
        @Schema(description = "目标操作类型：1-签到；2-取消考勤；3-补签；4-无操作")
        @NotNull(
            groups = {BClassTimeStudentValidGroup.AttendanceConfirmation.class},
            message = "目标操作类型不能为空")
        private Integer targetOperation;

        /** 是否强制签到（针对课时不足等情况） */
        @Schema(description = "是否强制签到：0-否；1-是")
        private Integer forceCheckIn = 0;

        /** 指定扣减的课程类型ID（针对课消异常选择） */
        @Schema(description = "指定扣减的课程类型ID")
        private Integer courseTypeId;

        /** 错误处理信息（针对预校验返回的错误码） */
        @Schema(description = "错误处理信息")
        private ErrorHandlingInfo errorHandling;
    }

    @Data
    @Schema(description = "错误处理信息")
    public static class ErrorHandlingInfo implements Serializable {

        /** 错误码 */
        @Schema(description = "错误码")
        private Integer errorCode;

        /** 用户选择的处理方式 */
        @Schema(description = "用户选择的处理方式：1-强制操作；2-选择课程类型；3-跳过")
        private Integer handlingType;

        /** 用户选择的课程类型ID（当handlingType=2时使用） */
        @Schema(description = "用户选择的课程类型ID")
        private Integer selectedCourseTypeId;
    }

    /**
     * 目标操作类型枚举
     */
    public enum TargetOperationType {
        /** 签到 */
        CHECK_IN(1, "签到"),
        /** 取消考勤 */
        CANCEL_CHECK_IN(2, "取消考勤"),
        /** 补签 */
        REISSUE_CHECK_IN(3, "补签"),
        /** 无操作 */
        NO_OPERATION(4, "无操作");

        private final Integer code;
        private final String desc;

        TargetOperationType(Integer code, String desc) {
            this.code = code;
            this.desc = desc;
        }

        public Integer getCode() {
            return code;
        }

        public String getDesc() {
            return desc;
        }
    }

    /**
     * 原始考勤状态枚举
     */
    public enum OriginalStatusType {
        /** 未考勤 */
        NOT_CHECKED_IN(0, "未考勤"),
        /** 已考勤 */
        CHECKED_IN(1, "已考勤"),
        /** 预考勤 */
        PRE_ATTENDANCE(2, "预考勤");

        private final Integer code;
        private final String desc;

        OriginalStatusType(Integer code, String desc) {
            this.code = code;
            this.desc = desc;
        }

        public Integer getCode() {
            return code;
        }

        public String getDesc() {
            return desc;
        }
    }
}
