package com.yuedu.ydsf.eduConnect.jw.api.dto;

import com.yuedu.ydsf.eduConnect.jw.api.valid.BClassTimeStudentValidGroup;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 临加学员传输对象
 *
 * <AUTHOR>
 * @date 2025/06/19
 */
@Data
@Schema(description = "临加学员传输对象")
public class TemporaryStudentDTO implements Serializable {

    /** 课次编号 */
    @Schema(description = "课次编号")
    @NotNull(
        groups = {
            BClassTimeStudentValidGroup.AddTemporaryStudents.class,
            BClassTimeStudentValidGroup.RemoveTemporaryStudent.class
        },
        message = "课次编号不能为空")
    private Long lessonNo;

    /** 学生id */
    @Schema(description = "学生id")
    @NotNull(
        groups = {BClassTimeStudentValidGroup.RemoveTemporaryStudent.class},
        message = "学生id不能为空")
    private Long studentId;

    /** 学生id集合 */
    @Schema(description = "学生id集合")
    @NotNull(
        groups = {BClassTimeStudentValidGroup.AddTemporaryStudents.class},
        message = "学生id集合不能为空")
    @NotEmpty(
        groups = {BClassTimeStudentValidGroup.AddTemporaryStudents.class},
        message = "学生id集合不能为空")
    private List<Long> studentIds;
}
