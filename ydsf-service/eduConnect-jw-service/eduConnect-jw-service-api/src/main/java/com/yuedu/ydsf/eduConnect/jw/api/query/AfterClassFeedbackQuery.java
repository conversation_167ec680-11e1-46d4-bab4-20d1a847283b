package com.yuedu.ydsf.eduConnect.jw.api.query;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@Schema(description = "课后反馈")
public class AfterClassFeedbackQuery {
    /**
     * 新双师系统课次编号
     */
    @Schema(description = "课次编号")
    private Long lessonNo;

    /**
     * 课程ID
     */
    @Schema(description = "课程ID")
    private Long courseId;

    /**
     * 第几节课
     */
    @Schema(description = "第几节课")
    private Integer lessonOrder;
}
