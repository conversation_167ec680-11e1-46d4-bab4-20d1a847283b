package com.yuedu.ydsf.eduConnect.jw.api.dto;

import lombok.Builder;
import lombok.Data;

/**
 * 反馈推送公众号消息模板请求类
 * <AUTHOR>
 * @date 2024/9/2 11:39
 */
@Data
public class WxMsgTplDto {


    /**
     * 接收人公众号openId
     */
    private String touser;

    /**
     * 消息模版ID
     */
    private String template_id;

    /**
     * 跳转小程序时填写（url 和 miniprogram 同时不填，无跳转，page 和 miniprogram 同时填写，优先跳转小程序）
     */
    private MiniProgram miniprogram;

    /**
     * 防重入id。对于同一个openid + client_msg_id, 只发送一条消息,10分钟有效,超过10分钟不保证效果。若无防重入需求，可不填
     */
    private String client_msg_id;

    /**
     * 推送消息内容
     */
    private DataContent data;

    @Data
    @Builder
    public static class MiniProgram {

        /**
         * 小程序appid
         */
        private String appid;

        /**
         * 小程序页面路径
         */
        private String pagepath;

    }

    @Data
    @Builder
    public static class DataContent {

        /**
         * 课程名称
         */
        private ValueContent thing8;

        /**
         * 学员姓名
         */
        private ValueContent thing7;

        /**
         * 上课时间
         */
        private ValueContent time5;

        /**
         * 任课教师
         */
        private ValueContent thing4;

        /**
         * 学员姓名 (新模板字段)
         */
        private ValueContent thing17;

        /**
         * 上课时间 (新模板字段)
         */
        private ValueContent time8;

        /**
         * 课程名称 (新模板字段)
         */
        private ValueContent thing2;

        /**
         * 课消类型 (新模板字段)
         */
        private ValueContent phrase13;

        /**
         * 消课课时 (新模板字段)
         */
        private ValueContent character_string18;

        @Data
        @Builder
        public static class ValueContent {

            /**
             * 内容
             */
            private String value;

        }

    }


}
