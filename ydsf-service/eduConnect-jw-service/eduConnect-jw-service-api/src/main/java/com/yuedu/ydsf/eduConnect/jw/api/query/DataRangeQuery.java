package com.yuedu.ydsf.eduConnect.jw.api.query;

import io.swagger.v3.oas.annotations.media.Schema;
import java.time.LocalDate;
import lombok.Data;

/**
 * @author: 张浩宇
 * @date: 2024/12/10
 **/
@Data
public class DataRangeQuery {

    /**
     * 上课开始日期时间
     */
    @Schema(description = "上课开始日期时间")
    private LocalDate startTime;

    /**
     * 上课结束日期时间
     */
    @Schema(description = "上课结束日期时间")
    private LocalDate endTime;

    /**
     * 门店ID
     */
    @Schema(description = "门店ID")
    private Long storeId;
}
