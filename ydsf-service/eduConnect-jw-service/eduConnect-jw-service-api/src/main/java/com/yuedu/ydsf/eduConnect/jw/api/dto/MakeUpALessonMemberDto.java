package com.yuedu.ydsf.eduConnect.jw.api.dto;

import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Data;

@Data
public class MakeUpALessonMemberDto {

  /** 补课学员集合 */
  private List<BSsMakeUpALessonMemberDto> members;

  /** 排课ID */
  private String makeUpALessonCourseId;

  /** 排课状态0-正常;-1取消排课 */
  private String makeUpALessonStatus;

  /** 对应的匹配视频 */
  private MakeUpVideo makeUpVideo;

  @AllArgsConstructor
  public enum MakeUpALessonStatus_StatusEnum {
    STATUS_ENUM_0(0, "正常"),
    STATUS_ENUM_1(-1, "删除");

    private final int code;

    private final String message;
  }

  @Data
  public static class MakeUpVideo {

    /** 补课视频老师对应的id */
    private Long lectureId;

    /** 补课视频对应的书名 */
    private String makeUpALessonName;

    private Long matchDateStart;

    private Long matchDateEnd;

    private String videoId;

    private String resourcePath;

    private Long validityDays;
  }
}
