package com.yuedu.ydsf.eduConnect.jw.api.vo;

import com.yuedu.ydsf.eduConnect.jw.api.constant.enums.MakeUpErrorEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;


/**
 * 线上补课相关错误类
 *
 * @date 2025/4/28 14:44
 * @project @Title: MakeUpOnlineErrorVO.java
 */
@Data
public class MakeUpOnlineErrorVO {

  public MakeUpOnlineErrorVO() {}

  public MakeUpOnlineErrorVO(MakeUpErrorEnum errorEnum) {
    this.code = errorEnum.getCode();
    this.msg = errorEnum.getDesc();
  }

  public MakeUpOnlineErrorVO(CourseVodVO vod) {
    this.vod = vod;
  }

  /** 错误码 */
  @Schema(description = "code")
  private Integer code = 0;

  /** 错误信息 */
  @Schema(description = "错误信息")
  private String msg;

  /** 视频资源 */
  @Schema(description = "视频资源")
  private CourseVodVO vod;
}
