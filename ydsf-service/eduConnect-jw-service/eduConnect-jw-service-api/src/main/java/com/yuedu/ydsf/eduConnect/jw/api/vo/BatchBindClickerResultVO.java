package com.yuedu.ydsf.eduConnect.jw.api.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 批量绑定答题器结果VO
 *
 * <AUTHOR>
 * @date 2025/07/10
 */
@Data
@Schema(description = "批量绑定答题器结果VO")
public class BatchBindClickerResultVO implements Serializable {

    /**
     * RTM消息是否发送成功
     */
    @Schema(description = "RTM消息是否发送成功")
    private Boolean rtmSendSuccess;

    /**
     * RTM消息发送结果描述
     */
    @Schema(description = "RTM消息发送结果描述")
    private String rtmMessage;

    /**
     * 应出勤学员总数
     */
    @Schema(description = "应出勤学员总数")
    private Integer totalStudentCount;

    /**
     * 课时扣减成功的学员数量
     */
    @Schema(description = "课时扣减成功的学员数量")
    private Integer courseHoursDeductSuccessCount;

    /**
     * 课时扣减异常的学员数量
     */
    @Schema(description = "课时扣减异常的学员数量")
    private Integer courseHoursDeductErrorCount;

    /**
     * 课时扣减异常的学员列表
     */
    @Schema(description = "课时扣减异常的学员列表")
    private List<StudentCourseHoursErrorVO> courseHoursErrorStudents;

    /**
     * 学员课时扣减异常信息VO
     */
    @Data
    @Schema(description = "学员课时扣减异常信息VO")
    public static class StudentCourseHoursErrorVO implements Serializable {

        /**
         * 学员ID
         */
        @Schema(description = "学员ID")
        private Long studentId;

        /**
         * 学员姓名
         */
        @Schema(description = "学员姓名")
        private String studentName;

        /**
         * 学员手机号
         */
        @Schema(description = "学员手机号")
        private String studentPhone;

        /**
         * 课时扣减异常信息
         */
        @Schema(description = "课时扣减异常信息")
        private ClassStudentErrorVO errorInfo;
    }
}
