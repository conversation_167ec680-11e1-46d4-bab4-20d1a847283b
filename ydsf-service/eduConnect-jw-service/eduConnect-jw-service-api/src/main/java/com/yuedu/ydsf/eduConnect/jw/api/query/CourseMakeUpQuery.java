package com.yuedu.ydsf.eduConnect.jw.api.query;

import com.yuedu.ydsf.eduConnect.jw.api.valid.CourseMakeUpValidGroup;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.List;
import lombok.Data;

/**
 * 门店补课表 查询类
 *
 * <AUTHOR>
 * @date 2024-12-09 10:23:17
 */
@Data
@Schema(description = "门店补课表查询类")
public class CourseMakeUpQuery {


	/**
	* 主键ID
	*/
    @Schema(description="主键ID")
    @NotNull(groups = {CourseMakeUpValidGroup.CourseMakeUpDetailGroup.class}, message = "补课ID不能为空")
    private Long id;

	/**
	* 门店ID
	*/
    @Schema(description="门店ID")
    private Long storeId;

	/**
	* 已约直播课/点播课课表ID
	*/
    @Schema(description="已约直播课/点播课课表ID")
    private Long timetableId;

	/**
	* 课程ID
	*/
    @Schema(description="课程ID")
    private Long courseId;

	/**
	* 课节ID
	*/
    @Schema(description="课节ID")
    private Long lessonId;

	/**
	* 第几节课
	*/
    @Schema(description="第几节课")
    private Integer lessonOrder;

	/**
	* 上课时段ID
	*/
    @Schema(description="上课时段ID")
    private Long timeSlotId;

	/**
	* 主讲老师ID
	*/
    @Schema(description="主讲老师ID")
    private Long lectureId;

	/**
	* 上课教室ID
	*/
    @Schema(description="上课教室ID")
    private Long classRoomId;

	/**
	* 上课日期
	*/
    @Schema(description="上课日期")
    private LocalDate classDate;

	/**
	* 上课开始时间
	*/
    @Schema(description="上课开始时间")
    private LocalTime classStartTime;

	/**
	* 上课结束时间
	*/
    @Schema(description="上课结束时间")
    private LocalTime classEndTime;

	/**
	* 创建人
	*/
    @Schema(description="创建人")
    private String createBy;

	/**
	* 创建时间
	*/
    @Schema(description="创建时间")
    private LocalDateTime createTime;

	/**
	* 修改人
	*/
    @Schema(description="修改人")
    private String updateBy;

	/**
	* 修改时间
	*/
    @Schema(description="修改时间")
    private LocalDateTime updateTime;

	/**
	* 是否删除: 0-否; 1-是;
	*/
    @Schema(description="是否删除: 0-否; 1-是;")
    private Integer delFlag;

    /**
     * 课程类型
     */
    @Schema(description="课程类型")
    private Integer courseType;

    /**
     * 教学计划ID列表
     */
    @Schema(description = "教学计划ID列表")
    private List<Long> teachingPlanIdList;
}
