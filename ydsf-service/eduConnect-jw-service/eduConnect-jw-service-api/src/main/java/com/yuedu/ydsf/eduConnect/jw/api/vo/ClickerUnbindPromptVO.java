package com.yuedu.ydsf.eduConnect.jw.api.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 答题器解绑提示VO
 *
 * <AUTHOR>
 * @date 2025/06/17
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "答题器解绑提示信息")
public class ClickerUnbindPromptVO {

    /**
     * 是否需要弹框提示解绑答题器
     */
    @Schema(description = "是否需要弹框提示解绑答题器")
    private Boolean showUnbindPrompt;

    /**
     * 提示文案
     */
    @Schema(description = "提示文案")
    private String promptMessage;

    /**
     * 课次号
     */
    @Schema(description = "课次号")
    private Long lessonNo;

    /**
     * 已签到学生数量
     */
    @Schema(description = "已签到学生数量")
    private Integer checkedInCount;

    /**
     * 总学生数量
     */
    @Schema(description = "总学生数量")
    private Integer totalStudentCount;

    /**
     * 创建需要提示的VO
     *
     * @param lessonNo 课次号
     * @param checkedInCount 已签到学生数量
     * @param totalStudentCount 总学生数量
     * @param promptMessage 提示文案
     * @return ClickerUnbindPromptVO
     */
    public static ClickerUnbindPromptVO createPromptVO(Long lessonNo, Integer checkedInCount, Integer totalStudentCount, String promptMessage) {
        return new ClickerUnbindPromptVO(true, promptMessage, lessonNo, checkedInCount, totalStudentCount);
    }

    /**
     * 创建不需要提示的VO
     *
     * @param lessonNo 课次号
     * @param checkedInCount 已签到学生数量
     * @param totalStudentCount 总学生数量
     * @return ClickerUnbindPromptVO
     */
    public static ClickerUnbindPromptVO createNoPromptVO(Long lessonNo, Integer checkedInCount, Integer totalStudentCount) {
        return new ClickerUnbindPromptVO(false, null, lessonNo, checkedInCount, totalStudentCount);
    }
}
