package com.yuedu.ydsf.eduConnect.jw.api.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import java.time.LocalDateTime;
import lombok.Data;

@Data
@Schema(description = "调课课程列表返回对象")
public class AttendanceManagementVO {

  @Schema(description = "课程编号")
  private Long lessonNo;

  @Schema(description = "课程ID")
  private Long courseId;

  @Schema(description = "课程名称")
  private String courseName;

  @Schema(description = "课节序号")
  private Integer lessonOrder;

  @Schema(description = "课节名称")
  private String lessonName;

  @Schema(description = "上课时间")
  private LocalDateTime classTime;

  @Schema(description = "教室ID")
  private Long classroomId;

  @Schema(description = "教室名称")
  private String classroomName;

  @Schema(description = "班级id")
  private String classId;

  @Schema(description = "班级名称")
  private String className;

  @Schema(description = "指导老师ID")
  private Long teacherId;

  @Schema(description = "指导老师姓名")
  private String teacherName;

  @Schema(description = "主讲老师ID")
  private Long lecturerId;

  @Schema(description = "主讲老师姓名")
  private String lecturerName;

  @Schema(description = "阶段名称")
  private String stageName;

  @Schema(description = "门店id")
  private Long storeId;

  @Schema(description = "完整时间")
  private String fullClassTimeStr;

  /** 已签到人数 */
  @Schema(description = "已签到人数")
  private Integer checkedInCount;

  /** 应到人数 */
  @Schema(description = "应到人数")
  private Integer totalCount;
}
