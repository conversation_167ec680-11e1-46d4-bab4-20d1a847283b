package com.yuedu.ydsf.eduConnect.jw.api.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import java.io.Serializable;
import lombok.Data;

/**
 * 门店已约直播课 传输类
 *
 * <AUTHOR>
 * @date 2024-12-09 10:20:15
 */
@Data
@Schema(description = "门店已约直播课传输类")
public class CourseLiveDTO implements Serializable {


	/**
	* 主键ID
	*/
    @Schema(description="主键ID")
    private Long id;

	/**
	* 门店ID
	*/
    @Schema(description="门店ID")
    private Long storeId;

	/**
	* 教学计划ID
	*/
    @Schema(description="教学计划ID")
    private Long teachingPlanId;

	/**
	* 上课时段ID
	*/
    @Schema(description="上课时段ID")
    private Long timeSlotId;

    /*
     * 课程类型: 1-直播课; 2-点播课; 3-补课;
     */
    @Schema(description = "课程类型: 1-直播课; 2-点播课; 3-补课;")
    private Integer courseType;

	/**
	* 阶段
	*/
    @Schema(description="阶段")
    private Integer stage;

	/**
	* 班级ID
	*/
    @Schema(description="班级ID")
    private Long classId;

	/**
	* 教室ID
	*/
    @Schema(description="教室ID")
    private Long classroomId;

	/**
	* 指导老师ID
	*/
    @Schema(description="指导老师ID")
    private Long teacherId;
}
