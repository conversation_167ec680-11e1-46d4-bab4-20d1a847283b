package com.yuedu.ydsf.eduConnect.jw.api.dto;

import com.yuedu.ydsf.eduConnect.jw.api.valid.InteractionRedPacketSettingValidGroup;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import java.io.Serializable;
import java.time.LocalDateTime;
import lombok.Data;

/**
 * 门店红包规则设置表 传输类
 *
 * <AUTHOR>
 * @date 2024-11-04 08:41:26
 */
@Data
@Schema(description = "门店红包规则设置表传输类")
public class InteractionRedPacketSettingDTO implements Serializable {


	/**
	* 主键ID
	*/
    @Schema(description="主键ID")
    private Long id;

    /**
     * 校区ID
     */
    @Schema(description="校区ID")
    @NotNull(groups = {InteractionRedPacketSettingValidGroup.InteractionRedPacketSettingGroup.class}, message = "校区ID不能为空")
    private Long source;

    /**
     * 校管家校区ID
     */
    @Schema(description="校管家校区ID")
    private String xgjCampusId;

    /**
     * 红包总分数
     */
    @Schema(description="红包总分数")
    @NotNull(groups = {InteractionRedPacketSettingValidGroup.InteractionRedPacketSettingGroup.class}, message = "红包总分数不能为空")
    @Min(value = 1, message = "请输入大于0的有效分数，不能包含小数位和非数字字符。", groups = {
        InteractionRedPacketSettingValidGroup.InteractionRedPacketSettingGroup.class})
    private Integer redPacketNumber;

    /**
     * 红包分数上限
     */
    @Schema(description="红包分数上限")
    @NotNull(groups = {InteractionRedPacketSettingValidGroup.InteractionRedPacketSettingGroup.class}, message = "红包分数上限不能为空")
    @Min(value = 1, message = "请输入大于0的有效分数，不能包含小数位和非数字字符。", groups = {
        InteractionRedPacketSettingValidGroup.InteractionRedPacketSettingGroup.class})
    private Integer redPacketUpperLimit;

    /**
     * 红包分数下限
     */
    @Schema(description="红包分数下限")
    @NotNull(groups = {InteractionRedPacketSettingValidGroup.InteractionRedPacketSettingGroup.class}, message = "红包分数下限不能为空")
    @Min(value = 1, message = "请输入大于0的有效分数，不能包含小数位和非数字字符。", groups = {
        InteractionRedPacketSettingValidGroup.InteractionRedPacketSettingGroup.class})
    private Integer redPacketLowerLimit;

	/**
	* 创建人
	*/
    @Schema(description="创建人")
    private String createBy;

	/**
	* 创建时间
	*/
    @Schema(description="创建时间")
    private LocalDateTime createTime;

	/**
	* 修改人
	*/
    @Schema(description="修改人")
    private String updateBy;

	/**
	* 修改时间
	*/
    @Schema(description="修改时间")
    private LocalDateTime updateTime;

	/**
	* 是否删除: 0-未删除;1-已删除
	*/
    @Schema(description="是否删除: 0-未删除;1-已删除")
    private Integer delFlag;

    /**
     * 飞天登录账号
     */
    @Schema(description="飞天登录账号")
    private String managerMobile;

    /**
     * 飞天账号名称
     */
    @Schema(description="飞天账号名称")
    private String managerName;

    /**
     * 规则设置: 0-默认; 1-自定义;
     */
    @Schema(description="规则设置: 0-默认; 1-自定义;")
    @NotNull(groups = {InteractionRedPacketSettingValidGroup.InteractionRedPacketSettingGroup.class}, message = "规则设置不能为空")
    private Integer rulesSetting;

}
