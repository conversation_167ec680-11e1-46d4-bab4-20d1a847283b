package com.yuedu.ydsf.eduConnect.jw.api.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @author: yuxingkui
 * @date: 2025/3/5
 **/
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TimetableClassTimeVO {

    /**
     * 主键ID
     */
    @Schema(description="主键ID")
    private Long id;

    /**
     * 完整时间
     */
    @Schema(description="完整时间")
    private String fullClassTimeStr;
}
