package com.yuedu.ydsf.eduConnect.jw.api.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 课表签到考勤学生列表
 *
 * <AUTHOR>
 * @date 2025/02/24
 */
@Data
@Schema(description = "课表签到考勤学生列表")
public class CheckInStudentVO {

  /** 课节名称 */
  @Schema(description = "课节名称")
  private String lessonName;

  /** 已签到人数 */
  @Schema(description = "已签到人数")
  private Integer checkedInCount;

  /** 应到人数 */
  @Schema(description = "应到人数")
  private Integer totalCount;

  /** 学生列表 */
  @Schema(description = "学生列表")
  private List<StudentCheckInInfoVO> students;

  /** 上课开始日期时间 */
  @Schema(description = "上课开始日期时间")
  private LocalDateTime classStartDateTime;

  /** 上课结束日期时间 */
  @Schema(description = "上课结束日期时间")
  private LocalDateTime classEndDateTime;

  /** 课程编号 */
  @Schema(description = "课程编号")
  private Long lessonNo;

  @Data
  public static class StudentCheckInInfoVO {
    /** 课次学生ID */
    @Schema(description = "课次学生ID")
    private Long classStudentId;

    /** 学生ID */
    @Schema(description = "学生ID")
    private Long studentId;

    /** 学生姓名 */
    @Schema(description = "学生姓名")
    private String studentName;

    /** 学生手机号 */
    @Schema(description = "学生手机号")
    private String studentMobile;

    /** 学生性别 */
    @Schema(description = "学生性别: 0-未知; 1-男; 2-女")
    private Integer gender;

    /** 剩余总课次 */
    @Schema(description = "剩余总课次")
    private Integer remainingLessons;

    /** 阶段名称 */
    @Schema(description = "阶段名称")
    private String stageName;

    /** 学员状态 */
    @Schema(description = "状态 0:试听,1:在读,3:休学,99:退学,-1:暂时不明确")
    private Integer status;

    /** 课次学生类型 */
    @Schema(description = "课次学生类型: 1-班级学生; 2-调课学生; 3-添加考勤学生; 4-临加学员")
    private Integer studentType;

    /** 是否为临加学员 */
    @Schema(description = "是否为临加学员: true-是; false-否")
    private Boolean isTemporary;

    /** 考勤状态 */
    @Schema(description = "考勤状态: 0-未出勤(缺勤); 1-已考勤")
    private Integer checkInStatus;

    /** 阶段Id */
    @Schema(description = "阶段Id", requiredMode = Schema.RequiredMode.REQUIRED)
    private Integer stageId;

    /** 课程编号 */
    @Schema(description = "课程编号")
    private Long lessonNo;

    /** 接收器SN码 */
    private String receiverSnNumber;

    /** 答题器SN码 */
    private String clickerSnNumber;

    /** 绑定答题器状态 */
    private String bindClickerStatus;

    /** 绑定答题器状态枚举 */
    private Integer bindClickerStatusType;

    /** 考勤时间 */
    private LocalDateTime checkInTime;

    /** 预考勤状态：true-产生互动数据且课消校验正常；false-未满足预考勤条件 */
    @Schema(description = "预考勤状态：true-产生互动数据且课消校验正常；false-未满足预考勤条件")
    private Boolean preAttendanceStatus;

    /** 原始考勤状态：0-未考勤；1-已考勤；2-预考勤 */
    @Schema(description = "原始考勤状态：0-未考勤；1-已考勤；2-预考勤")
    private Integer originalAttendanceStatus;

      /**
       * 姓名拼音首字母
       */
      @Schema(description = "姓名拼音首字母")
      private String pinyinPre;
  }
}
