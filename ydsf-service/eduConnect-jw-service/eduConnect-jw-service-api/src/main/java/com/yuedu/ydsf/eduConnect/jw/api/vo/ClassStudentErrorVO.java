package com.yuedu.ydsf.eduConnect.jw.api.vo;

import com.yuedu.ydsf.eduConnect.jw.api.constant.enums.ClassStudentErrorEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;

import java.util.List;

/**
 * 考勤签到异常类
 *
 * @date 2025/2/27 17:01
 * @project @Title: ClassStudentErrorVO.java
 */
@Data
public class ClassStudentErrorVO {

    public ClassStudentErrorVO() {}

    public ClassStudentErrorVO(ClassStudentErrorEnum errorEnum) {
        this.code = errorEnum.getCode();
        this.msg = errorEnum.getDesc();
    }

    /**
     * 错误码
     */
    @Schema(description = "code")
    private Integer code = 0;

    /**
     * 错误信息
     */
    @Schema(description = "错误信息")
    private String msg;

    /**
     * 当前课程的课程类型ID（用于后续签到接口传入）
     */
    @Schema(description = "当前课程的课程类型ID")
    private Integer currentCourseTypeId;

    /**
     * 其他可用课程类型列表（当需要选择其他课程类型扣减时返回）
     */
    @Schema(description = "其他可用课程类型列表")
    private List<CourseTypeInfoVO> otherCourseTypes;

    @Data
    @Schema(description = "课程类型信息")
    public static class CourseTypeInfoVO {
        /** 课程类型ID */
        @Schema(description = "课程类型ID")
        private Integer courseTypeId;

        /** 课程类型名称 */
        @Schema(description = "课程类型名称")
        private String courseTypeName;

        /** 剩余课次 */
        @Schema(description = "剩余课次")
        private Integer remainingSessions;

        /** 正式课次 */
        @Schema(description = "正式课次")
        private Integer formalSessions;

        /** 赠送课次 */
        @Schema(description = "赠送课次")
        private Integer giftSessions;
    }
}
