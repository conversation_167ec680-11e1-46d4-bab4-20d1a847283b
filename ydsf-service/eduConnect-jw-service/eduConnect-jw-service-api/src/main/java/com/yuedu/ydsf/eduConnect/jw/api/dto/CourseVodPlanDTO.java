package com.yuedu.ydsf.eduConnect.jw.api.dto;

import com.yuedu.ydsf.eduConnect.jw.api.valid.CourseVodValidGroup;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import java.io.Serializable;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import lombok.Data;

/**
 * 门店已约点播课排期表 传输类
 *
 * <AUTHOR>
 * @date 2024-12-09 10:25:38
 */
@Data
@Schema(description = "门店已约点播课排期表传输类")
public class CourseVodPlanDTO implements Serializable {


	/**
	* 主键ID
	*/
    @Schema(description="主键ID")
    @NotNull(groups = {CourseVodValidGroup.AddOrderedStoreCourseVodGroup.class,
        CourseVodValidGroup.SaveStoreCourseVodPlanGroup.class}, message = "门店已约点播课排期ID不能为空")
    private Long id;

	/**
	* 门店ID
	*/
    @Schema(description="门店ID")
    private Long storeId;

	/**
	* 门店已约点播课ID
	*/
    @Schema(description="门店已约点播课ID")
    private Long vodCourseId;

	/**
	* 课程ID
	*/
    @Schema(description="课程ID")
    @NotNull(groups = {CourseVodValidGroup.AddOrderedStoreCourseVodGroup.class,
        CourseVodValidGroup.SaveStoreCourseVodPlanGroup.class}, message = "课程ID不能为空")
    private Long courseId;

	/**
	* 上课时段ID
	*/
    @Schema(description="上课时段ID")
    @NotNull(groups = {CourseVodValidGroup.AddOrderedStoreCourseVodGroup.class,
        CourseVodValidGroup.SaveStoreCourseVodPlanGroup.class}, message = "上课时段ID不能为空")
    private Long timeSlotId;

	/**
	* 第几节课
	*/
    @Schema(description="第几节课")
    @NotNull(groups = {CourseVodValidGroup.AddOrderedStoreCourseVodGroup.class,
        CourseVodValidGroup.SaveStoreCourseVodPlanGroup.class}, message = "第几节课不能为空")
    private Integer lessonOrder;

	/**
	* 上课日期
	*/
    @Schema(description="上课日期")
    @NotNull(groups = {CourseVodValidGroup.AddOrderedStoreCourseVodGroup.class,
        CourseVodValidGroup.SaveStoreCourseVodPlanGroup.class}, message = "上课日期不能为空")
    private LocalDate classDate;

	/**
	* 上课开始时间
	*/
    @Schema(description="上课开始时间")
    @NotNull(groups = {CourseVodValidGroup.AddOrderedStoreCourseVodGroup.class,
        CourseVodValidGroup.SaveStoreCourseVodPlanGroup.class}, message = "上课开始时间不能为空")
    private LocalTime classStartTime;

	/**
	* 上课结束时间
	*/
    @Schema(description="上课结束时间")
    @NotNull(groups = {CourseVodValidGroup.AddOrderedStoreCourseVodGroup.class,
        CourseVodValidGroup.SaveStoreCourseVodPlanGroup.class}, message = "上课结束时间不能为空")
    private LocalTime classEndTime;

    /**
     * 课程版本
     */
    @Schema(description="课程版本")
    private Integer courseVersion;

    /**
     * 课件版本
     */
    @Schema(description="课件版本")
    private Integer coursewareVersion;

	/**
	* 创建人
	*/
    @Schema(description="创建人")
    private String createBy;

	/**
	* 创建时间
	*/
    @Schema(description="创建时间")
    private LocalDateTime createTime;

	/**
	* 修改人
	*/
    @Schema(description="修改人")
    private String updateBy;

	/**
	* 修改时间
	*/
    @Schema(description="修改时间")
    private LocalDateTime updateTime;

	/**
	* 是否删除: 0-否; 1-是;
	*/
    @Schema(description="是否删除: 0-否; 1-是;")
    private Integer delFlag;

    /**
     * 时段类型: 1-上午; 2-下午; 3-晚上;
     */
    @Schema(description="时段类型: 1-上午; 2-下午; 3-晚上;")
    private Integer timeSlotType;

}
