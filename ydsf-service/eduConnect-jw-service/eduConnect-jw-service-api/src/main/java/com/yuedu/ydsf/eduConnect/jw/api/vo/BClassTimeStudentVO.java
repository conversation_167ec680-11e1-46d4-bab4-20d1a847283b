package com.yuedu.ydsf.eduConnect.jw.api.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import java.time.LocalDateTime;

/**
 * 课次学生表
 *
 * <AUTHOR>
 * @date 2025/02/24
 */
@Data
@Schema(description = "课次学生表展示对象")
public class BClassTimeStudentVO {

  /** 主键ID */
  @Schema(description = "主键ID")
  private Long id;

  /** 门店ID */
  @Schema(description = "门店ID")
  private Long storeId;

  /** 学生id */
  @Schema(description = "学生id")
  private Long studentId;

  /**
   * 是否调出:0-未调出;1-已调出
   */
  @Schema(description = "是否调出:0-未调出;1-已调出")
  private Integer adjustStatus;

  /** 学生课次类型:1-班级学生;2-调课学生;3-添加考勤学生 */
  @Schema(description = "学生课次类型:1-班级学生;2-调课学生;3-添加考勤学生")
  private Integer studentType;

  /** 课次编号 */
  @Schema(description = "课次编号")
  private Long lessonNo;

  /** 考勤状态:0-未出勤(缺勤);1-已考勤 */
  @Schema(description = "考勤状态:0-未出勤(缺勤);1-已考勤")
  private Integer checkInStatus;

  /** 考勤类型:0-正常签到;1-补签 */
  @Schema(description = "考勤类型:0-正常签到;1-补签")
  private Integer checkInType;

  /** 考勤时间 */
  @Schema(description = "考勤时间")
  private LocalDateTime checkInTime;

  /** 考勤操作人 */
  @Schema(description = "考勤操作人")
  private String checkInCreateBy;

  /** 创建人 */
  @Schema(description = "创建人")
  private String createBy;

  /** 创建时间 */
  @Schema(description = "创建时间")
  private LocalDateTime createTime;

  /** 修改人 */
  @Schema(description = "修改人")
  private String updateBy;

  /** 修改时间 */
  @Schema(description = "修改时间")
  private LocalDateTime updateTime;

  /** 是否删除: 0-否; 1-是; */
  @Schema(description = "是否删除: 0-否; 1-是;")
  private Byte delFlag;
}
