package com.yuedu.ydsf.eduConnect.jw.api.query;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2025/02/12
 */
@Data
@Schema(description = "目标排课列表查询对象")
public class TimetableChangeTargetQuery {

  @Schema(description = "学员ID")
  @NotNull(message = "学员ID不能为空")
  private Long studentId;

  @Schema(description = "原课程ID")
  @NotNull(message = "原课程ID不能为空")
  private Long sourceCourseId;

  @Schema(description = "原课节序号")
  @NotNull(message = "原课节序号不能为空")
  private Integer sourceLessonOrder;

  @Schema(description = "原课节号")
  @NotNull(message = "原课节号")
  private Long sourceLessonNo;

  @Schema(description = "开始时间")
  private LocalDateTime startTime;

  @Schema(description = "结束时间")
  private LocalDateTime endTime;
}
