package com.yuedu.ydsf.eduConnect.jw.api.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 考勤确认结果VO
 *
 * <AUTHOR>
 * @date 2025/07/09
 */
@Data
@Schema(description = "考勤确认结果VO")
public class AttendanceConfirmationResultVO implements Serializable {

    /** 是否全部成功 */
    @Schema(description = "是否全部成功")
    private Boolean allSuccess;

    /** 成功处理的学员数量 */
    @Schema(description = "成功处理的学员数量")
    private Integer successCount;

    /** 失败处理的学员数量 */
    @Schema(description = "失败处理的学员数量")
    private Integer failureCount;

    /** 成功处理的学员列表 */
    @Schema(description = "成功处理的学员列表")
    private List<StudentOperationResultVO> successResults;

    /** 失败处理的学员列表 */
    @Schema(description = "失败处理的学员列表")
    private List<StudentOperationResultVO> failureResults;

    /** 需要用户处理的错误列表 */
    @Schema(description = "需要用户处理的错误列表")
    private List<StudentErrorResultVO> errorResults;

    @Data
    @Schema(description = "学员操作结果VO")
    public static class StudentOperationResultVO implements Serializable {

        /** 学生ID */
        @Schema(description = "学生ID")
        private Long studentId;

        /** 学生姓名 */
        @Schema(description = "学生姓名")
        private String studentName;

        /** 学生手机号 */
        @Schema(description = "学生手机号")
        private String studentPhone;

        /** 操作类型 */
        @Schema(description = "操作类型：1-签到；2-取消考勤；3-补签；4-无操作")
        private Integer operationType;

        /** 操作结果描述 */
        @Schema(description = "操作结果描述")
        private String resultMessage;

        /** 是否成功 */
        @Schema(description = "是否成功")
        private Boolean success;
    }

    @Data
    @Schema(description = "学员错误结果VO")
    public static class StudentErrorResultVO implements Serializable {

        /** 学生ID */
        @Schema(description = "学生ID")
        private Long studentId;

        /** 学生姓名 */
        @Schema(description = "学生姓名")
        private String studentName;

        /** 学生手机号 */
        @Schema(description = "学生手机号")
        private String studentPhone;

        /** 操作类型 */
        @Schema(description = "操作类型：1-签到；2-取消考勤；3-补签；4-无操作")
        private Integer operationType;

        /** 错误信息 */
        @Schema(description = "错误信息")
        private ClassStudentErrorVO errorInfo;
    }

    /**
     * 创建全部成功的结果
     */
    public static AttendanceConfirmationResultVO createAllSuccessResult(List<StudentOperationResultVO> successResults) {
        AttendanceConfirmationResultVO result = new AttendanceConfirmationResultVO();
        result.setAllSuccess(true);
        result.setSuccessCount(successResults.size());
        result.setFailureCount(0);
        result.setSuccessResults(successResults);
        result.setFailureResults(List.of());
        result.setErrorResults(List.of());
        return result;
    }

    /**
     * 创建部分失败的结果
     */
    public static AttendanceConfirmationResultVO createPartialFailureResult(
            List<StudentOperationResultVO> successResults,
            List<StudentOperationResultVO> failureResults,
            List<StudentErrorResultVO> errorResults) {
        AttendanceConfirmationResultVO result = new AttendanceConfirmationResultVO();
        result.setAllSuccess(false);
        result.setSuccessCount(successResults.size());
        result.setFailureCount(failureResults.size() + errorResults.size());
        result.setSuccessResults(successResults);
        result.setFailureResults(failureResults);
        result.setErrorResults(errorResults);
        return result;
    }
}
