package com.yuedu.ydsf.eduConnect.jw.api.query;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import java.time.LocalDateTime;

/**
 * 学生家长的微信消息 查询类
 *
 * <AUTHOR>
 * @date 2025-07-11 11:53:36
 */
@Data
@Schema(description = "学生家长的微信消息查询类")
public class WxStudentMsgQuery {


	/**
	* 主键
	*/
    @Schema(description="主键")
    private Long id;

	/**
	* 公众号名称
	*/
    @Schema(description="公众号名称")
    private String appName;

	/**
	* 校区ID
	*/
    @Schema(description="校区ID")
    private Long schoolId;

	/**
	* 门店ID
	*/
    @Schema(description="门店ID")
    private Long storeId;

	/**
	* 学生ID
	*/
    @Schema(description="学生ID")
    private Long studentId;

	/**
	* 对象ID（如：课次编号 lessonNo）
	*/
    @Schema(description="对象ID（如：课次编号 lessonNo）")
    private Long objId;

	/**
	* 对象类型（如：同课程类型）
	*/
    @Schema(description="对象类型（如：同课程类型）")
    private Integer objType;

	/**
	* 开始上课时间
	*/
    @Schema(description="开始上课时间")
    private LocalDateTime classStartTime;

	/**
	* 消息分类（1、用户发给公众号；2、公众号发给用户；）
	*/
    @Schema(description="消息分类（1、用户发给公众号；2、公众号发给用户；）")
    private Integer type;

	/**
	* 消息类型（text：文本；image：图片；voice：语音；video：视频；shortvideo：小视频；location：地理位置；music：音乐；news：图文；event：推送事件）
	*/
    @Schema(description="消息类型（text：文本；image：图片；voice：语音；video：视频；shortvideo：小视频；location：地理位置；music：音乐；news：图文；event：推送事件）")
    private String repType;

	/**
	* 事件类型（1、上课提醒；2、课消提醒；）
	*/
    @Schema(description="事件类型（1、上课提醒；2、课消提醒；）")
    private Integer repEvent;

	/**
	* 回复类型文本保存文字、地理位置信息
	*/
    @Schema(description="回复类型文本保存文字、地理位置信息")
    private String repContent;

	/**
	* 回复类型imge、voice、news、video的mediaID或音乐缩略图的媒体id
	*/
    @Schema(description="回复类型imge、voice、news、video的mediaID或音乐缩略图的媒体id")
    private String repMediaId;

	/**
	* 回复的素材名、视频和音乐的标题
	*/
    @Schema(description="回复的素材名、视频和音乐的标题")
    private String repName;

	/**
	* 视频和音乐的描述
	*/
    @Schema(description="视频和音乐的描述")
    private String repDesc;

	/**
	* 链接
	*/
    @Schema(description="链接")
    private String repUrl;

	/**
	* 图文消息的内容
	*/
    @Schema(description="图文消息的内容")
    private String content;

	/**
	* 消息发送状态（0：未发送；1：已发送；2：发送失败；）
	*/
    @Schema(description="消息发送状态（0：未发送；1：已发送；2：发送失败；）")
    private Integer sendStatus;

	/**
	* 已读标记（1：是；0：否）
	*/
    @Schema(description="已读标记（1：是；0：否）")
    private Integer readFlag;

	/**
	* 公众号ID
	*/
    @Schema(description="公众号ID")
    private String appId;

	/**
	* 微信唯一标识
	*/
    @Schema(description="微信唯一标识")
    private String openId;

	/**
	* 备注
	*/
    @Schema(description="备注")
    private String remark;

	/**
	* 创建时间
	*/
    @Schema(description="创建时间")
    private LocalDateTime createTime;

	/**
	* 更新时间
	*/
    @Schema(description="更新时间")
    private LocalDateTime updateTime;

	/**
	* 删除标记，0未删除，1已删除
	*/
    @Schema(description="删除标记，0未删除，1已删除")
    private Integer delFlag;
}
