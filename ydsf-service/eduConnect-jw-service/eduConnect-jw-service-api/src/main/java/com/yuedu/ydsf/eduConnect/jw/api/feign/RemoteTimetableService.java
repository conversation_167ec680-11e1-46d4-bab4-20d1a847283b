package com.yuedu.ydsf.eduConnect.jw.api.feign;

import com.yuedu.ydsf.common.core.constant.ServiceNameConstants;
import com.yuedu.ydsf.common.core.util.R;
import com.yuedu.ydsf.common.core.util.RetOps;
import com.yuedu.ydsf.common.feign.annotation.NoToken;
import com.yuedu.ydsf.eduConnect.jw.api.dto.TimetableDTO;
import com.yuedu.ydsf.eduConnect.jw.api.query.TimetableQuery;
import com.yuedu.ydsf.eduConnect.jw.api.vo.BClassTimeStudentVO;
import com.yuedu.ydsf.eduConnect.jw.api.vo.TimetableClassTimeVO;
import com.yuedu.ydsf.eduConnect.jw.api.vo.TimetableVO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.Mapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import java.util.List;

/**
 * 查询门店课表
 * <AUTHOR>
 * @date 2024/12/17 18:27
 */
@FeignClient(contextId = "remoteTimetableService", value = ServiceNameConstants.EDU_CONNECT_JW_SERVICE)
public interface RemoteTimetableService {

    /**
     * 查询门店下老师是否存在未结束课程
     * @return true: 存在; false: 不存在
     * <AUTHOR>
     * @date 2025/2/11 11:12
     */
    @PostMapping("/timetable/existNotFinishedByTeacherId")
    @NoToken
    R<Boolean> existNotFinishedByTeacherId(@RequestBody TimetableDTO timetableDTO);

    /**
     * 查询班级下是否存在排课课程
     * @param timetableDTO
     * @return com.yuedu.ydsf.common.core.util.R<java.lang.Boolean>
     * <AUTHOR>
     * @date 2025/2/12 15:12
     */
    @PostMapping("/timetable/existScheduleCourseByClassId")
    @NoToken
    R<Boolean> existScheduleCourseByClassId(@RequestBody TimetableDTO timetableDTO);

    /**
     * 查询教室id下是否存在排课
     * @param timetableDTO DTO
     * @return R<Boolean>
     */
    @PostMapping("/timetable/existScheduleCourseByClassRoomId")
    @NoToken
    R<Boolean> existScheduleCourseByClassRoomId(@RequestBody TimetableDTO timetableDTO);

    /**
     * 根据ID获取排课列表
     * @param timetableDTO DTO
     * @return List<TimetableVO>
     */
    @PostMapping("/timetable/getListById")
    @NoToken
    R<List<TimetableVO>> getListById(@RequestBody TimetableDTO timetableDTO);

    /**
     * 根据ID获取排课列表
     * @param ids ids
     * @return List<TimetableClassTimeVO>
     */
    @PostMapping("/timetable/getListByIds")
    @NoToken
    R<List<TimetableClassTimeVO>> getListByIds(@RequestBody List<Long> ids);


    /**
     *
     *  根据课号查询对应学生列表
     *
     * <AUTHOR>
     * @date 2025年03月11日 16时26分
     */
    @PostMapping("/timetable/getClassTimeStudentListByIds")
    @NoToken
    R<List<BClassTimeStudentVO>> getBClassTimeStudentList(@RequestBody List<Long> ids);

    /**
     * 根据上课日期查询课次ID
     */
    @PostMapping("/timetable/listTimeTableIdByClassDate")
    @NoToken
    R<List<Long>> listTimeTableIdByClassDate(@RequestBody TimetableQuery timetableQuery);

    /**
     * 根据补课id找出对应的原上课的课次相关信息
     * <AUTHOR>
     * @date 2025/4/29 10:10
     * @param makeUpOnLineId
     * @return com.yuedu.ydsf.eduConnect.jw.api.vo.TimetableVO
     */
    @GetMapping("/timetable/getOriginalTimetable/{makeUpOnLineId}")
    @NoToken
    TimetableVO getOriginalTimetable(@PathVariable("makeUpOnLineId") Long makeUpOnLineId);

    /**
     * 根据课表ID获取课表详情
     * @param timetableId
     * @return TimetableVO
     */
    @GetMapping("/timetable/getTimetableInfoByTimetableId/{timetableId}")
    @NoToken
    R<TimetableVO> getTimetableInfoById(@PathVariable("timetableId") Long timetableId);

    /**
     * 根据IDs获取排课列表
     * @param ids ids
     * @return List<TimetableClassTimeVO>
     */
    @PostMapping("/timetable/getTimetableInfoByTimetableIds")
    @NoToken
    R<List<TimetableVO>> getTimetableInfoByTimetableIds(@RequestBody List<Long> ids,@RequestParam("storeId")Long storeId);
    /**
     *  根据门店ID查询已约课程ID列表
     *
     * <AUTHOR>
     * @date 2025年06月05日 16时28分
     */
    @GetMapping("/timetable/getCourseListByStoreId/{storeId}")
    @NoToken
    R<List<Long>> getCourseListByStoreId(Long storeId);


}
