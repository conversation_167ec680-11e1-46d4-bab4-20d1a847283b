package com.yuedu.ydsf.eduConnect.jw.api.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 门店红包规则设置表 视图类
 *
 * <AUTHOR>
 * @date 2024-11-04 08:41:26
 */
@Data
@Schema(description = "门店红包规则设置表视图类")
public class InteractionRedPacketSettingVO {


    /**
     * 主键ID
     */
    @Schema(description = "主键ID")
    private Long id;

    /**
     * 校区ID
     */
    @Schema(description = "校区ID")
    private Long source;

    /**
     * 校管家校区ID
     */
    @Schema(description = "校管家校区ID")
    private String xgjCampusId;

    /**
     * 红包总分数
     */
    @Schema(description = "红包总分数")
    private Integer redPacketNumber;

    /**
     * 红包分数上限
     */
    @Schema(description = "红包分数上限")
    private Integer redPacketUpperLimit;

    /**
     * 红包分数下限
     */
    @Schema(description = "红包分数下限")
    private Integer redPacketLowerLimit;

    /**
     * 创建人
     */
    @Schema(description = "创建人")
    private String createBy;

    /**
     * 创建时间
     */
    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    /**
     * 修改人
     */
    @Schema(description = "修改人")
    private String updateBy;

    /**
     * 修改时间
     */
    @Schema(description = "修改时间")
    private LocalDateTime updateTime;

    /**
     * 是否删除: 0-未删除;1-已删除
     */
    @Schema(description = "是否删除: 0-未删除;1-已删除")
    private Integer delFlag;
}

