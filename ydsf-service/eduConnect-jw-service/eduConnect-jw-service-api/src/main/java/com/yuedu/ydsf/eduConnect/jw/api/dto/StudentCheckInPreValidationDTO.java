package com.yuedu.ydsf.eduConnect.jw.api.dto;

import com.yuedu.ydsf.eduConnect.jw.api.valid.BClassTimeStudentValidGroup;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.io.Serializable;

/**
 * 学生签到预校验请求DTO
 *
 * <AUTHOR>
 * @date 2025/12/26
 */
@Data
@Schema(description = "学生签到预校验请求DTO")
public class StudentCheckInPreValidationDTO implements Serializable {

    /** 学生ID */
    @Schema(description = "学生ID")
    @NotNull(
        groups = {BClassTimeStudentValidGroup.PreValidateCheckIn.class},
        message = "学生ID不能为空")
    private Long studentId;

    /** 课次编号 */
    @Schema(description = "课次编号")
    @NotNull(
        groups = {BClassTimeStudentValidGroup.PreValidateCheckIn.class},
        message = "课次编号不能为空")
    private Long lessonNo;

    /** 门店ID */
    @Schema(description = "门店ID")
    @NotNull(
        groups = {BClassTimeStudentValidGroup.PreValidateCheckIn.class},
        message = "门店ID不能为空")
    private Long storeId;

    /** 是否强制签到 */
    @Schema(description = "是否强制签到: 0-否; 1-是")
    private Integer forceCheckIn = 0;

    /** 指定扣减的课程类型ID（当选择其他课程类型扣减时传入） */
    @Schema(description = "指定扣减的课程类型ID")
    private Integer selectedCourseType;
}
