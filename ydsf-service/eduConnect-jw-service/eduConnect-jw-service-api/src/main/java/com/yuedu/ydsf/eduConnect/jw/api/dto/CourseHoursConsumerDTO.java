package com.yuedu.ydsf.eduConnect.jw.api.dto;


import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@Schema(description = "课时消费记录")
public class CourseHoursConsumerDTO {

    /**
     * 校区编号
     */
    @Schema(description = "校区编号")
    private String schoolNo;

    /**
     * 校区名称
     */
    @Schema(description = "校区名称")
    private String schoolName;

    /**
     * 计费数量
     */
    @Schema(description = "计费数量")
    private Integer num;

    /**
     * 计费总金额
     */
    @Schema(description = "计费总金额")
    private Double totalCourseFee;
}
