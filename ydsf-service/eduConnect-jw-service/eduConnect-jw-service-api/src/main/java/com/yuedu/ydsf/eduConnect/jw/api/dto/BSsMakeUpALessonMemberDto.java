package com.yuedu.ydsf.eduConnect.jw.api.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import java.util.Date;

import lombok.Data;

/**
 * 双师补课学员表 传输类
 *
 * <AUTHOR> @date 2023年5月10日
 */
@Data
public class BSsMakeUpALessonMemberDto {

  /** 主健ID */
  private Long id;

  /** 匹配补课视频ID */
  private Long makeUpALessonVideoId;

  /** 会员表ID(通过校管家学员名称,校管家学员手机号在会员表匹配到的ID) */
  private Long memberId;

  /** 双师主讲老师设置表ID */
  @JsonProperty(value = "bSsLecturerId")
  private Long bSsLecturerId;

  /** 校管家门店名称 */
  private String schName;

  /** 校管家编号 */
  private String schCode;

  /** 校管家id */
  private String schIdCode;

  /** 校管家学员ID */
  private String schStudentId;

  /** 校管家补课学员名称 */
  private String memberName;

  /** 校管家补课学员手机号 */
  private String memberPhone;

  /** 校管家主讲老师名称 */
  private String lecturerName;

  /** 校管家授课年级 */
  private String ssLevel;

  /** 原考勤班级 */
  private String originalCourseName;

  /** 原考勤班级上课开始时间 */
  private Long originalCourseDate;

  /** 校管家补课班ID */
  private String makeUpALessonCourseId;

  /** 校管家补课班学生ID */
  private String makeUpALessonCourseStudentId;

  /** 补课班上课开始时间 */
  private Long makeUpALessonCourseDate;

  /** 补课班上课结束时间 */
  private Long makeUpALessonCourseEndDate;

  /** 补课状态: 0-补课; 1-取消补课; */
  private Integer makeUpALessonStatus;

  /** 匹配状态: 0-未匹配; 1-匹配成功; 2-匹配失败; */
  private Integer matchStatus;

  /** 出勤状态: 0-未出勤；1-已出勤 */
  private Integer attendStatus;

  /** 创建人 */
  private String creator;

  /** 创建时间 */
  @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
  private Date ctime;

  /** 编辑人 */
  private String modifer;

  /** 编辑时间 */
  @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
  private Date mtime;

  /** 匹配状态多个: 0-未匹配; 1-匹配成功; 2-匹配失败; */
  private String matchStatusString;
}
