package com.yuedu.ydsf.eduConnect.jw.api.query;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.time.LocalDate;

@Data
public class CourseHoursConsumerQuery {
    /**
     * 开始时间
     */
    @NotNull(message = "开始时间不能为空")
    @Schema(description = "上课开始时间")
    private LocalDate startDate;

    /**
     * 结束时间
     */
    @NotNull(message = "结束时间不能为空")
    @Schema(description = "上课结束时间")
    private LocalDate endDate;

    /**
     * 学校编号
     */
    @Schema(description = "学校编号")
    private String schoolNo;
}
