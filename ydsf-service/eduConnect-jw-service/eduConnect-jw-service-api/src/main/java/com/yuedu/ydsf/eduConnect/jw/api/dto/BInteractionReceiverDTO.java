package com.yuedu.ydsf.eduConnect.jw.api.dto;

import com.yuedu.ydsf.common.core.util.V_A_E;
import com.yuedu.ydsf.common.core.util.V_E;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import org.hibernate.validator.constraints.Length;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 互动接收器
 *
 * <AUTHOR>
 * @date 2025/04/09
 */
@Data
@Schema(description = "互动接收器传输对象")
public class BInteractionReceiverDTO implements Serializable {

  /** 主键ID */
  @NotNull(
      groups = {V_E.class},
      message = "主键ID不能为空")
  private Long id;

  /** 门店ID */
  @Schema(description = "门店ID")
  @NotNull(
      groups = {V_A_E.class},
      message = "门店ID不能为空")
  private Long storeId;

  /** 教室ID */
  @Schema(description = "教室ID")
  @NotNull(
      groups = {V_A_E.class},
      message = "教室ID不能为空")
  private Long classroomId;

  /** 接收器sn码 */
  @Schema(description = "接收器sn码")
  @NotBlank(
      groups = {V_A_E.class},
      message = "接收器sn码不能为空")
  @Length(
      groups = {V_A_E.class},
      max = 255,
      message = "接收器sn码长度不能大于255")
  private String snNumber;

  /** 接收器型号 */
  @Schema(description = "接收器型号")
  @NotBlank(
      groups = {V_A_E.class},
      message = "接收器型号不能为空")
  @Length(
      groups = {V_A_E.class},
      max = 255,
      message = "接收器型号长度不能大于255")
  private String modelNo;

  /** 接收器通道号 */
  @Schema(description = "接收器通道号")
  @NotNull(
      groups = {V_A_E.class},
      message = "接收器通道号不能为空")
  private Long aisle;

  /** 接收器状态: 0-启用; 1-禁用; */
  @Schema(description = "接收器状态: 0-启用; 1-禁用;")
  @NotNull(
      groups = {V_A_E.class},
      message = "接收器状态不能为空")
  private Byte receiverState;

  /** 创建人 */
  @Schema(description = "创建人")
  @Length(
      groups = {V_A_E.class},
      max = 255,
      message = "创建人长度不能大于255")
  private String createBy;

  /** 创建时间 */
  @Schema(description = "创建时间")
  @NotNull(
      groups = {V_A_E.class},
      message = "创建时间不能为空")
  private LocalDateTime createTime;

  /** 修改人 */
  @Schema(description = "修改人")
  @Length(
      groups = {V_A_E.class},
      max = 255,
      message = "修改人长度不能大于255")
  private String updateBy;

  /** 修改时间 */
  @Schema(description = "修改时间")
  @NotNull(
      groups = {V_A_E.class},
      message = "修改时间不能为空")
  private LocalDateTime updateTime;

  /** 是否删除: 0-否; 1-是; */
  @Schema(description = "是否删除: 0-否; 1-是;")
  @NotNull(
      groups = {V_A_E.class},
      message = "是否删除不能为空")
  private Byte delFlag;
}
