package com.yuedu.ydsf.eduConnect.jw.api.query;

import com.yuedu.ydsf.eduConnect.jw.api.valid.BMakeUpOnlineValidGroup;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import java.time.LocalDateTime;
import java.util.Date;
import io.swagger.v3.oas.annotations.media.Schema;


/**
* 门店线上补课表
*
* <AUTHOR>
* @date  2025/04/23
*/
@Data
@Schema(description = "门店线上补课表查询对象")
public class BCourseMakeUpOnlineQuery {

    /**
     * 主键ID(雪花id生成)
     */
    @Schema(description = "主键ID(雪花id生成)")
    @NotNull(
        groups = {BMakeUpOnlineValidGroup.GetMakeUpOnlineInfoEdit.class},
        message = "数据ID不能为空")
    private Long id;

    /**
     * 门店ID
     */
    @Schema(description = "门店ID")
    private Long storeId;

    /**
     * 课表号
     */
    @Schema(description = "课表号")
    @NotNull(
        groups = {BMakeUpOnlineValidGroup.GetMakeUpOnlineInfoAdd.class,BMakeUpOnlineValidGroup.CheckHasResources.class},
        message = "课表号不能为空")
    private Long lessonNo;

    /**
     * 教学计划ID
     */
    @Schema(description = "教学计划ID")
    private Long teachingPlanId;

    /**
     * 课程ID
     */
    @Schema(description = "课程ID")
    private Long courseId;

    /**
     * 第几节课
     */
    @Schema(description = "第几节课")
    private Integer lessonOrder;

    /**
     * 上课时段ID
     */
    @Schema(description = "上课时段ID")
    private Long timeSlotId;

    /**
     * 主讲老师ID
     */
    @Schema(description = "主讲老师ID")
    private Long lectureId;

    /**
     * 上课教室ID
     */
    @Schema(description = "上课教室ID")
    private Long classRoomId;

    /**
     * 班级ID
     */
    @Schema(description = "班级ID")
    private Long classId;

    /**
     * 上课日期
     */
    @Schema(description = "上课日期")
    private LocalDateTime classDate;

    /**
     * 上课开始时间
     */
    @Schema(description = "上课开始时间")
    private Date classStartTime;

    /**
     * 上课结束时间
     */
    @Schema(description = "上课结束时间")
    private Date classEndTime;

    /**
     * 补课有效期开始时间
     */
    @Schema(description = "补课有效期开始时间")
    private LocalDateTime validityStartTime;

    /**
     * 补课有效期结束时间
     */
    @Schema(description = "补课有效期结束时间")
    private LocalDateTime validityEndTime;

    /**
     * 创建人
     */
    @Schema(description = "创建人")
    private String createBy;

    /**
     * 创建时间
     */
    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    /**
     * 修改人
     */
    @Schema(description = "修改人")
    private String updateBy;

    /**
     * 修改时间
     */
    @Schema(description = "修改时间")
    private LocalDateTime updateTime;

    /**
     * 是否删除: 0-否; 1-是;
     */
    @Schema(description = "是否删除: 0-否; 1-是;")
    private Byte delFlag;

    /**
     * 上课开始时间
     */
    /**
     * 上课日期
     */
    @Schema(description = "上课日期")
    private LocalDateTime startClassDate;

    /**
     * 上课结束时间
     */
    @Schema(description = "上课结束时间")
    private LocalDateTime endClassDate;

}
