package com.yuedu.ydsf.eduConnect.jw.api.dto;

import com.yuedu.ydsf.eduConnect.jw.api.valid.TimetableValidGroup;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import java.io.Serializable;
import java.time.LocalDate;
import java.util.List;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 门店课表 传输类
 *
 * <AUTHOR>
 * @date 2024-12-09 10:27:50
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Schema(description = "门店课表传输类")
public class TimetableDTO implements Serializable {


    /**
     * 主键ID
     */
    @Schema(description = "主键ID")
    private Long id;

    /**
     * 主键ID集合
     */
    @Schema(description = "主键ID集合")
    private List<Long> idList;

    /**
     * 门店ID
     */
    @Schema(description = "门店ID")
    @NotNull(groups = {TimetableValidGroup.ExistNotFinishedByTeacherIdGroup.class}, message = "门店ID不能为空")
    private Long storeId;

    /**
     * 指导老师ID
     */
    @Schema(description = "指导老师ID")
    @NotNull(groups = {TimetableValidGroup.ExistNotFinishedByTeacherIdGroup.class}, message = "老师ID不能为空")
    private Long teacherId;

    /**
     * 班级ID
     */
    @Schema(description = "班级ID")
    @NotNull(groups = {TimetableValidGroup.ExistScheduleCourseByClassIdGroup.class}, message = "班级ID不能为空")
    private Long classId;

    /**
     * 教室ID
     */
    @Schema(description = "教室ID")
    private Long classroomId;


    /**
     * 门店已约直播课Id
     */
    @Schema(description = "门店已约直播课Id")
    private Long coursePlanId;

    /**
     * 上课时段ID
     */
    @Schema(description = "上课时段ID")
    private Long timeSlotId;

    /**
     * 时段类型: 1-上午; 2-下午; 3-晚上;
     */
    @Schema(description = "时段类型: 1-上午; 2-下午; 3-晚上;")
    private Integer timeSlotType;

    /**
     * 上课日期
     */
    @Schema(description = "上课日期")
    @NotNull(groups = {
        TimetableValidGroup.GetTimeTableByDateGroup.class}, message = "上课日期不能为空")
    private LocalDate classDate;
}
