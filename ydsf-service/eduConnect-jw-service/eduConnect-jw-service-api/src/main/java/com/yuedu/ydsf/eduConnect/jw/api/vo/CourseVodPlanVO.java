package com.yuedu.ydsf.eduConnect.jw.api.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import lombok.Data;

/**
 * 门店已约点播课排期表 视图类
 *
 * <AUTHOR>
 * @date 2024-12-09 10:25:38
 */
@Data
@Schema(description = "门店已约点播课排期表视图类")
public class CourseVodPlanVO {


	/**
	* 主键ID
	*/
    @Schema(description="主键ID")
    private Long id;

	/**
	* 门店ID
	*/
    @Schema(description="门店ID")
    private Long storeId;

	/**
	* 门店已约点播课ID
	*/
    @Schema(description="门店已约点播课ID")
    private Long vodCourseId;

	/**
	* 课程ID
	*/
    @Schema(description="课程ID")
    private Long courseId;

	/**
	* 上课时段ID
	*/
    @Schema(description="上课时段ID")
    private Long timeSlotId;

	/**
	* 第几节课
	*/
    @Schema(description="第几节课")
    private Integer lessonOrder;

	/**
	* 上课日期
	*/
    @Schema(description="上课日期")
    private LocalDate classDate;

	/**
	* 上课开始时间
	*/
    @Schema(description="上课开始时间")
    private LocalTime classStartTime;

	/**
	* 上课结束时间
	*/
    @Schema(description="上课结束时间")
    private LocalTime classEndTime;

    /**
     * 课程版本
     */
    @Schema(description="课程版本")
    private Integer courseVersion;

    /**
     * 课件版本
     */
    @Schema(description="课件版本")
    private Integer coursewareVersion;

	/**
	* 创建人
	*/
    @Schema(description="创建人")
    private String createBy;

	/**
	* 创建时间
	*/
    @Schema(description="创建时间")
    private LocalDateTime createTime;

	/**
	* 修改人
	*/
    @Schema(description="修改人")
    private String updateBy;

	/**
	* 修改时间
	*/
    @Schema(description="修改时间")
    private LocalDateTime updateTime;

	/**
	* 是否删除: 0-否; 1-是;
	*/
    @Schema(description="是否删除: 0-否; 1-是;")
    private Integer delFlag;

    /**
     * 封面(全路径)
     */
    @Schema(description = "封面")
    private String imgUrl;

    /**
     * 课节名字
     */
    @Schema(description="课节名字")
    private String lessonName;


    /**
     * 主讲老师, 课程对应最早教学计划明细上课开始时间
     */
    @Schema(description="主讲老师, 课程对应最早教学计划明细上课开始时间")
    private LocalDateTime teachingPlanDetailClassStartDateTime;

    /**
     * 主讲老师, 课程对应最早教学计划明细上课结束时间
     */
    @Schema(description="主讲老师, 课程对应最早教学计划明细上课结束时间")
    private LocalDateTime teachingPlanDetailClassEndDateTime;

    /**
     * 书籍名称
     */
    private String bookName;

    /**
     * 上课时段名称
     */
    @Schema(description="上课时段名称")
    private String timeSlotName;

    /**
     * 上课状态: 0-未结束; 1-已结束;
     */
    @Schema(description="上课状态: 0-未结束; 1-已结束;")
    private Integer courseVodPlanStatus;

    /**
     * 主讲老师ID
     */
    @Schema(description="主讲老师ID")
    private Long lectureId;

    /**
     * 班级ID
     */
    @Schema(description="班级ID")
    private Long classId;

    /**
     * 教室ID
     */
    @Schema(description="教室ID")
    private Long classroomId;

    /**
     * 指导老师ID
     */
    @Schema(description="指导老师ID")
    private Long teacherId;

    /**
     * 主讲老师名称
     */
    @Schema(description="主讲老师名称")
    private String lectureName;

    /**
     * 主讲老师昵称
     */
    @Schema(description="主讲老师昵称")
    private String lectureNickName;

    /**
     * 班级名称
     */
    @Schema(description="班级名称")
    private String className;

    /**
     * 教室名称
     */
    @Schema(description="教室名称")
    private String classroomName;

    /**
     * 指导老师名称
     */
    @Schema(description="指导老师名称")
    private String teacherName;

}

