package com.yuedu.ydsf.eduConnect.jw.api.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 默认/门店红包规则设置 视图类
 *
 * <AUTHOR>
 * @date 2024/11/4 9:40
 */
@Data
@Schema(description = "默认/门店红包规则设置 视图类")
public class InteractionRedPacketSettingInfoVO {


    /**
     * 门店默认红包规则
     */
    @Schema(description = "门店默认红包规则")
    private InteractionRedPacketSettingVO defaultInteractionRedPacketSettingVO = new InteractionRedPacketSettingVO();

    /**
     * 门店自定义红包规则
     */
    @Schema(description = "门店自定义红包规则")
    private InteractionRedPacketSettingVO customInteractionRedPacketSettingVO = new InteractionRedPacketSettingVO();

    /**
     * 规则设置: 0-默认; 1-自定义;
     */
    @Schema(description = "规则设置: 0-默认; 1-自定义;")
    private Integer rulesSetting = 0;


}

