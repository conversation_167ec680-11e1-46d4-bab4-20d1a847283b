package com.yuedu.ydsf.eduConnect.jw.api.constant.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * @author: 张浩宇
 * @date: 2024/12/11
 **/
@Getter
@AllArgsConstructor
public enum CouseTypeEnum {

    /**
     * 直播课
     */
    LIVE(1, "直播课"),

    /**
     * 点播
     */
    VOD(2, "点播课"),

    /**
     * 补课
     */
    MAKE_UP(3, "补课");

    /**
     * 类型
     */
    private final Integer code;

    /**
     * 描述
     */
    private final String desc;
}
