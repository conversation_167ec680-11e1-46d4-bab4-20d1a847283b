package com.yuedu.ydsf.eduConnect.jw.api.query;
import com.yuedu.ydsf.eduConnect.jw.api.valid.CourseMakeUpValidGroup;
import com.yuedu.ydsf.eduConnect.jw.api.valid.TimetableValidGroup;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 门店课表 查询类
 *
 * <AUTHOR>
 * @date 2024-12-09 10:27:50
 */
@Data
@Schema(description = "门店课表查询类")
public class TimetableQuery {


	/**
	* 主键ID
	*/
    @Schema(description="主键ID")
    private Long id;

	/**
	* 门店ID
	*/
    @Schema(description="门店ID")
    private Long storeId;

	/**
	* 这节课的编号，课程类型（1位）+ 第几节课（3位）+ 主表的ID
	*/
    @Schema(description="这节课的编号，课程类型（1位）+ 第几节课（3位）+ 主表的ID")
    private Long lessonNo;

	/**
	* 门店已约直播课ID、门店已约点播课排期表ID、门店补课表ID
	*/
    @Schema(description="门店已约直播课ID、门店已约点播课排期表ID、门店补课表ID")
    private Long coursePlanId;

	/**
	* 课程类型: 1-直播课; 2-点播课; 3-补课;
	*/
    @Schema(description="课程类型: 1-直播课; 2-点播课; 3-补课;")
    private Integer courseType;

	/**
	* 教室ID
	*/
    @Schema(description="教室ID")
    private Long classroomId;

	/**
	* 指导老师ID
	*/
    @Schema(description="指导老师ID")
    private Long teacherId;

	/**
	* 课程ID
	*/
    @Schema(description="课程ID")
    private Long courseId;

	/**
	* 课节ID
	*/
    @Schema(description="课节ID")
    private Long lessonId;

	/**
	* 第几节课
	*/
    @Schema(description="第几节课")
    private Integer lessonOrder;

	/**
	* 上课时段ID
	*/
    @Schema(description="上课时段ID")
    private Long timeSlotId;

	/**
	* 时段类型: 1-上午; 2-下午; 3-晚上;
	*/
    @Schema(description="时段类型: 1-上午; 2-下午; 3-晚上;")
    private Integer timeSlotType;

	/**
	* 上课日期
	*/
    @Schema(description="上课日期")
    private LocalDate classDate;

	/**
	* 上课开始时间
	*/
    @Schema(description="上课开始时间")
    private Object classStartTime;

	/**
	* 上课结束时间
	*/
    @Schema(description="上课结束时间")
    private Object classEndTime;

	/**
	* 上课开始日期时间
	*/
    @Schema(description="上课开始日期时间")
    private LocalDateTime classStartDateTime;

	/**
	* 上课结束日期时间
	*/
    @Schema(description="上课结束日期时间")
    private LocalDateTime classEndDateTime;

	/**
	* 创建人
	*/
    @Schema(description="创建人")
    private String createBy;

	/**
	* 创建时间
	*/
    @Schema(description="创建时间")
    private LocalDateTime createTime;

	/**
	* 修改人
	*/
    @Schema(description="修改人")
    private String updateBy;

	/**
	* 修改时间
	*/
    @Schema(description="修改时间")
    private LocalDateTime updateTime;

	/**
	* 是否删除: 0-否; 1-是;
	*/
    @Schema(description="是否删除: 0-否; 1-是;")
    private Integer delFlag;

    /**
     * 上课开始日期时间
     */
    @Schema(description="上课开始日期时间")
    private LocalDate selectStartClassDate;

    /**
     * 上课结束日期时间
     */
    @Schema(description="上课结束日期时间")
    private LocalDate selectEndClassDate;

    /**
     * 课节名称
     */
    @Schema(description="课节名称")
    private String lessonName;

}
