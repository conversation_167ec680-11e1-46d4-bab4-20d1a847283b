package com.yuedu.ydsf.eduConnect.jw.api.query;

import com.yuedu.ydsf.eduConnect.jw.api.valid.CourseVodValidGroup;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import java.time.LocalDateTime;
import lombok.Data;

/**
 * 门店已约点播课 查询类
 *
 * <AUTHOR>
 * @date 2024-12-09 10:24:34
 */
@Data
@Schema(description = "门店已约点播课查询类")
public class CourseVodQuery {


	/**
	* 主键ID
	*/
    @Schema(description="主键ID")
    @NotNull(groups = {CourseVodValidGroup.CourseVodDetailGroup.class}, message = "主键ID不能为空")
    private Long id;

	/**
	* 门店ID
	*/
    @Schema(description="门店ID")
    private Long storeId;

	/**
	* 排课名称
	*/
    @Schema(description="排课名称")
    private String name;

	/**
	* 课程ID
	*/
    @Schema(description="课程ID")
    private Long courseId;

	/**
	* 阶段
	*/
    @Schema(description="阶段")
    private Integer stage;

	/**
	* 主讲老师ID
	*/
    @Schema(description="主讲老师ID")
    private Long lectureId;

	/**
	* 班级ID
	*/
    @Schema(description="班级ID")
    private Long classId;

	/**
	* 教室ID
	*/
    @Schema(description="教室ID")
    private Long classroomId;

	/**
	* 指导老师ID
	*/
    @Schema(description="指导老师ID")
    private Long teacherId;

	/**
	* 是否已排课: 0-未排课; 1-已排课;
	*/
    @Schema(description="是否已排课: 0-未排课; 1-已排课;")
    private Integer scheduled;

	/**
	* 创建人
	*/
    @Schema(description="创建人")
    private String createBy;

	/**
	* 创建时间
	*/
    @Schema(description="创建时间")
    private LocalDateTime createTime;

	/**
	* 修改人
	*/
    @Schema(description="修改人")
    private String updateBy;

	/**
	* 修改时间
	*/
    @Schema(description="修改时间")
    private LocalDateTime updateTime;

	/**
	* 是否删除: 0-否; 1-是;
	*/
    @Schema(description="是否删除: 0-否; 1-是;")
    private Integer delFlag;

    /**
     * 上课开始时间
     */
    @Schema(description="上课开始时间")
    private LocalDateTime classStartDateTime;

    /**
     * 上课结束时间
     */
    @Schema(description="上课结束时间")
    private LocalDateTime classEndDateTime;

    /**
     * 教学计划ID
     */
    @Schema(description="教学计划ID")
    private Long teachingPlanId;

    /**
     * 课程类型
     */
    @Schema(description = "课程类型")
    private Integer courseType;

}
