package com.yuedu.ydsf.eduConnect.jw.api.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2025/02/13
 */
@Data
@Schema(description = "学生调课记录返回对象")
public class StudentTimetableChangeVO {

  @Schema(description = "调课记录ID")
  private Long id;

  @Schema(description = "原课程名称")
  private String sourceLessonName;

  @Schema(description = "原老师名称")
  private String sourceTeacherName;

  @Schema(description = "原教室名")
  private String sourceClassroomName;

  @Schema(description = "原上课时间")
  private LocalDateTime sourceClassTime;

  @Schema(description = "目标课程名称")
  private String targetLessonName;

  @Schema(description = "目标老师名称")
  private String targetTeacherName;

  @Schema(description = "目标教室名")
  private String targetClassroomName;

  @Schema(description = "目标上课时间")
  private LocalDateTime targetClassTime;

  @Schema(description = "调课操作时间")
  private LocalDateTime createTime;

  @Schema(description = "调课操作人")
  private String createBy;

  @Schema(description = "目标主讲老师ID")
  private Long targetLecturerId;

  @Schema(description = "目标主讲老师姓名")
  private String targetLecturerName;

  @Schema(description = "原主讲老师ID")
  private Long sourceLecturerId;

  @Schema(description = "原主讲老师姓名")
  private String sourceLecturerName;
}
