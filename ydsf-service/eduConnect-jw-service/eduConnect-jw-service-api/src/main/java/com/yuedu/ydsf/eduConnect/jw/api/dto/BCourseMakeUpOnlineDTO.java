package com.yuedu.ydsf.eduConnect.jw.api.dto;

import com.yuedu.ydsf.common.core.util.V_A_E;
import com.yuedu.ydsf.common.core.util.V_E;
import com.yuedu.ydsf.eduConnect.jw.api.valid.BMakeUpOnlineValidGroup;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import org.hibernate.validator.constraints.Length;
import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;

/**
 * 门店线上补课表
 *
 * <AUTHOR>
 * @date 2025/04/23
 */
@Data
@Schema(description = "门店线上补课表传输对象")
public class BCourseMakeUpOnlineDTO implements Serializable {

  /** 主键ID(雪花id生成) */
  @NotNull(
      groups = {
        V_E.class,
        BMakeUpOnlineValidGroup.EditMakeUpOnline.class,
        BMakeUpOnlineValidGroup.DeleteMakeUpOnline.class
      },
      message = "主键ID(雪花id生成)不能为空")
  private Long id;

  /** 门店ID */
  @Schema(description = "门店ID")
  @NotNull(
      groups = {V_A_E.class, BMakeUpOnlineValidGroup.AddMakeUpOnline.class},
      message = "门店ID不能为空")
  private Long storeId;

  /** 课表号 */
  @Schema(description = "课表号")
  @NotNull(
      groups = {BMakeUpOnlineValidGroup.AddMakeUpOnline.class},
      message = "课表号不能为空")
  private Long lessonNo;

  /** 新增补课时勾选的出勤的学生 */
  @Schema(description = "出勤学生")
  private List<Integer> checkStudents;

  /** 教学计划ID */
  @Schema(description = "教学计划ID")
  private Long teachingPlanId;

  /** 课程ID */
  @Schema(description = "课程ID")
  private Long courseId;

  /** 第几节课 */
  @Schema(description = "第几节课")
  private Integer lessonOrder;

  /** 上课时段ID */
  @Schema(description = "上课时段ID")
  private Long timeSlotId;

  /** 主讲老师ID */
  @Schema(description = "主讲老师ID")
  @NotNull(
      groups = {V_A_E.class},
      message = "主讲老师ID不能为空")
  private Long lectureId;

  /** 上课教室ID */
  @Schema(description = "上课教室ID")
  private Long classRoomId;

  /** 班级ID */
  @Schema(description = "班级ID")
  private Long classId;

  /** 上课日期 */
  @Schema(description = "上课日期")
  private LocalDateTime classDate;

  /** 上课开始时间 */
  @Schema(description = "上课开始时间")
  private Date classStartTime;

  /** 上课结束时间 */
  @Schema(description = "上课结束时间")
  private Date classEndTime;

  /** 补课有效期开始时间 */
  @Schema(description = "补课有效期开始时间")
  @NotNull(
      groups = {
        V_A_E.class,
        BMakeUpOnlineValidGroup.AddMakeUpOnline.class,
        BMakeUpOnlineValidGroup.EditMakeUpOnline.class
      },
      message = "补课有效期开始时间不能为空")
  private LocalDateTime validityStartTime;

  /** 补课有效期结束时间 */
  @Schema(description = "补课有效期结束时间")
  @NotNull(
      groups = {
        V_A_E.class,
        BMakeUpOnlineValidGroup.AddMakeUpOnline.class,
        BMakeUpOnlineValidGroup.EditMakeUpOnline.class
      },
      message = "补课有效期结束时间不能为空")
  private LocalDateTime validityEndTime;

  /** 创建人 */
  @Schema(description = "创建人")
  @Length(
      groups = {V_A_E.class},
      max = 255,
      message = "创建人长度不能大于255")
  private String createBy;

  /** 创建时间 */
  @Schema(description = "创建时间")
  @NotNull(
      groups = {V_A_E.class},
      message = "创建时间不能为空")
  private LocalDateTime createTime;

  /** 修改人 */
  @Schema(description = "修改人")
  @Length(
      groups = {V_A_E.class},
      max = 255,
      message = "修改人长度不能大于255")
  private String updateBy;

  /** 修改时间 */
  @Schema(description = "修改时间")
  @NotNull(
      groups = {V_A_E.class},
      message = "修改时间不能为空")
  private LocalDateTime updateTime;

  /** 是否删除: 0-否; 1-是; */
  @Schema(description = "是否删除: 0-否; 1-是;")
  @NotNull(
      groups = {V_A_E.class},
      message = "是否删除不能为空")
  private Byte delFlag;

  /** 课程类型ID（用于指定扣减的课程类型） */
  @Schema(description = "课程类型ID")
  private Integer courseTypeId;
}
