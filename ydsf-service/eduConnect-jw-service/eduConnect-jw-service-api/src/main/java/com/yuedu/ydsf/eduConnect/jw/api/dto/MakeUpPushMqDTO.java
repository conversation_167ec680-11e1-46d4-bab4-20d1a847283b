package com.yuedu.ydsf.eduConnect.jw.api.dto;

import lombok.Data;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 补课推送消息队列DTO
 *
 * <AUTHOR>
 * @date 2025/4/25 11:05
 */
@Data
public class MakeUpPushMqDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 学生ID
     */
    private Long studentId;

    /**
     * 补课在线ID
     */
    private Long makeUpOnlineId;

    /**
     * 课表课次号
     */
    private Long lessonNo;

    /**
     * 是否取消考勤
     */
    private Boolean isCancelAttendance = false;

    /**
     * 重试次数
     */
    private Integer retryCount = 0;

    /**
     * 最大重试次数
     */
    private Integer maxRetryCount = 3;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 门店ID
     */
    private Long storeId;

    /**
     * 租户ID
     */
    private Long tenantId;

    /**
     * 创建人
     */
    private String createBy;
}
