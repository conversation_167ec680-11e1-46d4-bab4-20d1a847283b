package com.yuedu.teaching.controller.web;

import com.yuedu.teaching.dto.CoursewareCopyDTO;
import com.yuedu.teaching.query.StoreCoursewareDataQuery;
import com.yuedu.teaching.service.StoreCoursewareDataService;
import com.yuedu.teaching.vo.StepVO;
import com.yuedu.ydsf.common.core.util.R;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.ArrayList;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

/**
 * WebStoreCoursewareDataController 测试类
 *
 * <AUTHOR>
 * @date 2025/08/05
 */
@ExtendWith(MockitoExtension.class)
class WebStoreCoursewareDataControllerTest {

    @Mock
    private StoreCoursewareDataService storeCoursewareDataService;

    @InjectMocks
    private WebStoreCoursewareDataController webStoreCoursewareDataController;

    /**
     * 测试创建课件副本
     */
    @Test
    void testCreateCoursewareCopy() {
        // 准备测试数据
        CoursewareCopyDTO coursewareCopyDTO = new CoursewareCopyDTO();
        coursewareCopyDTO.setCoursewareId(1);
        coursewareCopyDTO.setCoursewareDataId(1);

        // Mock service 返回值
        when(storeCoursewareDataService.createCoursewareCopy(any(CoursewareCopyDTO.class)))
                .thenReturn(true);

        // 执行测试
        R<Boolean> result = webStoreCoursewareDataController.createCoursewareCopy(coursewareCopyDTO);

        // 验证结果
        assertNotNull(result);
        assertTrue(result.isOk());
        assertTrue(result.getData());
    }

    /**
     * 测试查询所有教学环节
     */
    @Test
    void testListStoreCoursewareSteps() {
        // 准备测试数据
        StoreCoursewareDataQuery query = new StoreCoursewareDataQuery();
        query.setCoursewareId(1);
        query.setCoursewareDataId(1);

        List<StepVO> mockSteps = new ArrayList<>();
        StepVO stepVO = new StepVO();
        stepVO.setId(1);
        stepVO.setStepName("测试环节");
        stepVO.setStepParent(0);
        stepVO.setStepOrder(1);
        stepVO.setType(1);
        mockSteps.add(stepVO);

        // Mock service 返回值
        when(storeCoursewareDataService.listStoreCoursewareSteps(any(StoreCoursewareDataQuery.class)))
                .thenReturn(mockSteps);

        // 执行测试
        R<List<StepVO>> result = webStoreCoursewareDataController.listStoreCoursewareSteps(query);

        // 验证结果
        assertNotNull(result);
        assertTrue(result.isOk());
        assertNotNull(result.getData());
        assertEquals(1, result.getData().size());
        assertEquals("测试环节", result.getData().get(0).getStepName());
    }
}
