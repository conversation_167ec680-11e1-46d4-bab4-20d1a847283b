package com.yuedu.teaching.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.yuedu.teaching.query.StoreCoursewareDataStepDetailsQuery;
import com.yuedu.teaching.dto.StoreCoursewareDataStepDetailsDTO;
import com.yuedu.teaching.vo.StoreCoursewareDataStepDetailsVO;
import com.yuedu.teaching.entity.StoreCoursewareDataStepDetails;

import java.util.List;

/**
* 门店教学环节详情表服务接口
*
* <AUTHOR>
* @date  2025/08/05
*/
public interface StoreCoursewareDataStepDetailsService extends IService<StoreCoursewareDataStepDetails> {

}
