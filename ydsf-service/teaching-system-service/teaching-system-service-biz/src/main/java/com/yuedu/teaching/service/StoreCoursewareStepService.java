package com.yuedu.teaching.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.yuedu.teaching.query.StoreCoursewareStepQuery;
import com.yuedu.teaching.dto.StoreCoursewareStepDTO;
import com.yuedu.teaching.dto.StoreCoursewareStepOrderDTO;
import com.yuedu.teaching.vo.StoreCoursewareStepVO;
import com.yuedu.teaching.entity.StoreCoursewareStep;

import java.util.List;

/**
* 门店教学环节表服务接口
*
* <AUTHOR>
* @date  2025/08/05
*/
public interface StoreCoursewareStepService extends IService<StoreCoursewareStep> {

    /**
     * 新增门店教学环节
     *
     * @param storeCoursewareStepDTO 新增参数
     * @return StoreCoursewareStepVO 新增结果
     */
    StoreCoursewareStepVO addStoreCoursewareStep(StoreCoursewareStepDTO storeCoursewareStepDTO);

    /**
     * 修改门店教学环节
     *
     * @param storeCoursewareStepDTO 修改参数
     * @return StoreCoursewareStepVO 修改结果
     */
    StoreCoursewareStepVO updateStoreCoursewareStep(StoreCoursewareStepDTO storeCoursewareStepDTO);

    /**
     * 调整门店教学环节顺序
     *
     * @param storeCoursewareStepOrderDTO 环节顺序调整参数
     */
    void updateStoreCoursewareStepOrder(StoreCoursewareStepOrderDTO storeCoursewareStepOrderDTO);

    /**
     * 删除门店教学环节
     *
     * @param storeCoursewareStepDTO 删除参数
     * @return Boolean 删除结果
     */
    Boolean deleteStoreCoursewareStep(StoreCoursewareStepDTO storeCoursewareStepDTO);
}
