package com.yuedu.teaching.service.impl;


import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;
import org.springframework.beans.BeanUtils;
import cn.hutool.core.util.ArrayUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yuedu.teaching.mapper.StoreCoursewareDataStepDetailsMapper;
import com.yuedu.teaching.service.StoreCoursewareDataStepDetailsService;
import com.yuedu.teaching.query.StoreCoursewareDataStepDetailsQuery;
import com.yuedu.teaching.dto.StoreCoursewareDataStepDetailsDTO;
import com.yuedu.teaching.vo.StoreCoursewareDataStepDetailsVO;
import com.yuedu.teaching.entity.StoreCoursewareDataStepDetails;

import java.util.List;


/**
* 门店教学环节详情表服务层
*
* <AUTHOR>
* @date  2025/08/05
*/
@Service
public class StoreCoursewareDataStepDetailsServiceImpl extends ServiceImpl<StoreCoursewareDataStepDetailsMapper,StoreCoursewareDataStepDetails>
    implements StoreCoursewareDataStepDetailsService{



}
