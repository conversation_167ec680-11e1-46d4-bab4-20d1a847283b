package com.yuedu.teaching.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.lang.Opt;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yuedu.teaching.constant.TeachingConstant;
import com.yuedu.teaching.dto.CoursewareStepByTemplateDTO;
import com.yuedu.teaching.dto.CoursewareStepDTO;
import com.yuedu.teaching.dto.CoursewareStepOrderDTO;
import com.yuedu.teaching.entity.*;
import com.yuedu.teaching.mapper.*;
import com.yuedu.teaching.query.CoursewareStepQuery;
import com.yuedu.teaching.service.*;
import com.yuedu.teaching.vo.*;
import com.yuedu.ydsf.common.core.exception.BizException;
import com.yuedu.ydsf.common.data.resolver.ParamResolver;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Service;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.transaction.TransactionDefinition;
import org.springframework.transaction.TransactionStatus;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.DefaultTransactionDefinition;

import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

import static com.yuedu.teaching.service.impl.CoursewarePubServiceImpl.addPathField;

/**
 * 教学环节表 服务实现类
 *
 * <AUTHOR>
 * @date 2024-11-05 17:30:36
 */
@Slf4j
@Service
@AllArgsConstructor
public class CoursewareStepServiceImpl extends ServiceImpl<CoursewareStepMapper, CoursewareStep> implements CoursewareStepService {

    //默认值
    private static final Integer STEP_ORDER = 1;
    private static final Integer COUNT_UPDATE_NUM = 1;

    private final CoursewareDataStepDetailsService coursewareDataStepDetailsService;
    private final TeachingPageTemplateService teachingPageTemplateService;
    private final CoursewareStepTemplateMapper coursewareStepTemplateMapper;
    private PlatformTransactionManager transactionManager;
    private CoursewareStepMapper coursewareStepMapper;
    private CoursewareDataService coursewareDataService;
    private CoursewareDataStepDetailsMapper coursewareDataStepDetailsMapper;
    private TeachingPageTemplateMapper teachingPageTemplateMapper;
    private final CoursewarePubService coursewarePubService;

    /**
     * 新增教学环节
     *
     * @param coursewareStepAddDTO 新增参数
     * @return 新增结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public CoursewareStepVO addCoursewareStep(CoursewareStepDTO coursewareStepAddDTO) {

        if (Objects.nonNull(coursewareStepAddDTO.getPageTemplateId()) && coursewareStepAddDTO.getPageTemplateId() != 0) {
            //判断教学页是否存在
            boolean exists = teachingPageTemplateService.exists(Wrappers.<TeachingPageTemplateEntity>lambdaQuery()
                    .eq(TeachingPageTemplateEntity::getId, coursewareStepAddDTO.getPageTemplateId()));
            if (!exists) {
                throw new BizException("页面模板不存在");
            }
        }
        CoursewareStep coursewareStep = BeanUtil.copyProperties(coursewareStepAddDTO, CoursewareStep.class, "id");
        // 校验环节类型是否为有效值(1或2)
        if (inValidStepType(coursewareStep.getType())) {
            throw new BizException("环节类型错误：" + coursewareStep.getType());
        }

        // 更新环节顺序并插入新环节
        this.update(Wrappers.lambdaUpdate(CoursewareStep.class)
                .eq(CoursewareStep::getCoursewareId, coursewareStep.getCoursewareId())
                .eq(CoursewareStep::getCoursewareDataId, coursewareStep.getCoursewareDataId())
                .eq(CoursewareStep::getStepParent, coursewareStep.getStepParent())
                .ge(CoursewareStep::getStepOrder, coursewareStep.getStepOrder())
                .setIncrBy(CoursewareStep::getStepOrder, COUNT_UPDATE_NUM)
        );
        this.save(coursewareStep);

        // 设置发布状态为不可发布
        coursewareDataService.setPubStatusById(coursewareStep.getCoursewareDataId(),
                TeachingConstant.COURSEWARE_DATA_CANNOT_PUBLISH, coursewareStep.getCoursewareId());

        return BeanUtil.copyProperties(coursewareStep, CoursewareStepVO.class);
    }

    /**
     * 根据模板新增
     *
     * @param coursewareStepByTemplate 新增参数
     * @return 新增结果
     */
    @Override
    public Boolean copyCoursewareStep(CoursewareStepByTemplateDTO coursewareStepByTemplate) {

        CoursewareData coursewareData = coursewareDataService.getById(coursewareStepByTemplate.getCoursewareDataId());

        if (Objects.isNull(coursewareData)) {
            throw new BizException("课件不存在");
        }

        if (!Objects.equals(coursewareData.getCoursewareId(), coursewareStepByTemplate.getCoursewareId()) || !Objects.equals(coursewareData.getDataTemplateId(), coursewareStepByTemplate.getDataTemplateId())) {
            throw new BizException("课件信息不存在，请检查！");
        }

        //获取教学环节模版数据
        CoursewareStepTemplate stepTemplate = coursewareStepTemplateMapper.selectById(coursewareStepByTemplate.getStepTemplateId());
        if (Objects.isNull(stepTemplate)) {
            throw new BizException("教学环节模版不存在");
        }

        // 将 stepTemplate.getDetails() 转换为 List<StepVO>
        List<StepVO> steps = JSONUtil.toList(stepTemplate.getDetails(), StepVO.class);

        // 将 stepIds 转换为 Set 以便于快速查找
        Set<Integer> stepIdSet = new HashSet<>(coursewareStepByTemplate.getStepIds());

        saveCoursewareSteps(steps, stepIdSet, coursewareStepByTemplate);

        // 设置发布状态为不可发布
        coursewareDataService.setPubStatusById(coursewareStepByTemplate.getCoursewareDataId(),
                TeachingConstant.COURSEWARE_DATA_CANNOT_PUBLISH, coursewareStepByTemplate.getCoursewareId());

        return true;
    }

    /**
     * 保存教学环节及其子环节
     *
     * @param steps                    步骤列表
     * @param stepIdSet                步骤ID集合
     * @param coursewareStepByTemplate 课程步骤模板
     */
    public void saveCoursewareSteps(List<StepVO> steps, Set<Integer> stepIdSet, CoursewareStepByTemplateDTO coursewareStepByTemplate) {
        // 线程安全 初始化步序
        AtomicInteger stepOrder = new AtomicInteger(getStepOrder(coursewareStepByTemplate.getCoursewareId(), coursewareStepByTemplate.getCoursewareDataId()));

        // 手动开启事务
        TransactionDefinition def = new DefaultTransactionDefinition();
        TransactionStatus status = transactionManager.getTransaction(def);

        try {
            // 使用 Stream API 过滤 steps，保留那些 id 存在于 stepIdSet 中的 StepVO
            steps.stream()
                    .filter(step -> stepIdSet.contains(step.getId()))
                    .forEach(step -> saveCoursewareStepAndChildren(step, coursewareStepByTemplate.getCoursewareId(), coursewareStepByTemplate.getCoursewareDataId(), stepOrder.getAndIncrement()));
            transactionManager.commit(status);
        } catch (Exception e) {
            transactionManager.rollback(status);
            log.error("模版保存教学环节失败: {}", e.getMessage(), e);
            throw new BizException("保存教学环节失败");
        }

    }

    /**
     * 获取现在最大排序值
     *
     * @param coursewareId     课件ID
     * @param coursewareDataId 课件内容ID
     * @return getStepOrder 最大排序值
     */
    private Integer getStepOrder(Integer coursewareId, Integer coursewareDataId) {
        //排序规则
        CoursewareStep coursewareStepOrder = this.getOne(Wrappers.<CoursewareStep>lambdaQuery()
                .eq(CoursewareStep::getCoursewareId, coursewareId)
                .eq(CoursewareStep::getCoursewareDataId, coursewareDataId)
                .eq(CoursewareStep::getType, TeachingConstant.COURSEWARE_STEP_TYPE_LINK)
                .orderByDesc(CoursewareStep::getStepOrder)
                .last("limit 1"));

        if (Objects.isNull(coursewareStepOrder)) {
            return STEP_ORDER;
        }

        Integer count = this.lambdaQuery()
                .eq(CoursewareStep::getCoursewareId, coursewareId)
                .eq(CoursewareStep::getCoursewareDataId, coursewareDataId)
                .eq(CoursewareStep::getType, TeachingConstant.COURSEWARE_STEP_TYPE_LINK)
                .count().intValue();

        return coursewareStepOrder.getStepOrder() > count ? coursewareStepOrder.getStepOrder() + 1 : count + 1;
    }


    /**
     * 保存教学环节及其子环节
     *
     * @param step                当前环节
     * @param coursewareDataId    数据ID
     * @param coursewareId        课程ID
     * @param coursewareStepOrder 环节顺序
     */
    private void saveCoursewareStepAndChildren(StepVO step, Integer coursewareId, Integer coursewareDataId, Integer coursewareStepOrder) {
        // 创建并保存父节点
        CoursewareStep coursewareStep = CoursewareStep.builder()
                .coursewareDataId(coursewareDataId)
                .coursewareId(coursewareId)
                .stepName(step.getStepName())
                .stepOrder(coursewareStepOrder)
                .type(step.getType())
                .pageTemplateId(step.getPageTemplateId())
                .stepParent(0)
                .build();
        this.save(coursewareStep);

        // 获取父节点的ID
        Integer parentId = coursewareStep.getId();

        // 获取子节点列表
        List<StepVO> children = step.getChildren();

        if (CollUtil.isNotEmpty(children)) {
            // 使用流处理子节点
            List<CoursewareStep> childSteps = children.stream()
                    .map(child -> CoursewareStep.builder()
                            .coursewareDataId(coursewareDataId)
                            .coursewareId(coursewareId)
                            .stepName(child.getStepName())
                            .stepOrder(child.getStepOrder())
                            .type(child.getType())
                            .pageTemplateId(child.getPageTemplateId())
                            .stepParent(parentId)
                            .build())
                    .toList();

            // 保存所有子节点
            coursewareStepMapper.insert(childSteps);
        }
    }


    /**
     * 查询所有教学环节
     *
     * @param coursewareStepQuery 查询参数
     * @return 查询结果
     */
    @Override
    public List<StepVO> listCoursewareStep(CoursewareStepQuery coursewareStepQuery) {
        List<CoursewareStep> coursewareStepList = getCoursewareSteps(coursewareStepQuery);
        return findTree(coursewareStepList);
    }


    /**
     * 构建树形环节
     *
     * @param steps 所有数据
     * @return List<StepVO>
     */
    public List<StepVO> findTree(List<CoursewareStep> steps) {

        List<StepVO> menus = steps.stream()
                .map(step -> BeanUtil.copyProperties(step, StepVO.class))
                .toList();

        List<StepVO> rootMenus = new ArrayList<>();
        for (StepVO menu : menus) {
            //从最上级菜单开始展示
            if (menu.getStepParent().equals(0)) {
                rootMenus.add(menu);
            }
        }

        for (StepVO rootMenu : rootMenus) {
            List<StepVO> child = getChild(rootMenu.getId(), menus);
            rootMenu.setChildren(child);
        }
        return rootMenus;

    }


    /**
     * 通过传入当前菜单id，获取当前id的子菜单 且子菜单里也有子菜单及其信息，直到子菜单没有子菜单了，递归结束
     *
     * @param id    当前菜单的id
     * @param menus 要查询的菜单范围
     * @return 该id的子菜单
     */
    private List<StepVO> getChild(Integer id, List<StepVO> menus) {
        List<StepVO> childList = new ArrayList<>();
        for (StepVO menu : menus) {
            if (menu.getStepParent().equals(id)) {
                //当菜单范围内的父id等于传入的id,获取该id的所有子列表
                childList.add(menu);
            }
        }
        for (StepVO menu : childList) {
            //当上面循环语句查询不到子菜单，长度为0，自然也就不会递归调用了
            //将该id的子菜单便利，通过递归调用，给每一个子菜单赋值子菜单
            List<StepVO> child = getChild(menu.getId(), menus);
            menu.setChildren(child);
        }
        if (childList.isEmpty()) {
            //子菜单的长度为0,返回null,null不会被jackson解析
            return Collections.emptyList();
        }
        return childList;
    }

    /**
     * 获取所有教学环节
     *
     * @param coursewareId     课件ID
     * @param coursewareDataId 课件内容ID
     * @return 教学环节列表
     */
    public List<CoursewareStep> getCoursewareStepList(Integer coursewareId, Integer coursewareDataId) {
        // 获取教学环节数据
        return this.list(Wrappers.<CoursewareStep>lambdaQuery()
                .eq(CoursewareStep::getCoursewareId, coursewareId)
                .eq(CoursewareStep::getCoursewareDataId, coursewareDataId)
                .orderByAsc(CoursewareStep::getStepOrder)
                .orderByDesc(CoursewareStep::getCreateTime)
        );
    }


    /**
     * 根据id查询教学环节
     *
     * @param coursewareStepQuery 查询参数
     * @return 查询结果
     */
    @Override
    public CoursewareStepVO getCoursewareStepById(CoursewareStepQuery coursewareStepQuery) {
        CoursewareStep coursewareStep = this.getOne(Wrappers.<CoursewareStep>lambdaQuery()
                .eq(CoursewareStep::getId, coursewareStepQuery.getId()));
        CoursewareDataStepDetails dataStepDetails = coursewareDataStepDetailsService.getById(coursewareStep.getId());
        CoursewareStepVO coursewareStepVO = BeanUtil.copyProperties(coursewareStep, CoursewareStepVO.class);
        coursewareStepVO.setDataStepDetailsId(dataStepDetails.getId());
        return coursewareStepVO;
    }

    /**
     * 修改教学环节
     *
     * @param coursewareStepUpdateDTO 修改参数
     * @return 修改结果
     */
    @Override
    public CoursewareStepVO updateCoursewareStep(CoursewareStepDTO coursewareStepUpdateDTO) {
        CoursewareStep coursewareStep = this.getOne(Wrappers.<CoursewareStep>lambdaQuery()
                .eq(CoursewareStep::getId, coursewareStepUpdateDTO.getId())
                .eq(CoursewareStep::getCoursewareDataId, coursewareStepUpdateDTO.getCoursewareDataId()));

        if (Opt.ofNullable(coursewareStep).isEmpty()) {
            throw new BizException("id不存在：" + coursewareStepUpdateDTO.getId());
        }
        if (ObjectUtils.isNotEmpty(coursewareStepUpdateDTO.getType()) && inValidStepType(coursewareStepUpdateDTO.getType())) {
            throw new BizException("环节类型错误：" + coursewareStep.getType());
        }
        CoursewareStep updatedCoursewareStep = BeanUtil.copyProperties(coursewareStepUpdateDTO, CoursewareStep.class);
        this.updateById(updatedCoursewareStep);
        coursewareDataService.setPubStatusById(coursewareStep.getCoursewareDataId(), TeachingConstant.COURSEWARE_DATA_CANNOT_PUBLISH, coursewareStep.getCoursewareId());
        return BeanUtil.copyProperties(updatedCoursewareStep, CoursewareStepVO.class);
    }

    /**
     * 调整教学环节顺序
     *
     * @param coursewareStepOrder 环节顺序
     */
    @Override
    public void updateBatchByList(CoursewareStepOrderDTO coursewareStepOrder) {

        //获取coursewareStepOrderDTO  infoList 中的id
        List<Integer> ids = coursewareStepOrder.getInfoList().stream().map(CoursewareStepOrderDTO.Info::getId).toList();

        List<CoursewareStep> coursewareStepList = baseMapper.selectList(Wrappers.<CoursewareStep>lambdaQuery()
                .eq(CoursewareStep::getCoursewareId, coursewareStepOrder.getCoursewareId())
                .eq(CoursewareStep::getCoursewareDataId, coursewareStepOrder.getCoursewareDataId())
                .in(CoursewareStep::getId, ids));

        if (coursewareStepList.size() != ids.size()) {
            throw new BizException("数据条数有误");
        }

        for (CoursewareStepOrderDTO.Info info : coursewareStepOrder.getInfoList()) {
            CoursewareStep coursewareStep = new CoursewareStep();
            coursewareStep.setId(info.getId());
            coursewareStep.setStepParent(info.getStepParent());
            coursewareStep.setStepOrder(info.getStepOrder());
            this.updateById(coursewareStep);
        }

        coursewareDataService.setPubStatusById(coursewareStepOrder.getCoursewareDataId(), TeachingConstant.COURSEWARE_DATA_CANNOT_PUBLISH, coursewareStepList.get(0).getCoursewareId());

    }

    /**
     * 删除教学环节
     *
     * @param coursewareStepRemoveDTO 删除参数
     * @return 删除结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean deleteCoursewareStep(CoursewareStepDTO coursewareStepRemoveDTO) {
        CoursewareStep coursewareStep = this.getOne(Wrappers.<CoursewareStep>lambdaQuery()
                .eq(CoursewareStep::getId, coursewareStepRemoveDTO.getId())
                .eq(CoursewareStep::getCoursewareId, coursewareStepRemoveDTO.getCoursewareId()));

        if (Opt.ofNullable(coursewareStep).isEmpty()) {
            throw new BizException("id不存在" + coursewareStepRemoveDTO.getId());
        }
        // 删除当前环节下的页面
        if (coursewareStep.getType() == TeachingConstant.COURSEWARE_STEP_TYPE_LINK) {
            this.remove(Wrappers.<CoursewareStep>lambdaQuery().eq(CoursewareStep::getStepParent, coursewareStep.getId()));
        }
        // 设置发布状态为不可发布
        coursewareDataService.setPubStatusById(coursewareStep.getCoursewareDataId(),
                TeachingConstant.COURSEWARE_DATA_CANNOT_PUBLISH, coursewareStepRemoveDTO.getCoursewareId());
        return this.removeById(coursewareStepRemoveDTO.getId());
    }

    /**
     * 判断环节类型是否合法
     *
     * @param type 环节类型
     * @return 是否合法
     */
    private boolean inValidStepType(int type) {
        return type != TeachingConstant.COURSEWARE_STEP_TYPE_LINK && type != TeachingConstant.COURSEWARE_STEP_TYPE_PAGE;
    }

    /**
     * 获取所有环节
     *
     * @param coursewareStepQuery 查询参数
     * @return 查询结果
     */
    @NotNull
    private List<CoursewareStep> getCoursewareSteps(CoursewareStepQuery coursewareStepQuery) {
        List<CoursewareStep> coursewareStepList = getCoursewareStepList(coursewareStepQuery.getCoursewareId(), coursewareStepQuery.getCoursewareDataId());
        if (coursewareStepList.isEmpty()) {
            // 环节为空时 添加默认环节
            CoursewareData coursewareData = coursewareDataService.getById(coursewareStepQuery.getCoursewareDataId());
            if (Objects.isNull(coursewareData)) {
                throw new BizException("参数错误");
            }

            // 自动创建一个环节和页面
            createDefaultStep(coursewareStepQuery.getCoursewareId(), coursewareStepQuery.getCoursewareDataId());

            return getCoursewareStepList(coursewareStepQuery.getCoursewareId(), coursewareStepQuery.getCoursewareDataId());
        }

        return coursewareStepList;
    }

    /**
     * 创建默认环节
     *
     * @param coursewareId     课程id
     * @param coursewareDataId 课程内容id
     */
    private void createDefaultStep(Integer coursewareId, Integer coursewareDataId) {
        // 新增的时候自动创建一个环节和页面
        CoursewareStep link = CoursewareStep.builder()
                .coursewareId(coursewareId)
                .coursewareDataId(coursewareDataId)
                .stepName(getDefaultStepName())
                .stepParent(0)
                .stepOrder(STEP_ORDER)
                .type(TeachingConstant.COURSEWARE_STEP_TYPE_LINK)
                .build();

        this.save(link);
        TeachingPageTemplateEntity pageTemplate = getFirstPageTemplate();

        CoursewareStep page = CoursewareStep.builder()
                .coursewareId(coursewareId)
                .coursewareDataId(coursewareDataId)
                .stepName(pageTemplate.getTemplateName())
                .stepParent(link.getId())
                .stepOrder(STEP_ORDER)
                .pageTemplateId(pageTemplate.getId())
                .type(TeachingConstant.COURSEWARE_STEP_TYPE_PAGE)
                .build();
        // 设置发布状态为不可发布
        coursewareDataService.setPubStatusById(coursewareDataId,
                TeachingConstant.COURSEWARE_DATA_CANNOT_PUBLISH, coursewareId);
        this.save(page);
    }


    /**
     * 获取默认环节名称
     *
     * @return String
     */
    private String getDefaultStepName() {
        return ParamResolver.getStr(TeachingConstant.STEP_NAME_LINK);
    }


    /**
     * 获取默认教学页ID
     *
     * @return Integer
     */
    private Integer getDefaultPageTemplateId() {
        String pictureBookTemplateId = ParamResolver.getStr(TeachingConstant.PAGE_TEMPLATE_ID);
        if (pictureBookTemplateId.isBlank()){
            throw new BizException("参数配置 默认教学页ID为空！");
        }
        return Integer.valueOf(pictureBookTemplateId);
    }

    /**
     * 获取第一条页面模板
     *
     * @return TeachingPageTemplateEntity 页面模板
     */
    private TeachingPageTemplateEntity getFirstPageTemplate() {
        TeachingPageTemplateEntity teachingPageTemplate =  teachingPageTemplateService.getById(getDefaultPageTemplateId());
        if (Objects.isNull(teachingPageTemplate)){
            throw new BizException("参数配置 默认教学页ID 未查到数据！");
        }
        return teachingPageTemplate;
    }

    @Override
    public List<ClientStepDetailsVO> viewCoursewareStep(CoursewareStepQuery coursewareStepQuery) {
        Integer coursewareId = coursewareStepQuery.getCoursewareId();
        Integer coursewareDataId = coursewareStepQuery.getCoursewareDataId();

        // 获取课节环节详情
        List<StepPubVO> coursewareStepPub = getStepDetails(coursewareId, coursewareDataId);

        // 将 StepPubVO 转换为 ClientStepDetailsVO
        if (coursewareStepPub == null || coursewareStepPub.isEmpty()) {
            return Collections.emptyList();
        }

        ClientStepDetailsVO stepDetailsVo = new ClientStepDetailsVO();
        stepDetailsVo.setPages(coursewareStepPub);

        // 返回包含单个对象的列表（根据需求调整）
        return Collections.singletonList(stepDetailsVo);
    }


    public List<StepPubVO> getStepDetails(Integer coursewareId, Integer coursewareDataId) {

        // 获取课节环节
        List<CoursewareStep> steps = getCoursewareStepList(coursewareId, coursewareDataId);
        if (Objects.isNull(steps)) {
            return Collections.emptyList();
        }
        // 获取steps 的ids列表
        List<Integer> stepIds = steps.stream().map(CoursewareStep::getId).toList();

        // 获取并分组课件步骤详情
        Map<Integer, CoursewareDataStepDetails> stepDetailsMap = getStepDetailsMap(coursewareId, coursewareDataId, stepIds);

        Map<Integer, CoursewareStep> stepMap = steps.stream().collect(Collectors.toMap(CoursewareStep::getId, step -> step));
        // 获取所有模版
        Map<Integer, String> templateMap = getTemplate();

        return steps.stream()
                .filter(step -> step.getType() == TeachingConstant.COURSEWARE_STEP_TYPE_PAGE)
                .filter(step -> ObjectUtil.isNotNull(stepDetailsMap.get(step.getId())))
                .map(step -> {
                    StepPubVO stepViewVO = new StepPubVO();
                    stepViewVO.setId(step.getId());
                    stepViewVO.setStepOrder(step.getStepOrder());
                    stepViewVO.setPageTemplateId(step.getPageTemplateId());
                    stepViewVO.setViewUrl(templateMap.get(step.getPageTemplateId()));
                    stepViewVO.setStepParentOrder(stepMap.get(step.getStepParent()).getStepOrder());

                    CoursewareDataStepDetails stepDetails = stepDetailsMap.get(step.getId());
                    JSONObject objectDetails = JSONUtil.parseObj(ObjectUtil.defaultIfNull(stepDetails.getDetails(), "{}"));
                    JSONObject objectTool = JSONUtil.parseObj(ObjectUtil.defaultIfNull(stepDetails.getTool(), "{}"));

                    if (Objects.equals(step.getPageTemplateId(), getConfigPictureBookTemplateId())) {
                        // 根据绘本查询角色数据
                        List<PictureBookRolePcVO> roleList = coursewarePubService.getRoleList(coursewareId);
                        if (roleList == null || roleList.isEmpty()) {
                            roleList = new ArrayList<>();
                        }
                        objectDetails.set("role", roleList);
                    }

                    objectDetails.set("stepName", stepMap.get(step.getStepParent()).getStepName());

                    // 教学文案
                    String teachingPlan = "";
                    if (Objects.nonNull(stepDetailsMap.get(step.getStepParent()))) {
                        // 获取details
                        teachingPlan = JSONUtil.parseObj(ObjectUtil.defaultIfNull(stepDetailsMap.get(step.getStepParent()).getDetails(), "{}")).getStr("teachingPlan", "");
                    }
                    objectDetails.set("teachingPlan", teachingPlan);
                    addPathField(objectDetails);
                    stepViewVO.setConfigs(objectDetails);
                    addPathField(objectTool);
                    stepViewVO.setTools(objectTool);
                    return stepViewVO;
                })
                .sorted(Comparator.comparingInt(StepPubVO::getStepParentOrder)
                        .thenComparingInt(StepPubVO::getStepOrder))
                .toList();
    }


    /**
     * 获取并分组课件步骤详情
     *
     * @return Map<Integer, CoursewareDataStepDetailsPub>
     */
    private Map<Integer, CoursewareDataStepDetails> getStepDetailsMap(Integer coursewareId, Integer coursewareDataId, List<Integer> stepIds) {
        // 获取并分组课件步骤详情
        return coursewareDataStepDetailsMapper.selectList(Wrappers.<CoursewareDataStepDetails>lambdaQuery()
                        .eq(CoursewareDataStepDetails::getCoursewareId, coursewareId)
                        .eq(CoursewareDataStepDetails::getCoursewareDataId, coursewareDataId)
                        .in(CoursewareDataStepDetails::getStepId, stepIds))
                .stream()
                .collect(Collectors.toMap(
                        CoursewareDataStepDetails::getStepId,
                        coursewareDataStepDetails -> coursewareDataStepDetails
                ));
    }
    /**
     * 获取所有模版
     *
     * @return Map<Integer, String>
     */
    private Map<Integer, String> getTemplate() {
        return teachingPageTemplateMapper.selectList(Wrappers.lambdaQuery(TeachingPageTemplateEntity.class)).stream()
                .collect(Collectors.toMap(
                        TeachingPageTemplateEntity::getId,
                        TeachingPageTemplateEntity::getViewUrl
                ));
    }

    /**
     * 获取模版配置ID
     *
     * @return Integer
     */
    private Integer getConfigPictureBookTemplateId() {
        String pictureBookTemplateId = ParamResolver.getStr(TeachingConstant.PICTURE_BOOK_TEMPLATE_ID);
        if (pictureBookTemplateId == null || pictureBookTemplateId.trim().isEmpty()) {
            return 0;
        }
        return Integer.valueOf(pictureBookTemplateId);
    }
}
