package com.yuedu.teaching.service.impl;


import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;
import org.springframework.beans.BeanUtils;
import cn.hutool.core.util.ArrayUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yuedu.teaching.mapper.StoreCoursewareStepMapper;
import com.yuedu.teaching.service.StoreCoursewareStepService;
import com.yuedu.teaching.query.StoreCoursewareStepQuery;
import com.yuedu.teaching.dto.StoreCoursewareStepDTO;
import com.yuedu.teaching.vo.StoreCoursewareStepVO;
import com.yuedu.teaching.entity.StoreCoursewareStep;

import java.util.List;


/**
 * 门店教学环节表服务层
 *
 * <AUTHOR>
 * @date 2025/08/05
 */
@Service
public class StoreCoursewareStepServiceImpl extends ServiceImpl<StoreCoursewareStepMapper, StoreCoursewareStep>
        implements StoreCoursewareStepService {

}
