package com.yuedu.teaching.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.yulichang.wrapper.MPJLambdaWrapper;
import com.yuedu.teaching.constant.CacheConstant;
import com.yuedu.teaching.constant.TeachingConstant;
import com.yuedu.teaching.constant.enums.DatabaseOperationTypeEnum;
import com.yuedu.teaching.constant.enums.TeachingTypeEnum;
import com.yuedu.teaching.dto.*;
import com.yuedu.teaching.entity.*;
import com.yuedu.teaching.mapper.*;
import com.yuedu.teaching.service.LessonService;
import com.yuedu.teaching.utils.TraceLogUtils;
import com.yuedu.teaching.vo.LessonCoursewareVO;
import com.yuedu.teaching.vo.LessonNameVO;
import com.yuedu.teaching.vo.LessonPracticeVO;
import com.yuedu.teaching.vo.LessonVO;
import com.yuedu.ydsf.common.core.exception.BizException;
import com.yuedu.ydsf.common.core.util.FileUtils;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * @ClassName LessonServiceImpl
 * @Description 课程章节serviceImpl
 * <AUTHOR>
 * @Date 2024/10/24 9:48
 * @Version v0.0.1
 */
@Slf4j
@Service
@AllArgsConstructor
public class LessonServiceImpl extends ServiceImpl<LessonMapper, LessonEntity> implements LessonService {

    private final RedisTemplate<String, String> redisTemplate;
    private LessonMapper lessonMapper;
    private LessonPubMapper lessonPubMapper;
    private TraceLogUtils traceLogUtils;
    private CourseMapper courseMapper;
    private BookNameMapper bookNameMapper;
    private CoursewareMapper coursewareMapper;
    private CoursewareVersionMapper coursewareVersionMapper;
    private BookVersionMapper bookVersionMapper;

    /**
     * 根据课程Id查询课程下的课节数量
     *
     * @param courseId 课程Id
     * @return 课节数量
     */
    @Override
    public Long getLessonCount(Long courseId) {
        LambdaQueryWrapper<LessonEntity> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(LessonEntity::getCourseId, courseId);
        queryWrapper.eq(LessonEntity::getDelFlag, false);
        return lessonMapper.selectCount(queryWrapper);
    }

    /**
     * 通过Id查询课节信息
     *
     * @param id 课节id
     * @return 课节信息
     */
    @Override
    public LessonVO getLessonById(Integer id) {
        LessonVO lessonVO = null;
        LambdaQueryWrapper<LessonEntity> lessonQueryWrapper = Wrappers.lambdaQuery();
        lessonQueryWrapper.eq(LessonEntity::getId, id);
        LessonEntity lessonEntity = lessonMapper.selectOne(lessonQueryWrapper);
        if (lessonEntity != null && lessonEntity.getBookId() != null) {
            lessonVO = new LessonVO();
            BeanUtil.copyProperties(lessonEntity, lessonVO);

            //获取书籍信息
            getBookName(lessonEntity.getBookId(), lessonVO);

            //获取课件名称
            getCoursewareName(lessonEntity.getCoursewareId(), lessonVO);
        }
        return lessonVO;
    }


    /**
     * 通过bookId查询书名
     *
     * @param bookId   书籍Id
     * @param lessonVO 课节vo
     */
    private void getBookName(Integer bookId, LessonVO lessonVO) {
        if (bookId != null) {
            LambdaQueryWrapper<BookName> bookNameQueryWrapper = Wrappers.lambdaQuery();
            bookNameQueryWrapper.eq(BookName::getId, bookId);
            BookName bookName = bookNameMapper.selectOne(bookNameQueryWrapper);
            if (bookName != null) {
                lessonVO.setBookName(bookName.getTitle());
                lessonVO.setAuthor(bookName.getAuthor());
            }
        }
    }

    /**
     * 通过课件Id查询课件名称
     *
     * @param coursewareId 课件Id
     * @param lessonVO     课节vo
     */
    private void getCoursewareName(Integer coursewareId, LessonVO lessonVO) {
        if (coursewareId != null) {
            CoursewareVersion coursewareVersion = coursewareVersionMapper.selectOne(Wrappers.lambdaQuery(CoursewareVersion.class)
                    .eq(CoursewareVersion::getCoursewareId, coursewareId).orderByDesc(CoursewareVersion::getId).last("LIMIT 1"));
            if (coursewareVersion != null) {
                lessonVO.setCoursewareName(coursewareVersion.getCoursewareName());
            }
        }
    }

    /**
     * 通过课程Id获取课节列表
     *
     * @param courseId 课程Id
     * @return 课节列表
     */
    @Override
    public List<LessonVO> getLessonList(Integer courseId) {
        if (courseId == null) {
            throw new BizException("课程编号不能为空!");
        }
        List<LessonVO> lessonVOList = new ArrayList<>();
        LambdaQueryWrapper<LessonEntity> lessonQueryWrapper = Wrappers.lambdaQuery();
        lessonQueryWrapper.eq(LessonEntity::getCourseId, courseId);
        lessonQueryWrapper.orderByAsc(LessonEntity::getLessonOrder);
        List<LessonEntity> lessonList = lessonMapper.selectList(lessonQueryWrapper);
        log.info("课节列表:{}", JSONUtil.toJsonStr(lessonList));
        if (!lessonList.isEmpty()) {
            lessonList.forEach(lessonEntity -> {
                LessonVO lessonVO = new LessonVO();
                BeanUtil.copyProperties(lessonEntity, lessonVO);
                if (lessonEntity != null && lessonEntity.getBookId() != null) {
                    //获取书籍名称
                    getBookName(lessonEntity.getBookId(), lessonVO);

                    //获取课件名称
                    getCoursewareName(lessonEntity.getCoursewareId(), lessonVO);
                }
                lessonVOList.add(lessonVO);
            });
        }
        return lessonVOList;
    }

    /**
     * 通过id列表获取课节名称列表
     *
     * @param ids 课节id列表
     * @return 课节名称列表
     */
    @Override
    public List<LessonNameVO> listLessonNameByIds(List<Long> ids) {
        return lessonPubMapper.selectList(Wrappers.<LessonPubEntity>lambdaQuery()
                        .select(LessonPubEntity::getLessonId, LessonPubEntity::getLessonName, LessonPubEntity::getVersion)
                        .in(LessonPubEntity::getLessonId, ids))
                .stream()
                .collect(Collectors.groupingBy(LessonPubEntity::getLessonId,
                        Collectors.maxBy(Comparator.comparingInt(LessonPubEntity::getVersion))))
                .values().stream().filter(Optional::isPresent).map(Optional::get)
                .map(s -> new LessonNameVO(s.getLessonId(), s.getCourseId(), s.getLessonName(), s.getVersion(), s.getLessonOrder())).toList();
    }


    /**
     * 通过Id列表查询课件Id和课件名称
     *
     * @param ids id列表
     * @return List<CoursewareVO>
     */
    @Override
    public List<LessonCoursewareVO> listCoursewareByIds(List<Integer> ids) {
        return lessonPubMapper.selectJoinList(LessonCoursewareVO.class,
                new MPJLambdaWrapper<LessonPubEntity>()
                        .select(LessonPubEntity::getLessonId)
                        .select(Courseware::getId, Courseware::getCoursewareName)
                        .leftJoin(Courseware.class, Courseware::getId, LessonPubEntity::getCoursewareId)
                        .in(LessonPubEntity::getLessonId, ids));
    }

    /**
     * 保存课节
     *
     * @param lessonAddDTO 课节信息
     */
    @Override
    public int saveLesson(LessonAddDTO lessonAddDTO) {
        LessonEntity lessonEntity = new LessonEntity();
        BeanUtil.copyProperties(lessonAddDTO, lessonEntity);

        //课件已限制为必填项,课节保存后状态变为可发布
        lessonEntity.setCanPublish(TeachingConstant.LESSON_CAN_PUBLISH);

        //获取并设置最新的课节顺序
        lessonEntity.setLessonOrder(getLessonOrder(lessonAddDTO) + TeachingConstant.NEXT_LESSON_ORDER);

        int result = lessonMapper.insert(lessonEntity);

        if (result > 0) {
            //课节添加成功后，更新课程表中的课节数量
            updateLessonCount(lessonEntity.getCourseId());
        }

        //课节Id
        lessonEntity.setId(lessonEntity.getId());

        //记录操作日志
        traceLogUtils.saveTraceLog(lessonEntity, null, TeachingTypeEnum.LESSON.getCode(), DatabaseOperationTypeEnum.INSERT.getCode());

        return 1;
    }


    /**
     * 获取下一个课节顺序
     *
     * @param lessonAddDTO 课节添加dto
     * @return 课节顺序
     */
    private Integer getLessonOrder(LessonAddDTO lessonAddDTO) {
        LambdaQueryWrapper<LessonEntity> lessonQueryWrapper = Wrappers.lambdaQuery();
        lessonQueryWrapper.select(LessonEntity::getLessonOrder);
        lessonQueryWrapper.eq(LessonEntity::getCourseId, lessonAddDTO.getCourseId());
        lessonQueryWrapper.orderByDesc(LessonEntity::getLessonOrder);
        List<LessonEntity> lessonList = lessonMapper.selectList(lessonQueryWrapper);
        Integer lessonOrder = 0;
        if (!lessonList.isEmpty()) {
            lessonOrder = lessonList.get(0).getLessonOrder();
        }
        return lessonOrder;
    }


    /**
     * 通过Id修改课节信息
     *
     * @param updateDTO 课节信息
     */
    @Override
    public int updateLessonById(LessonUpdateDTO updateDTO) {
        LessonEntity lesson = new LessonEntity();
        BeanUtil.copyProperties(updateDTO, lesson);

        //记录日志
        traceLogUtils.saveTraceLog(lesson, getLesson(lesson), TeachingTypeEnum.LESSON.getCode(), DatabaseOperationTypeEnum.UPDATE.getCode());

        return lessonMapper.updateById(lesson);
    }


    /**
     * 批量更新课节为发布状态
     *
     * @param canPublishIdList 可发布课节Id列表
     */
    @Override
    @Transactional
    public int updateLessonByList(List<Long> canPublishIdList) {
        LambdaUpdateWrapper<LessonEntity> lessonUpdateWrapper = Wrappers.lambdaUpdate();
        //更新条件
        lessonUpdateWrapper.in(LessonEntity::getId, canPublishIdList);
        //要修改的信息
        lessonUpdateWrapper.set(LessonEntity::getPublishStatus, TeachingConstant.LESSON_PUBLISH_STATUS_PUBLISH);
        lessonUpdateWrapper.set(LessonEntity::getCanPublish, TeachingConstant.LESSON_CANNOT_PUBLISH);
        return lessonMapper.update(lessonUpdateWrapper);
    }

    /**
     * 通过id删除课程章节表
     *
     * @param removeLessonDto 科技删除dto
     * @return 结果
     */
    @Transactional
    @Override
    public boolean removeLesson(RemoveLessonDto removeLessonDto) {
        if (Objects.isNull(removeLessonDto) || Objects.isNull(removeLessonDto.getId())) {
            throw new BizException("删除课节Id不能为空!");
        }
        LessonEntity lessonEntity = this.getById(removeLessonDto.getId());
        if (Objects.isNull(lessonEntity)) {
            throw new BizException("删除课节不存在!");
        }
        LambdaUpdateWrapper<LessonEntity> wrapper = Wrappers.lambdaUpdate();
        wrapper.eq(LessonEntity::getId, removeLessonDto.getId());
        //未发布的课节才能删除
        wrapper.eq(LessonEntity::getPublishStatus, 0);
        boolean flag = this.remove(wrapper);
        if (flag) {
            //课节添加成功后，更新课程表中的课节数量
            updateLessonCount(removeLessonDto.getCourseId());
            //更新lessonOrder
            updateLessonOrder(removeLessonDto, lessonEntity);
        }

        //记录操作日志
        LessonEntity oldLesson = this.getOne(Wrappers.<LessonEntity>lambdaQuery().eq(LessonEntity::getId, removeLessonDto.getId()));
        LessonEntity newLesson = new LessonEntity();
        BeanUtil.copyProperties(oldLesson, newLesson);
        traceLogUtils.saveTraceLog(newLesson, oldLesson, TeachingTypeEnum.LESSON.getCode(), DatabaseOperationTypeEnum.DELETE.getCode());
        return flag;
    }

    private void updateLessonOrder(RemoveLessonDto removeLessonDto, LessonEntity lessonEntity) {
        List<LessonEntity> lessonList = this.list(Wrappers.lambdaQuery(LessonEntity.class)
                        .eq(LessonEntity::getCourseId, removeLessonDto.getCourseId()))
                .stream()
                .filter(lesson -> lesson.getLessonOrder() > lessonEntity.getLessonOrder())
                .map(lesson -> {
                    LessonEntity updateLesson = new LessonEntity();
                    updateLesson.setId(lesson.getId());
                    updateLesson.setLessonOrder(lesson.getLessonOrder() - 1);
                    return updateLesson;
                }).toList();
        if (CollectionUtils.isNotEmpty(lessonList)) {
            lessonMapper.updateById(lessonList);
        }
    }


    /**
     * 更新课程的课节数量
     *
     * @param courseId 课程Id
     */
    private void updateLessonCount(Long courseId) {
        //获取当前课程下的课节数量
        Long lessonSum = getLessonCount(courseId);
        //原课程信息
        Course oldCourse = courseMapper.selectOne(Wrappers.<Course>lambdaQuery().eq(Course::getId, courseId));

        if (oldCourse != null) {
            //更新课节数量
            LambdaUpdateWrapper<Course> updateWrapper = Wrappers.lambdaUpdate();
            updateWrapper.eq(Course::getId, courseId);
            updateWrapper.set(Course::getLessonCount, lessonSum);
            courseMapper.update(updateWrapper);

            //记录课程中课节数量变化
            Course newCourse = new Course();
            BeanUtil.copyProperties(oldCourse, newCourse);
            newCourse.setLessonCount(lessonSum.intValue());
            traceLogUtils.saveTraceLog(newCourse, oldCourse, TeachingTypeEnum.COURSE.getCode(), DatabaseOperationTypeEnum.UPDATE.getCode());
        }
    }


    /**
     * 查询课节信息
     *
     * @param lesson 课节
     * @return 结果
     */
    private LessonEntity getLesson(LessonEntity lesson) {
        LambdaQueryWrapper<LessonEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(LessonEntity::getId, lesson.getId());
        return lessonMapper.selectOne(queryWrapper);
    }

    /**
     * 调整课节顺序
     *
     * @param updateBatchLessonDto 列表
     * @return 结果
     */
    @Override
    public int updateBatchByList(UpdateBatchLessonDto updateBatchLessonDto) {
        Course course = courseMapper.selectOne(Wrappers.<Course>lambdaQuery()
                .eq(Course::getId, updateBatchLessonDto.getCourseId())
                .eq(Course::getPublishStatus, TeachingConstant.COURSE_PUBLISH_STATUS_PUBLISH));
        if (course != null) {
            throw new BizException("已发布的课程不能再调整课节顺序!");
        }

        //排序完成课节列表
        List<LessonEntity> entityList = updateBatchLessonDto.getEntityList();
        //记录日志
        LambdaQueryWrapper<LessonEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(LessonEntity::getCourseId, updateBatchLessonDto.getCourseId());
        List<LessonEntity> oldLessonList = lessonMapper.selectList(queryWrapper);
        traceLogUtils.saveTraceLog(entityList, oldLessonList, TeachingTypeEnum.LESSON.getCode(), DatabaseOperationTypeEnum.INSERT.getCode());

        for (LessonEntity lessonEntity : entityList) {
            if (lessonEntity.getLessonName() == null) {
                throw new BizException("Id为" + lessonEntity.getId() + "的课节名称为空!");
            }
            if (lessonEntity.getLessonOrder() == null) {
                throw new BizException("Id为" + lessonEntity.getId() + "的课节顺序为空!");
            }
            lessonMapper.updateById(lessonEntity);
        }

        return 1;
    }

    /**
     * 根据课程id获取已发布的课节列表
     *
     * @param courseId 课程id
     * @return List<LessonVO>
     */
    @Override
    public List<LessonVO> getPublishLessonList(Long courseId, Integer version) {
        Course course;
        // 要查询的版本
        Integer selectVersion;

        // 根据id获取课程最新版本
        if (Objects.isNull(version)) {
            course = courseMapper.selectById(courseId);
            if (Objects.isNull(course)) {
                log.error("没有找到该课程:courseId:{}", courseId);
                return List.of();
            }
            selectVersion = course.getVersion();
        } else {
            selectVersion = version;
        }

        if (Objects.isNull(selectVersion)) {
            log.error("该课程要查询的版本为空,课程id:{}", courseId);
            return List.of();
        }

        // 缓存获取查询结果
        String lessonCache = getLessonCache(courseId, selectVersion);
        if (CharSequenceUtil.isNotBlank(lessonCache)) {
            return JSONUtil.toList(lessonCache, LessonVO.class);
        }

        List<LessonPubEntity> lessonPubEntityList = lessonPubMapper.selectList(Wrappers.lambdaQuery(LessonPubEntity.class)
                .eq(LessonPubEntity::getCourseId, courseId)
                .eq(LessonPubEntity::getVersion, selectVersion)
                .orderByAsc(LessonPubEntity::getLessonOrder));

        if (CollUtil.isEmpty(lessonPubEntityList)) {
            log.error("该课程没有已发布的课节,课程id:{}", courseId);
            return Collections.emptyList();
        }

        List<LessonVO> lessonVOList = new ArrayList<>();

        lessonPubEntityList.forEach(lessonPubEntity -> {
            LessonVO lessonVO = new LessonVO();
            BeanUtil.copyProperties(lessonPubEntity, lessonVO, "id");
            lessonVO.setId(lessonPubEntity.getLessonId());
            lessonVOList.add(lessonVO);
        });

        // 获取所有相关的 coursewareId
        Set<Integer> coursewareIds = lessonPubEntityList.stream()
                .map(LessonPubEntity::getCoursewareId)
                .collect(Collectors.toSet());

        List<Courseware> coursewareList = coursewareMapper.selectList(Wrappers.lambdaQuery(Courseware.class)
                .in(Courseware::getId, coursewareIds));

        List<Integer> versionList = coursewareList.stream()
                .map(Courseware::getVersion)
                .toList();

        List<CoursewareVersion> coursewareVersionList = coursewareVersionMapper.selectList(Wrappers.lambdaQuery(CoursewareVersion.class)
                .in(CoursewareVersion::getCoursewareId, coursewareIds)
                .in(CoursewareVersion::getId, versionList));

        List<Courseware> coursewares = new ArrayList<>();
        coursewareVersionList.forEach(coursewareVersion -> {
            Courseware courseware = BeanUtil.copyProperties(coursewareVersion, Courseware.class);
            courseware.setId(coursewareVersion.getCoursewareId());
            courseware.setVersion(coursewareVersion.getId());
            coursewares.add(courseware);
        });

        Map<Integer, Courseware> coursewareMap = coursewares.stream()
                .collect(Collectors.toMap(Courseware::getId, courseware -> courseware));

        // 查询 courseware 并构建 coursewareId 到 bookVersionId 的映射
        Map<Integer, Integer> coursewareIdToBookVersionIdMap = coursewareList
                .stream()
                .collect(Collectors.toMap(Courseware::getId, Courseware::getBookVersionId));

        List<BookVersion> bookVersionList = bookVersionMapper.selectList(Wrappers.lambdaQuery(BookVersion.class)
                .in(BookVersion::getId, coursewareIdToBookVersionIdMap.values()));

        // 查询 bookVersion 并构建 bookVersionId 到 cover 的映射
        Map<Integer, String> bookVersionIdToCoverMap = bookVersionList
                .stream()
                .collect(Collectors.toMap(BookVersion::getId, BookVersion::getCover));

        // 查询书籍名称 构建BookNameId到书籍名称的映射
        List<Integer> bookIds = bookVersionList.stream()
                .map(BookVersion::getBookId)
                .toList();

        Map<Integer, String> bookNameMap = bookNameMapper.selectList(Wrappers.lambdaQuery(BookName.class)
                        .in(BookName::getId, bookIds))
                .stream()
                .collect(Collectors.toMap(BookName::getId, BookName::getTitle));

        // 构建 coursewareId到书籍名称映射
        Map<Integer, String> coursewareIdToBookNameMap = coursewareMapper.selectList(Wrappers.lambdaQuery(Courseware.class)
                        .in(Courseware::getId, coursewareIds))
                .stream()
                .collect(Collectors.toMap(Courseware::getId, courseware -> bookNameMap.get(courseware.getBookId())));

        Integer courseVersion = selectVersion;

        // 将封面路径和书名放入 lessonVOList
        lessonVOList.forEach(lessonVO -> {
            Integer coursewareId = lessonVO.getCoursewareId();
            Integer bookVersionId = coursewareIdToBookVersionIdMap.get(coursewareId);
            String cover = bookVersionIdToCoverMap.get(bookVersionId);
            lessonVO.setImgUrl(FileUtils.completeUrl(cover));
            lessonVO.setBookName(coursewareIdToBookNameMap.get(coursewareId));
            lessonVO.setLessonVersion(courseVersion);
            // 存储课件信息
            Courseware courseware = coursewareMap.get(lessonVO.getCoursewareId());
            if (ObjectUtil.isNotEmpty(courseware)) {
                lessonVO.setCoursewareId(courseware.getId());
                lessonVO.setCoursewareName(courseware.getCoursewareName());
                lessonVO.setBookId(courseware.getBookId());
                lessonVO.setBookVersionId(courseware.getBookVersionId());
                lessonVO.setCoursewareVersion(courseware.getVersion());
            }
        });

        // 缓存课节列表
        String lessonListJson = JSONUtil.toJsonStr(lessonVOList);
        cacheLesson(courseId, selectVersion, lessonListJson);

        log.info("获取课节返回数据{}", JSONUtil.toJsonStr(lessonVOList));

        return lessonVOList;
    }

    /**
     * 缓存课节列表
     *
     * @param courseId       课程id
     * @param version        版本
     * @param lessonListJson 课节列表Json
     */
    private void cacheLesson(Long courseId, Integer version, String lessonListJson) {
        redisTemplate.opsForValue().set(String.format(CacheConstant.LESSON_PUB, courseId, version), lessonListJson, 5L, TimeUnit.MINUTES);
    }

    /**
     * 获取缓存课节列表
     *
     * @param courseId 课程id
     * @param version  版本
     * @return List<LessonVO>
     */
    private String getLessonCache(Long courseId, Integer version) {
        String key = String.format(CacheConstant.LESSON_PUB, courseId, version);
        return redisTemplate.opsForValue().get(key);
    }

    /**
     * 根据课节名称查询已发布的课节列表
     *
     * @param lessonName 课节名称
     * @return List<LessonEntity>
     */
    @Override
    public List<LessonEntity> getPublishLessonListByName(Long courseId, String lessonName) {
        List<LessonPubEntity> lessonPubEntityList = lessonPubMapper.selectList(Wrappers.lambdaQuery(LessonPubEntity.class)
                .eq(ObjectUtil.isNotEmpty(courseId), LessonPubEntity::getCourseId, courseId)
                .like(CharSequenceUtil.isNotBlank(lessonName), LessonPubEntity::getLessonName, lessonName)
                .orderByDesc(LessonPubEntity::getUpdateTime));

        // 使用一个集合来存储已经处理过的课节ID
        Set<Long> processedLessonIds = new HashSet<>();
        List<LessonPubEntity> uniqueLessonPubEntityList = new ArrayList<>();

        // 去重，每个课节只保留最新的一条记录
        for (LessonPubEntity lessonPubEntity : lessonPubEntityList) {
            if (!processedLessonIds.contains(lessonPubEntity.getLessonId())) {
                uniqueLessonPubEntityList.add(lessonPubEntity);
                processedLessonIds.add(lessonPubEntity.getLessonId());
            }
        }

        // 转换为 LessonEntity 列表
        return uniqueLessonPubEntityList.stream()
                .map(lessonPubEntity -> {
                    LessonEntity lessonEntity = new LessonEntity();
                    BeanUtil.copyProperties(lessonPubEntity, lessonEntity);
                    return lessonEntity;
                })
                .toList();
    }

    /**
     * 根据课节id列表查询已发布课节列表
     *
     * @param idList 课节id列表
     * @return List<LessonVO>
     */
    @Override
    public List<LessonVO> getPublishLessonListById(List<Integer> idList) {
        // 给输入的idList去重
        idList = idList.stream().distinct().toList();

        // 通过id获取最新版本
        List<Long> courseIdList = lessonPubMapper.selectList(Wrappers.lambdaQuery(LessonPubEntity.class)
                        .select(LessonPubEntity::getCourseId)
                        .in(LessonPubEntity::getLessonId, idList)
                        .groupBy(LessonPubEntity::getCourseId))
                .stream()
                .distinct()
                .map(LessonPubEntity::getCourseId)
                .toList();

        List<LessonVO> lessonVOList = new ArrayList<>();

        courseIdList.forEach(courseId -> lessonVOList.addAll(getPublishLessonList(courseId, null)));

        List<Integer> finalIdList = idList;
        return lessonVOList.stream()
                .filter(lessonVO -> finalIdList.contains(lessonVO.getId().intValue()))
                .toList();
    }

    /**
     * 根据版本集合获取课节名称集合
     *
     * @param versionList 版本集合
     * @return List<LessonNameVO>
     */
    @Override
    public List<LessonNameVO> getLessonNameList(List<Integer> versionList) {
        versionList = versionList.stream().distinct().toList();

        List<LessonPubEntity> lessonPubEntityList = lessonPubMapper.selectList(Wrappers.<LessonPubEntity>lambdaQuery()
                .select(LessonPubEntity::getLessonId, LessonPubEntity::getLessonName, LessonPubEntity::getVersion, LessonPubEntity::getCourseId, LessonPubEntity::getLessonOrder)
                .in(LessonPubEntity::getVersion, versionList));

        List<LessonNameVO> lessonNameVOList = new ArrayList<>();

        lessonPubEntityList.forEach(lessonPubEntity -> {
            LessonNameVO lessonNameVO = BeanUtil.copyProperties(lessonPubEntity, LessonNameVO.class);
            lessonNameVO.setId(lessonPubEntity.getLessonId());
            lessonNameVOList.add(lessonNameVO);
        });
        return lessonNameVOList;
    }

    /**
     * 根据课程id和课节排序集合获取课节列表
     *
     * @param lessonOrderDTOList 课节排序DTO集合
     * @return List<LessonVO>
     */
    @Override
    public List<LessonVO> getLessonListByOrder(List<LessonOrderDTO> lessonOrderDTOList) {
        if (CollUtil.isEmpty(lessonOrderDTOList)) {
            return List.of();
        }

        // 需要返回的课节列表
        List<LessonVO> lessonVOList = new ArrayList<>();

        // 给lessonOrderDTOList中没有version的课程查询版本
        List<Long> courseIdList = lessonOrderDTOList.stream()
                .filter(lessonOrderDTO -> ObjectUtil.isEmpty(lessonOrderDTO.getVersion()))
                .map(LessonOrderDTO::getCourseId)
                .distinct()
                .toList();

        if (CollUtil.isNotEmpty(courseIdList)) {
            fetchCourseVersion(courseIdList, lessonOrderDTOList);
        }

        lessonOrderDTOList.forEach(lessonOrderDTO -> {
            List<LessonVO> publishLessonList = getPublishLessonList(lessonOrderDTO.getCourseId(), lessonOrderDTO.getVersion());
            if (CollUtil.isEmpty(publishLessonList)) {
                log.error("该课程courseId:{}下没有课节", lessonOrderDTO.getCourseId());
                return;
            }
            lessonVOList.addAll(publishLessonList);
            // 获取的该课程下课节版本都相同，填入DTO中
            lessonOrderDTO.setVersion(publishLessonList.get(0).getLessonVersion());
        });

        List<LessonVO> lessonOrderList = new ArrayList<>();

        lessonOrderDTOList.forEach(lessonOrderDTO -> {
            if (ObjectUtil.isEmpty(lessonOrderDTO.getVersion())) {
                log.error("该课程courseId:{}下没有版本", lessonOrderDTO.getCourseId());
                return;
            }
            if (CollUtil.isEmpty(lessonOrderDTO.getLessonOrderList())) {
                lessonOrderList.addAll(lessonVOList.stream()
                        .filter(lessonVO -> lessonOrderDTO.getCourseId().equals(lessonVO.getCourseId()) &&
                                lessonOrderDTO.getVersion().equals(lessonVO.getLessonVersion()))
                        .toList());
            } else {
                lessonOrderList.addAll(lessonVOList.stream()
                        .filter(lessonVO -> lessonOrderDTO.getCourseId().equals(lessonVO.getCourseId()) &&
                                lessonOrderDTO.getLessonOrderList().contains(lessonVO.getLessonOrder()) &&
                                lessonOrderDTO.getVersion().equals(lessonVO.getLessonVersion()))
                        .toList());
            }
        });

        return lessonOrderList;
    }

    /**
     * 获取lessonOrderDTOList中课程没有的版本
     *
     * @param courseIdList       没有版本的课程id集合
     * @param lessonOrderDTOList 课节排序DTO集合
     */
    private void fetchCourseVersion(List<Long> courseIdList, List<LessonOrderDTO> lessonOrderDTOList) {
        List<Course> courseList = courseMapper.selectList(Wrappers.lambdaQuery(Course.class)
                .select(Course::getVersion, Course::getId)
                .in(Course::getId, courseIdList));

        Map<Long, Integer> courseVersionMap = courseList.stream()
                .collect(Collectors.toMap(
                        course -> Long.valueOf(course.getId()),
                        Course::getVersion));

        lessonOrderDTOList.forEach(lessonOrderDTO -> {
            if (ObjectUtil.isEmpty(lessonOrderDTO.getVersion())) {
                Integer version = courseVersionMap.get(lessonOrderDTO.getCourseId());
                if (Objects.nonNull(version)) {
                    lessonOrderDTO.setVersion(version);
                }
            }
        });
    }

    /**
     * 获取课节练课列表
     *
     * @param courseId 课程id
     * @param version  版本
     * @return List<LessonPracticeVO>
     */
    @Override
    public List<LessonPracticeVO> getLessonPracticeList(Long courseId, Integer version) {
        List<LessonVO> publishLessonList = getPublishLessonList(courseId, version);

        if (CollUtil.isEmpty(publishLessonList)) {
            return List.of();
        }

        return BeanUtil.copyToList(publishLessonList, LessonPracticeVO.class);
    }
}