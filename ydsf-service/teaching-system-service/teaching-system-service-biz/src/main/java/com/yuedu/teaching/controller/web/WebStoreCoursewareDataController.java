package com.yuedu.teaching.controller.web;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yuedu.ydsf.common.core.util.R;
import com.yuedu.ydsf.common.core.util.V_A;
import com.yuedu.ydsf.common.core.util.V_E;
import com.yuedu.ydsf.common.excel.annotation.ResponseExcel;
import com.yuedu.ydsf.common.log.annotation.SysLog;
import com.yuedu.ydsf.common.security.annotation.StorePermission;
import com.yuedu.ydsf.common.xss.core.XssCleanIgnore;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springdoc.core.annotations.ParameterObject;
import org.springframework.http.HttpHeaders;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import com.yuedu.ydsf.common.security.annotation.HasPermission;
import jakarta.validation.Valid;
import com.yuedu.teaching.service.StoreCoursewareDataService;
import com.yuedu.teaching.service.StoreCoursewareStepService;
import com.yuedu.teaching.query.StoreCoursewareDataQuery;
import com.yuedu.teaching.query.StoreCoursewareDataStepDetailsQuery;
import com.yuedu.teaching.dto.StoreCoursewareDataDTO;
import com.yuedu.teaching.dto.StoreCoursewareDataStepDetailsDTO;
import com.yuedu.teaching.dto.StoreCoursewareDataStatusDTO;
import com.yuedu.teaching.dto.StoreCoursewareRestoreDTO;
import com.yuedu.teaching.dto.StoreCoursewareStepDTO;
import com.yuedu.teaching.dto.StoreCoursewareStepOrderDTO;
import com.yuedu.teaching.vo.StoreCoursewareDataVO;
import com.yuedu.teaching.vo.StepVO;
import com.yuedu.teaching.vo.CoursewareDataStepDetailsVO;
import com.yuedu.teaching.vo.ClientStepDetailsVO;
import com.yuedu.teaching.vo.StoreCoursewareStepVO;
import com.yuedu.teaching.dto.CoursewareCopyDTO;
import com.yuedu.teaching.valid.StoreCoursewareDataStepDetailsValidGroup;
import com.yuedu.teaching.valid.StoreCoursewareDataValidGroup;
import com.yuedu.teaching.valid.StoreCoursewareStepValidGroup;

import java.io.Serializable;
import java.util.List;

/**
 * 门店课件表控制层
 *
 * <AUTHOR>
 * @date 2025/08/05
 */

@RestController
@RequiredArgsConstructor
@RequestMapping(value = "/storeCoursewareData")
@Tag(description = "store_courseware_data", name = "门店课件表")
@StorePermission
public class WebStoreCoursewareDataController {


    private final StoreCoursewareDataService storeCoursewareDataService;
    private final StoreCoursewareStepService storeCoursewareStepService;

    /**
     * 创建课件副本
     *
     * @param coursewareCopyDTO 课件副本创建参数
     * @return R<Boolean> 创建结果
     */
    @Operation(summary = "创建课件副本", description = "门店复制当前环节的课件信息，每个账号每个课件环节只能创建一个副本")
    @SysLog("创建课件副本")
    @PostMapping("/createCoursewareCopy")
    public R<Boolean> createCoursewareCopy(@Validated @RequestBody CoursewareCopyDTO coursewareCopyDTO) {
        return R.ok(storeCoursewareDataService.createCoursewareCopy(coursewareCopyDTO));
    }

    /**
     * 查询所有教学环节
     *
     * @param storeCoursewareDataQuery 查询参数
     * @return R<List<StepVO>> 教学环节列表
     */
    @Operation(summary = "查询所有教学环节", description = "查询并展示当前课件下的所有教学环节")
    @GetMapping("/listStoreCoursewareSteps")
    public R<List<StepVO>> listStoreCoursewareSteps(@Validated @ParameterObject StoreCoursewareDataQuery storeCoursewareDataQuery) {
        return R.ok(storeCoursewareDataService.listStoreCoursewareSteps(storeCoursewareDataQuery));
    }

    /**
     * 查询门店课件环节详情
     *
     * @param storeCoursewareDataStepDetailsQuery 查询参数
     * @return R<CoursewareDataStepDetailsVO> 环节详情
     */
    @Operation(summary = "查询门店课件环节详情", description = "查询门店创建的副本课件环节详细信息，确保门店只能查看自己创建的副本课件")
    @GetMapping("/getStoreStepDetails")
    public R<CoursewareDataStepDetailsVO> getStoreStepDetails(
            @Validated(StoreCoursewareDataStepDetailsValidGroup.StoreDataStepDetails.class)
            @ParameterObject StoreCoursewareDataStepDetailsQuery storeCoursewareDataStepDetailsQuery) {
        return R.ok(storeCoursewareDataService.getStoreStepDetails(storeCoursewareDataStepDetailsQuery));
    }

    /**
     * 编辑保存门店课件环节详情
     *
     * @param storeCoursewareDataStepDetailsDTO 环节详情更新参数（包含submitForUse参数控制是否提交使用）
     * @return R<Boolean> 更新结果
     */
    @Operation(summary = "编辑保存门店课件环节详情", description = "门店编辑修改自己创建的副本课件环节详情。submitForUse=true时为'保存并提交'，将课件设置为使用状态；submitForUse=false或null时仅保存，不改变使用状态")
    @SysLog("编辑保存门店课件环节详情")
    @PutMapping("/updateStoreStepDetails")
    @XssCleanIgnore
    public R<Boolean> updateStoreStepDetails(
            @Validated(StoreCoursewareDataStepDetailsValidGroup.UpdateStoreDataStepDetails.class)
            @RequestBody @Valid StoreCoursewareDataStepDetailsDTO storeCoursewareDataStepDetailsDTO) {
        return R.ok(storeCoursewareDataService.updateStoreStepDetails(storeCoursewareDataStepDetailsDTO));
    }

    /**
     * 更新门店课件使用状态
     *
     * @param storeCoursewareDataStatusDTO 状态更新参数
     * @return R<Boolean> 更新结果
     */
    @Operation(summary = "更新门店课件使用状态", description = "单独控制门店副本课件的启用/禁用状态，确保门店只能修改自己创建的副本课件状态")
    @SysLog("更新门店课件使用状态")
    @PutMapping("/updateStoreCoursewareStatus")
    public R<Boolean> updateStoreCoursewareStatus(
            @Validated(StoreCoursewareDataValidGroup.UpdateStatus.class)
            @RequestBody @Valid StoreCoursewareDataStatusDTO storeCoursewareDataStatusDTO) {
        return R.ok(storeCoursewareDataService.updateStoreCoursewareStatus(storeCoursewareDataStatusDTO));
    }

    /**
     * 恢复门店课件为标准版内容
     *
     * @param storeCoursewareRestoreDTO 恢复参数
     * @return R<Boolean> 恢复结果
     */
    @Operation(summary = "恢复门店课件为标准版内容", description = "将门店副本课件内容恢复为总部标准版最新内容，保持门店副本身份标识不变，仅同步内容数据")
    @SysLog("恢复门店课件为标准版内容")
    @PutMapping("/restoreStoreCoursewareToStandard")
    public R<Boolean> restoreStoreCoursewareToStandard(
            @Validated(StoreCoursewareDataValidGroup.RestoreStandard.class)
            @RequestBody @Valid StoreCoursewareRestoreDTO storeCoursewareRestoreDTO) {
        return R.ok(storeCoursewareDataService.restoreStoreCoursewareToStandard(storeCoursewareRestoreDTO));
    }

    /**
     * 门店副本课件预览接口
     *
     * @param storeCoursewareDataQuery 查询参数
     * @return R<List<ClientStepDetailsVO>> 预览数据
     */
    @Operation(summary = "门店副本课件预览", description = "预览门店创建的副本课件内容，返回与标准课件预览相同的数据结构")
    @GetMapping("/viewStoreCourseware")
    public R<List<ClientStepDetailsVO>> viewStoreCourseware(
            @Validated(StoreCoursewareDataValidGroup.ViewStoreCourseware.class)
            @ParameterObject StoreCoursewareDataQuery storeCoursewareDataQuery) {
        return R.ok(storeCoursewareDataService.viewStoreCourseware(storeCoursewareDataQuery));
    }

    /**
     * 新增门店教学环节
     *
     * @param storeCoursewareStepDTO 教学环节新增参数
     * @return R<StoreCoursewareStepVO> 新增结果
     */
    @Operation(summary = "新增门店教学环节", description = "门店自己创建的课件副本可以新增教学环节，确保只能操作门店自己创建的副本")
    @SysLog("新增门店教学环节")
    @PostMapping("/addStoreCoursewareStep")
    public R<StoreCoursewareStepVO> addStoreCoursewareStep(
            @Validated(StoreCoursewareStepValidGroup.AddStoreStep.class)
            @RequestBody StoreCoursewareStepDTO storeCoursewareStepDTO) {
        return R.ok(storeCoursewareStepService.addStoreCoursewareStep(storeCoursewareStepDTO));
    }

    /**
     * 修改门店教学环节
     *
     * @param storeCoursewareStepDTO 教学环节修改参数
     * @return R<StoreCoursewareStepVO> 修改结果
     */
    @Operation(summary = "修改门店教学环节", description = "门店自己创建的课件副本可以修改教学环节，确保只能操作门店自己创建的副本")
    @SysLog("修改门店教学环节")
    @PutMapping("/updateStoreCoursewareStep")
    public R<StoreCoursewareStepVO> updateStoreCoursewareStep(
            @Validated(StoreCoursewareStepValidGroup.UpdateStoreStep.class)
            @RequestBody StoreCoursewareStepDTO storeCoursewareStepDTO) {
        return R.ok(storeCoursewareStepService.updateStoreCoursewareStep(storeCoursewareStepDTO));
    }

    /**
     * 调整门店教学环节顺序
     *
     * @param storeCoursewareStepOrderDTO 教学环节顺序调整参数
     * @return R 调整结果
     */
    @Operation(summary = "调整门店教学环节顺序", description = "门店自己创建的课件副本可以调整教学环节顺序，确保只能操作门店自己创建的副本")
    @SysLog("调整门店教学环节顺序")
    @PutMapping("/updateStoreCoursewareStepOrder")
    public R updateStoreCoursewareStepOrder(
            @Validated(StoreCoursewareStepValidGroup.UpdateStoreStepOrder.class)
            @RequestBody StoreCoursewareStepOrderDTO storeCoursewareStepOrderDTO) {
        storeCoursewareStepService.updateStoreCoursewareStepOrder(storeCoursewareStepOrderDTO);
        return R.ok();
    }

    /**
     * 删除门店教学环节
     *
     * @param storeCoursewareStepDTO 教学环节删除参数
     * @return R<Boolean> 删除结果
     */
    @Operation(summary = "删除门店教学环节", description = "门店自己创建的课件副本可以删除教学环节，确保只能操作门店自己创建的副本")
    @SysLog("删除门店教学环节")
    @DeleteMapping("/deleteStoreCoursewareStep")
    public R<Boolean> deleteStoreCoursewareStep(
            @Validated(StoreCoursewareStepValidGroup.DeleteStoreStep.class)
            @RequestBody StoreCoursewareStepDTO storeCoursewareStepDTO) {
        return R.ok(storeCoursewareStepService.deleteStoreCoursewareStep(storeCoursewareStepDTO));
    }

}
