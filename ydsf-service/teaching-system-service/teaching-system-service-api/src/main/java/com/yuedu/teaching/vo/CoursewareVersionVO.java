package com.yuedu.teaching.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import java.time.LocalDateTime;

/**
 * @ClassName CoursewareVersionVO
 * @Description 课件版本视图类
 * <AUTHOR>
 * @Date 2024/12/3 15:41
 * @Version v0.0.1
 */

@Data
public class CoursewareVersionVO {
    /**
     * 课件id
     */
    private Integer id;

    /**
     * 课件版本
     */
    private Integer version;

    /**
     * 课件id
     */
    @Schema(description = "课件版本")
    private Long coursewareId;

    /**
     * 课件名称
     * 课件版本
     */
    private String coursewareName;

    /**
     * 课件版本
     */
    @Schema(description = "课件版本")
    private Integer coursewareVersion;


    /**
     * 课节Id
     */
    @Schema(description = "课节Id")
    private Long lessonId;

    /**
     * 创建时间
     */
    @Schema(description = "创建时间")
    private LocalDateTime createTime;


    @Schema(description = "是否生成录课任务，0:不生成 1：生成")
    private Integer genRecordTask;
}
