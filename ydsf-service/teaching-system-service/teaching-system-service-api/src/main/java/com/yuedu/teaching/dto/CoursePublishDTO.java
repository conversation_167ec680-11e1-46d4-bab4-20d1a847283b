package com.yuedu.teaching.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2024/10/24
 **/

@Data
public class CoursePublishDTO implements Serializable {

    /**
     * 课程产品名称
     */
    @NotNull(message = "课程Id不能为空!")
    @Schema(description = "课程id")
    private Integer courseId;

    /**
     * 课程产品名称
     */
    @Schema(description = "课程产品名称")
    private String courseName;

    /**
     * 课程编码
     */
    @Schema(description = "课程编码")
    private String courseCode;


    /**
     * 阶段ID
     */
    @Schema(description = "所属阶段ID")
    private Integer stageId;
}
