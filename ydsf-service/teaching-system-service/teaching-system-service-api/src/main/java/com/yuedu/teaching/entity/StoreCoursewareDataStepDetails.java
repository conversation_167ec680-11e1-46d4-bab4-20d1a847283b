package com.yuedu.teaching.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import java.time.LocalDateTime;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 门店教学环节详情表
 * 
 * <AUTHOR>
 * @date 2025/08/05
 */
@TableName("store_courseware_data_step_details")
@Data
@EqualsAndHashCode(callSuper = true)
public class StoreCoursewareDataStepDetails extends Model<StoreCoursewareDataStepDetails> {
    /**
     * 主键id
     */
    @TableId(type = IdType.AUTO)
    private Integer id;

    /**
     * 原details表id
     */
    private Integer coursewareDataStepDetailsId;

    /**
     * 课件名称
     */
    private String coursewareName;

    /**
     * 关联课件ID
     */
    private Integer coursewareId;

    /**
     * 关联课件内容ID
     */
    private Integer coursewareDataId;

    /**
     * 环节ID
     */
    private Integer stepId;

    /**
     * 详细数据
     */
    private Object details;

    /**
     * 页面工具
     */
    private Object tool;

    /**
     * 教学页分类
     */
    private Integer teachingPageCategory;

    /**
     * 教学页类型
     */
    private Integer teachingPageType;

    /**
     * 教学页模版ID
     */
    private Integer pageTemplateId;

    /**
     * 门店id
     */
    private Integer storeId;

    /**
     * 校区id
     */
    private Integer schoolId;

    /**
     * 所属者(副本创建人)
     */
    private Long owner;

    /**
     * 是否删除
     */
    @TableLogic
    @TableField(fill = FieldFill.INSERT)
    private Integer delFlag;

    /**
     * 创建人
     */
    @TableField(fill = FieldFill.INSERT)
    private String createBy;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 修改人
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private String updateBy;

    /**
     * 修改时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;
}