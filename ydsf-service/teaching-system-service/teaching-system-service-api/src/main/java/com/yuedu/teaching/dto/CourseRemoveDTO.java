package com.yuedu.teaching.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2024/10/24
 **/

@Data
public class CourseRemoveDTO {
    /**
     * id
     */
    @Schema(description = "id")
    @NotNull(message = "id不能为空")
    private Integer id;


    /**
     * 课程编号
     */
    private String courseCode;
}
