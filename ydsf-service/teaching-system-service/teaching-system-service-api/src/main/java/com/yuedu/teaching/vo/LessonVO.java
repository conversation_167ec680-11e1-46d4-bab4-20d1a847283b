package com.yuedu.teaching.vo;

import com.yuedu.teaching.entity.LessonEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * @ClassName LessonVo
 * @Description 课节vo
 * <AUTHOR>
 * @Date 2024/10/29 9:48
 * @Version v0.0.1
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class LessonVO extends LessonEntity {
    /**
     * 书籍名称
     */
    private String bookName;


    /**
     * 作者
     */
    private String author;


    /**
     * 出版社
     */
    private String press;


    /**
     * 封面(全路径)
     */
    private String imgUrl;

    /**
     * 课节版本
     */
    private Integer lessonVersion;

    /**
     * id
     */
    @Schema(description = "id")
    private Integer coursewareId;

    /**
     * 课件名称
     */
    @Schema(description = "课件名称")
    private String coursewareName;

    /**
     * 关联书籍
     */
    @Schema(description = "关联书籍")
    private Integer bookId;

    /**
     * 关联课件
     */
    @Schema(description = "关联书籍版本")
    private Integer bookVersionId;

    /**
     * 课件发布状态，0:未发布 1：已发布
     */
    @Schema(description = "课件发布状态，0:未发布 1：已发布")
    private Integer coursewarePublishStatus;

    /**
     * 课件版本
     */
    @Schema(description = "课件版本")
    private Integer coursewareVersion;

    /**
     * 阿里云视频播放地址
     */
    @Schema(description="阿里云视频播放地址")
    private String aliyunPlayUrl;

    /**
     * 课程版本号
     */
    @Schema(description="课程版本号")
    private Integer courseVersion;

    /**
     * 主讲老师
     */
    @Schema(description="主讲老师")
    private String leadTeacherName;

    /**
     * 主讲老师id
     */
    @Schema(description="主讲老师id")
    private Long leadTeacherId;

}