package com.yuedu.teaching.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * @author: 张浩宇
 * @date: 2024/11/12
 **/
@Data
public class DataTemplateFileMetadataDTO {

    /**
     * 上传文件限制
     */
    @Schema(description = "上传文件限制")
    private String fileFormat;

    /**
     * 上传文件大小
     */
    @Schema(description = "上传文件大小")
    private String fileSize;

    /**
     * 是否公开，0：默认，1：公开
     */
    @Schema(description = "是否公开")
    private Integer open;


    /**
     * 允许下载，0：默认 1：下载
     */
    @Schema(description = "允许下载")
    private Integer download;

    /**
     * 上传数量，1：默认一个
     */
    @Schema(description = "上传数量")
    private Integer quantity;
}
