package com.yuedu.teaching.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * 教学环节根据模版添加
 *
 * <AUTHOR>
 * @date 2024-11-15 16:26:03
 */
@Data
public class CoursewareStepByTemplateDTO {

    /**
     * 课件ID
     */
    @Schema(description = "课件ID", requiredMode = Schema.RequiredMode.REQUIRED)
    private Integer coursewareId;

    /**
     * 课件内容Id
     */
    @Schema(description = "课件内容Id", requiredMode = Schema.RequiredMode.REQUIRED)
    private Integer coursewareDataId;

    /**
     * 关联课件模版ID
     */
    @Schema(description = "关联课件模版ID", requiredMode = Schema.RequiredMode.REQUIRED)
    private Integer dataTemplateId;


    /**
     * 教学环节模版
     */
    @Schema(description = "教学环节模版ID", requiredMode = Schema.RequiredMode.REQUIRED)
    private Integer stepTemplateId;


    /**
     * 教学环节Id
     */
    @Schema(description = "教学环节Id", requiredMode = Schema.RequiredMode.REQUIRED)
    private List<Integer> stepIds;


}
