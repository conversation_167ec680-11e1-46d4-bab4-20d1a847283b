package com.yuedu.teaching.vo;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * @ClassName BookVersionVo
 * @Description 书籍版本VO
 * <AUTHOR>
 * @Date 2024/10/30 14:57
 * @Version v0.0.1
 */

@Data
public class BookVersionVO {
    /**
     * id
     */
    @TableId(type = IdType.AUTO)
    @Schema(description = "id")
    private Integer id;

    /**
     * 书籍ID
     */
    @Schema(description = "书籍ID")
    private Integer bookId;

    /**
     * ISBN号
     */
    @Schema(description = "ISBN号")
    private String isbn;

    /**
     * 出版社
     */
    @Schema(description = "出版社")
    private String press;

    /**
     * 译者
     */
    @Schema(description = "译者")
    private String translator;

    /**
     * 来源
     */
    @Schema(description = "来源")
    private Integer source;

    /**
     * 封面图片
     */
    @Schema(description = "封面图片")
    private String cover;

    /**
     * 封面图片地址
     */
    @Schema(description = "封面图片地址")
    private String coverUrl;

    /**
     * 创建人
     */
    @Schema(description = "创建人")
    private String createBy;

    /**
     * 创建时间
     */
    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    /**
     * 修改人
     */
    @Schema(description = "修改人")
    private String updateBy;

    /**
     * 修改时间
     */
    @Schema(description = "修改时间")
    private LocalDateTime updateTime;

    /**
     * 是否删除（0为不删除，1为删除）
     */
    @TableLogic
    @Schema(description = "是否删除（0为不删除，1为删除）")
    private Integer delFlag;

    @Schema(description = "阶段ID列表")
    private List<Integer> stageId;
}
