package com.yuedu.teaching.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * 课件共用资料表 实体类
 *
 * <AUTHOR>
 * @date 2024-11-06 09:07:37
 */
@Data
@TableName("data_template")
@EqualsAndHashCode(callSuper = true)
@Schema(description = "课件共用资料表实体类")
public class DataTemplate extends Model<DataTemplate> {


    /**
     * id
     */
    @TableId(type = IdType.AUTO)
    @Schema(description = "id")
    private Integer id;

    /**
     * 资料模版名称
     */
    @Schema(description = "资料模版名称")
    private String templateName;

    /**
     * 排序
     */
    @Schema(description = "排序")
    private Integer sortOrder;

    /**
     * 上传文件限制
     */
    @Schema(description = "上传文件限制")
    private String fileMetadata;

    /**
     * 是否必须 1是 0否
     */
    @Schema(description = "是否必须 1是 0否")
    private Integer required;

    /**
     * 类型 1:编辑 2:直接上传
     */
    @Schema(description = "类型 1:编辑 2:直接上传")
    private Integer type;

    /**
     * 创建人
     */
    @TableField(fill = FieldFill.INSERT)
    @Schema(description = "创建人")
    private String createBy;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    /**
     * 修改人
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    @Schema(description = "修改人")
    private String updateBy;

    /**
     * 修改时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    @Schema(description = "修改时间")
    private LocalDateTime updateTime;

    /**
     * 是否删除
     */
    @TableLogic
    @TableField(fill = FieldFill.INSERT)
    @Schema(description = "是否删除")
    private Integer delFlag;

}
