package com.yuedu.teaching.api.feign;

import com.yuedu.ydsf.common.core.constant.ServiceNameConstants;
import com.yuedu.ydsf.common.core.util.R;
import com.yuedu.ydsf.common.feign.annotation.NoToken;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 * 课程授权门店远程服务接口
 *
 * <AUTHOR>
 * @date 2025/05/21
 */
@FeignClient(contextId = "remoteCourseAuthStoreService", value = ServiceNameConstants.TEACHING_SERVICE)
public interface RemoteCourseAuthStoreService {

    /**
     * 根据课程类型ID和门店ID查询该门店下所有授权的课程ID列表（包含历史记录）
     *
     * @param courseTypeId 课程类型ID（可选）
     * @param storeId 门店ID
     * @return 课程ID列表
     */
    @GetMapping("/courseAuthStore/inner/listAuthCourseIds")
    @NoToken
    R<List<Long>> listAuthCourseIds(@RequestParam(value = "courseTypeId", required = false) Long courseTypeId, @RequestParam("storeId") Long storeId);

    /**
     * 根据课程类型ID和门店ID查询该门店下当前生效的授权课程ID列表
     *
     * @param courseTypeId 课程类型ID（可选）
     * @param storeId 门店ID
     * @return 课程ID列表
     */
    @GetMapping("/courseAuthStore/inner/listCurrentAuthCourseIds")
    @NoToken
    R<List<Long>> listCurrentAuthCourseIds(@RequestParam(value = "courseTypeId", required = false) Long courseTypeId, @RequestParam("storeId") Long storeId);
} 