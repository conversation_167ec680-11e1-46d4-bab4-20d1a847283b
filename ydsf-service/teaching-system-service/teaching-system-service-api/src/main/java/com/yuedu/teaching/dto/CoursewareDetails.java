package com.yuedu.teaching.dto;


import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * @ClassName CourseWareDetails
 * @Description 教学页内容信息
 * <AUTHOR>
 * @Date 2024/11/6 15:39
 * @Version v0.0.1
 */
@Data
public class CoursewareDetails {


    /**
     * 教学页类型,0:单图片  1:单视频  999:其他
     */
    @Schema(description = "教学页类型,0:单视频  1:单图片  999:其他")
    private Integer type;

    /**
     * 教学页内容
     */
    private ContentDTO content;

    /**
     * 教学页动画
     */
    private AnimationDTO animation;


    /**
     * 教学页教案
     */
    private TeachingPlanDTO teachingPlan;
}
