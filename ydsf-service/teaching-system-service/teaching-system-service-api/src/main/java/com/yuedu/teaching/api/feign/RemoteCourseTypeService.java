package com.yuedu.teaching.api.feign;

import com.yuedu.teaching.dto.CourseTypeDTO;
import com.yuedu.teaching.vo.CourseTypeVO;
import com.yuedu.ydsf.common.core.constant.ServiceNameConstants;
import com.yuedu.ydsf.common.core.util.R;
import com.yuedu.ydsf.common.feign.annotation.NoToken;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;

import java.util.List;

/**
 * 课程类型 远程服务接口
 *
 * <AUTHOR>
 * @date 2025/01/17
 */
@FeignClient(contextId = "remoteCourseTypeStoreService", value = ServiceNameConstants.TEACHING_SERVICE)
public interface RemoteCourseTypeService {

    /**
     * 根据门店ID查询该门店授权过的课程类型列表
     *
     * @param storeId 门店ID
     * @return 课程类型VO列表
     */
    @GetMapping("/courseType/inner/courseTypeByStore/{storeId}")
    @NoToken
    R<List<CourseTypeVO>> getCourseTypesByStoreAuth(@PathVariable("storeId") Long storeId);

    /**
     * 查询可用的课程类型
     */
    @GetMapping("/courseType/inner/allOK")
    @NoToken
    R<List<CourseTypeDTO>> getAllOK();

    /**
     * 查询所有课程类型
     */
    @GetMapping("/courseType/inner/all")
    @NoToken
    R<List<CourseTypeDTO>> getAll();
}
