package com.yuedu.teaching.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

import java.time.LocalDateTime;

/**
 * 书籍适配阶段表
 *
 * <AUTHOR>
 * @date 2024-10-24 10:23:50
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName("book_stage")
@EqualsAndHashCode(callSuper = true)
@Schema(description = "书籍适配阶段表")
public class BookStage extends Model<BookStage> {


    /**
     * id
     */
    @TableId(type = IdType.AUTO)
    @Schema(description = "id")
    private Integer id;

    /**
     * 阶段/书单ID
     */
    @Schema(description = "阶段/书单ID")
    private Integer stageId;

    /**
     * 书籍ID
     */
    @Schema(description = "书籍ID")
    private Integer bookId;

    /**
     * 创建人
     */
    @Schema(description = "创建人")
    @TableField(fill = FieldFill.INSERT)
    private String createBy;

    /**
     * 创建时间
     */
    @Schema(description = "创建时间")
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 修改人
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    @Schema(description = "修改人")
    private String updateBy;

    /**
     * 修改时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    @Schema(description = "修改时间")
    private LocalDateTime updateTime;

    /**
     * 是否删除
     */
    @TableLogic
    @Schema(description = "是否删除")
    private Integer delFlag;
}