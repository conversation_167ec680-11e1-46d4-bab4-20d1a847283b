package com.yuedu.teaching.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import java.time.LocalDateTime;

/**
* 门店教学环节详情表
*
* <AUTHOR>
* @date  2025/08/05
*/
@Data
@Schema(description = "门店教学环节详情表展示对象")
public class StoreCoursewareDataStepDetailsVO {


    /**
     * 主键id
     */
    @Schema(description = "主键id")
    private Integer id;

    /**
     * 原details表id
     */
    @Schema(description = "原details表id")
    private Integer coursewareDataStepDetailsId;

    /**
     * 课件名称
     */
    @Schema(description = "课件名称")
    private String coursewareName;

    /**
     * 关联课件ID
     */
    @Schema(description = "关联课件ID")
    private Integer coursewareId;

    /**
     * 关联课件内容ID
     */
    @Schema(description = "关联课件内容ID")
    private Integer coursewareDataId;

    /**
     * 环节ID
     */
    @Schema(description = "环节ID")
    private Integer stepId;

    /**
     * 详细数据
     */
    @Schema(description = "详细数据")
    private Object details;

    /**
     * 页面工具
     */
    @Schema(description = "页面工具")
    private Object tool;

    /**
     * 教学页分类
     */
    @Schema(description = "教学页分类")
    private Byte teachingPageCategory;

    /**
     * 教学页类型
     */
    @Schema(description = "教学页类型")
    private Byte teachingPageType;

    /**
     * 教学页模版ID
     */
    @Schema(description = "教学页模版ID")
    private Integer pageTemplateId;

    /**
     * 门店id
     */
    @Schema(description = "门店id")
    private Integer storeId;

    /**
     * 校区id
     */
    @Schema(description = "校区id")
    private Integer schoolId;

    /**
     * 所属者(副本创建人)
     */
    @Schema(description = "所属者(副本创建人)")
    private Long owner;

    /**
     * 是否删除
     */
    @Schema(description = "是否删除")
    private Byte delFlag;

    /**
     * 创建人
     */
    @Schema(description = "创建人")
    private String createBy;

    /**
     * 创建时间
     */
    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    /**
     * 修改人
     */
    @Schema(description = "修改人")
    private String updateBy;

    /**
     * 修改时间
     */
    @Schema(description = "修改时间")
    private LocalDateTime updateTime;

}
