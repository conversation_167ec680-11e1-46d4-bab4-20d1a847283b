package com.yuedu.teaching.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

import java.time.LocalDateTime;

/**
 * 课件发布表 实体类
 *
 * <AUTHOR>
 * @date 2024-11-07 10:53:06
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName("courseware_version")
@EqualsAndHashCode(callSuper = true)
@Schema(description = "课件发布表实体类")
public class CoursewareVersion extends Model<CoursewareVersion> {


    /**
     * id
     */
    @TableId(type = IdType.AUTO)
    @Schema(description = "id")
    private Integer id;

    /**
     * 关联书籍
     */
    @Schema(description = "关联书籍")
    private Integer bookId;

    /**
     * 关联版本
     */
    @Schema(description = "关联版本")
    private Integer bookVersionId;

    /**
     * 课件名称
     */
    @Schema(description = "课件名称")
    private String coursewareName;

    /**
     * 关联课件
     */
    @Schema(description = "关联课件")
    private Integer coursewareId;

    /**
     * 创建人
     */
    @TableField(fill = FieldFill.INSERT)
    @Schema(description = "创建人")
    private String createBy;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    /**
     * 修改人
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    @Schema(description = "修改人")
    private String updateBy;

    /**
     * 修改时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    @Schema(description = "修改时间")
    private LocalDateTime updateTime;

    /**
     * 是否删除
     */
    @TableLogic
    @TableField(fill = FieldFill.INSERT)
    @Schema(description = "是否删除")
    private Integer delFlag;

    @Schema(description = "是否生成录课任务，0:不生成 1：生成")
    private Integer genRecordTask;
}
