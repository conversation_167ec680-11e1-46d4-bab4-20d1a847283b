package com.yuedu.teaching.dto;

import com.baomidou.mybatisplus.annotation.TableName;
import com.yuedu.ydsf.common.core.util.ValidGroup;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * 书名表DTO
 *
 * <AUTHOR>
 * @date 2024-10-24 08:57:33
 */
@Data
@TableName("book_name")
@Schema(description = "书名表DTO")
public class BookNameDTO {

    /**
     * 书名
     */
    @Schema(description = "书名")
    @NotBlank(groups = {ValidGroup.Insert.class}, message = "书名不能为空")
    private String title;

    /**
     * 作者
     */
    @Schema(description = "作者")
    @NotBlank(groups = {ValidGroup.Insert.class}, message = "作者不能为空")
    private String author;

    /**
     * 阶段id
     */
    @Schema(description = "阶段Id")
    @NotNull(groups = {ValidGroup.Update.class}, message = "阶段Id不能为空")
    private Integer stageId;

    /**
     * 书名id
     */
    @Schema(description = "书名Id")
    private Integer bookId;
}
