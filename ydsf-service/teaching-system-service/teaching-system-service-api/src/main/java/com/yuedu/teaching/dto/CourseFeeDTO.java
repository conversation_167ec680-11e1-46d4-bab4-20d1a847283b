package com.yuedu.teaching.dto;

import com.yuedu.ydsf.common.core.util.V_A_E;
import com.yuedu.ydsf.common.core.util.V_E;
import lombok.Data;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import jakarta.validation.constraints.NotNull;
import io.swagger.v3.oas.annotations.media.Schema;
import org.hibernate.validator.constraints.Length;

/**
* 
*
* <AUTHOR>
* @date  2025/05/22
*/
@Data
@Schema(description = "传输对象")
public class CourseFeeDTO implements Serializable {


    /**
     * 课消ID
     */
    @NotNull(groups = {V_E.class}, message = "课消ID不能为空")
    private Long id;



    /**
     * 是否删除: 0-正常;1-删除
     */
    @Schema(description = "是否删除: 0-正常;1-删除 字典类型：del_flag" ,type = "del_flag", defaultValue = "'0'")
    private Integer delFlag;

    /**
     * 创建人
     */
    @Schema(description = "创建人", defaultValue = "''")
    @Length(groups = {V_A_E.class }, max =255 ,message = "创建人长度不能大于255")
    private String createBy;

    /**
     * 创建时间
     */
    @Schema(description = "创建时间", defaultValue = "CURRENT_TIMESTAMP")
    private LocalDateTime createTime;

    /**
     * 修改人
     */
    @Schema(description = "修改人")
    @Length(groups = {V_A_E.class }, max =255 ,message = "修改人长度不能大于255")
    private String updateBy;

    /**
     * 修改时间
     */
    @Schema(description = "修改时间", defaultValue = "CURRENT_TIMESTAMP")
    private LocalDateTime updateTime;

    /**
     * 标准价格
     */
    @Schema(description = "标准价格", defaultValue = "0.00")
    private BigDecimal standardPrice;

    /**
     * 生效日期
     */
    @Schema(description = "生效日期")
    private LocalDate effectiveDate;

    /**
     * 门店ID
     */
    @Schema(description = "门店ID")
    private Long storeId;

    /**
     * 课程类型ID
     */
    @Schema(description = "课程类型ID")
    private Long courseTypeId;

    /**
     * 课程ID
     */
    @Schema(description = "课程ID")
    private Long courseId;

    /**
     * 操作类型: 0-门店设置;1-自定义
     */
    @Schema(description = "操作类型: 0-门店设置;1-自定义")
    private Integer optType;


}
