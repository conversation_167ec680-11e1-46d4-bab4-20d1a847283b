package com.yuedu.teaching.dto;


import lombok.Data;

/**
 * 临时凭证
 *
 * <AUTHOR>
 */
@Data
public class OssSts {

    /**
     * 端点
     */
    private String endpoint;

    /**
     * 临时key
     */
    private String accessKeyId;

    /**
     * 临时秘钥
     */
    private String accessKeySecret;

    /**
     * 临时token
     */
    private String securityToken;


    /**
     * 有效时长
     */
    private Long durationSeconds;


    /**
     * 上传路径
     */
    private String uploadPath;

    /**
     * bucker地址
     */
    private String bucket;

    /**
     * url
     */
    private String url;

    /**
     * url
     */
    private String path;
}
