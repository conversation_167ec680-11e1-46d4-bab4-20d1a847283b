package com.yuedu.teaching.constant.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 站点类型
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum SiteEnum {
    /**
     * 后端
     */
    ADMIN("admin", "后端"),

    /**
     * 客户端
     */
    CLIENT("client", "客户端"),

    /**
     * 教室
     */
    CLASSROOM("classRoom", "教室端"),

    /**
     * 讲师
     */
    LECTURER("lecturer", "讲师端");

    private final String code;

    private final String desc;
}
