package com.yuedu.teaching.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * @ClassName CourseDisableDTO
 * @Description 课程是否停用DTO
 * <AUTHOR>
 * @Date 2024/12/17 14:38
 * @Version v0.0.1
 */

@Data
public class CourseDisableDTO {
    /**
     * 课程id
     */
    @Schema(description = "课程id")
    private Integer id;

    /**
     * 是否停用(0未停用，1停用)
     */
    @Schema(description = "是否停用(0未停用，1停用)")
    private Integer disable;
}
