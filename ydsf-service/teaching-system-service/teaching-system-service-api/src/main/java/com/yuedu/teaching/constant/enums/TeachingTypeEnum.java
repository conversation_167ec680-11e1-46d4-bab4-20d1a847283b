package com.yuedu.teaching.constant.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * teaching类型枚举
 *
 * <AUTHOR>
 * @date 2024/10/25
 **/
@Getter
@AllArgsConstructor
public enum TeachingTypeEnum {

    /**
     * 书名
     */
    BOOK_NAME(1, "书名"),
    /**
     * 版本
     */
    BOOK_VERSION(2, "版本"),
    /**
     * 课程
     */
    COURSE(3, "课程"),
    /**
     * 课节
     */
    LESSON(4, "课节");

    private final Integer code;

    private final String desc;

}
