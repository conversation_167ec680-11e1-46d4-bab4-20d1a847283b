package com.yuedu.teaching.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * 教学环节表
 *
 * <AUTHOR>
 * @date 2024-11-05 17:30:36
 */
@Data
@Schema(description = "教学环节表VO")
public class CoursewareStepVO {
    /**
     * id
     */
    @Schema(description = "id")
    private Integer id;

    /**
     * 课件内容Id
     */
    @Schema(description = "课件内容Id")
    private Integer coursewareDataId;

    /**
     * 关联课件ID
     */
    @Schema(description = "关联课件ID")
    private Integer coursewareId;

    /**
     * 课件名称
     */
    @Schema(description = "课件名称")
    private String stepName;

    /**
     * 父ID
     */
    @Schema(description = "父ID")
    private Integer stepParent;

    /**
     * 排序
     */
    @Schema(description = "排序")
    private Integer stepOrder;

    /**
     * 环节类型，1是环节，2是页面
     */
    @Schema(description = "环节类型，1是环节，2是页面")
    private Integer type;

    @Schema(description = "页面")
    private List<CoursewareStepVO> details;

    @Schema(description = "页面详情ID")
    private Integer dataStepDetailsId;

}
