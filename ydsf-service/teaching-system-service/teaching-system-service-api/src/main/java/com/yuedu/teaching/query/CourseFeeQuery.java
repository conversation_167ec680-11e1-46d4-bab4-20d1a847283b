package com.yuedu.teaching.query;

import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import io.swagger.v3.oas.annotations.media.Schema;


/**
* 
*
* <AUTHOR>
* @date  2025/05/22
*/
@Data
@Schema(description = "查询对象")
public class CourseFeeQuery {

    /**
     * 课消ID
     */
    @Schema(description = "课消ID")
    private Long id;

    /**
     * 是否删除: 0-正常;1-删除
     */
    @Schema(description = "是否删除: 0-正常;1-删除 字典类型：del_flag" ,type = "del_flag")
    private Integer delFlag;

    /**
     * 创建人
     */
    @Schema(description = "创建人")
    private String createBy;

    /**
     * 创建时间
     */
    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    /**
     * 修改人
     */
    @Schema(description = "修改人")
    private String updateBy;

    /**
     * 修改时间
     */
    @Schema(description = "修改时间")
    private LocalDateTime updateTime;

    /**
     * 标准价格
     */
    @Schema(description = "标准价格")
    private BigDecimal standardPrice;

    /**
     * 生效日期
     */
    @Schema(description = "生效日期")
    private LocalDate effectiveDate;

    /**
     * 门店ID
     */
    @Schema(description = "门店ID")
    private Long storeId;

    /**
     * 课程类型ID
     */
    @Schema(description = "课程类型ID")
    private Long courseTypeId;

    /**
     * 课程ID
     */
    @Schema(description = "课程ID")
    private Long courseId;

}
