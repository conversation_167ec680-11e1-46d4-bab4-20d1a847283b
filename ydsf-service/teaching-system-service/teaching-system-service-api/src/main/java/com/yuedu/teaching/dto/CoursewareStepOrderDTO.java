package com.yuedu.teaching.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * 教学环节排序
 * <AUTHOR>
 * @date 2024/11/18 08:50
 */
@Data
public class CoursewareStepOrderDTO {

    /**
     * 课件内容Id
     */
    @Schema(description = "课件内容Id", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "课件内容Id不能为null")
    private Integer coursewareDataId;

    /**
     * 关联课件ID
     */
    @Schema(description = "关联课件ID", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "关联课件ID")
    private Integer coursewareId;


    /**
     * 数据列表
     */
    @Schema(description = "关联课件ID", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "关联课件数组不完整")
    @Valid
    private List<Info> infoList;


    @Data
    @Accessors(chain = true)
    public static class Info{
        /**
         * id
         */
        @Schema(description = "id", requiredMode = Schema.RequiredMode.REQUIRED)
        @NotNull(message = "id不能为null")
        private Integer id;

        /**
         * 父id
         */
        @Schema(description = "stepParent", requiredMode = Schema.RequiredMode.REQUIRED)
        @NotNull(message = "stepParent不能为null")
        private Integer stepParent;

        /**
         * 排序
         */
        @Schema(description = "排序")
        @NotNull(message = "排序不能为空")
        private Integer stepOrder;
    }

}
