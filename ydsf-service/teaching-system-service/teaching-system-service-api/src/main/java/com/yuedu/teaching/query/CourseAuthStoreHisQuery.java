package com.yuedu.teaching.query;

import lombok.Data;
import java.time.LocalDateTime;
import io.swagger.v3.oas.annotations.media.Schema;


/**
* 课程授权门店历史表
*
* <AUTHOR>
* @date  2025/05/21
*/
@Data
@Schema(description = "课程授权门店历史表查询对象")
public class CourseAuthStoreHisQuery {

    /**
     * 主键id
     */
    @Schema(description = "主键id")
    private Long id;

    /**
     * 课程表id
     */
    @Schema(description = "课程表id")
    private Long courseId;

    /**
     * 门店id
     */
    @Schema(description = "门店id")
    private Long storeId;

    /**
     * 授权状态:0-正常授权;1-取消授权
     */
    @Schema(description = "授权状态:0-正常授权;1-取消授权")
    private Integer authStatus;

    /**
     * 是否删除
     */
    @Schema(description = "是否删除")
    private Byte delFlag;

    /**
     * 创建人
     */
    @Schema(description = "创建人")
    private String createBy;

    /**
     * 创建时间
     */
    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    /**
     * 修改人
     */
    @Schema(description = "修改人")
    private String updateBy;

    /**
     * 修改时间
     */
    @Schema(description = "修改时间")
    private LocalDateTime updateTime;

}
