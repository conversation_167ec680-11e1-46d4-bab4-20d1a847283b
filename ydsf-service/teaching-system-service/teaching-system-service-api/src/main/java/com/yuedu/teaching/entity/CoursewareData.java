package com.yuedu.teaching.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.yuedu.teaching.valid.CoursewareDataPubValidGroup;
import com.yuedu.teaching.valid.CoursewareDataValidGroup;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.*;

import java.time.LocalDateTime;

/**
 * 资料表 实体类
 *
 * <AUTHOR>
 * @date 2024-11-06 08:40:10
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName("courseware_data")
@EqualsAndHashCode(callSuper = true)
@Schema(description = "资料表实体类")
public class CoursewareData extends Model<CoursewareData> {


    /**
     * id
     */
    @TableId(type = IdType.AUTO)
    @Schema(description = "id")
    private Integer id;

    /**
     * 课件名称
     */
    @Schema(description = "课件名称")
    private String coursewareName;

    /**
     * 关联课件ID
     */
    @Schema(description = "关联课件ID")
    @NotNull(groups = {CoursewareDataPubValidGroup.PublishData.class, CoursewareDataValidGroup.ListCoursewareData.class})
    private Integer coursewareId;

    /**
     * 关联课件模版ID
     */
    @Schema(description = "关联课件模版ID")
    private Integer dataTemplateId;

    /**
     * 详细数据
     */
    @Schema(description = "详细数据")
    private String details;

    /**
     * 发布状态，0:不可发布 1：可发布
     */
    @Schema(description = "发布状态，0:不可发布 1：可发布")
    private Integer canPublish;

    /**
     * 类型 课件类型 与课件模版一致
     */
    @Schema(description = "类型 课件类型 与课件模版一致")
    private Integer type;

    /**
     * 是否公开，0：默认，1：不公开
     */
    @Schema(description = "是否公开")
    private Integer open;


    /**
     * 允许下载，0：默认 1：不下载
     */
    @Schema(description = "允许下载")
    private Integer download;

    /**
     * 创建人
     */
    @TableField(fill = FieldFill.INSERT)
    @Schema(description = "创建人")
    private String createBy;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    /**
     * 修改人
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    @Schema(description = "修改人")
    private String updateBy;

    /**
     * 修改时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    @Schema(description = "修改时间")
    private LocalDateTime updateTime;

    /**
     * 是否删除
     */
    @TableLogic
    @TableField(fill = FieldFill.INSERT)
    @Schema(description = "是否删除")
    private Integer delFlag;
}
