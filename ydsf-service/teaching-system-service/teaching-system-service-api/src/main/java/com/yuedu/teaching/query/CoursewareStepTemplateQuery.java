package com.yuedu.teaching.query;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * 教学环节模版表 实体类
 *
 * <AUTHOR>
 * @date 2024-11-05 17:32:03
 */
@Data
public class CoursewareStepTemplateQuery {
    /**
     * id
     */
    @Schema(description = "id", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "模版id不能为空")
    private Integer id;
}
