package com.yuedu.teaching.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import java.time.LocalDateTime;

/**
 * 教学页模版表
 *
 * <AUTHOR>
 * @date 2024-12-11 13:38:40
 */
@Data
@TableName("player_config")
@EqualsAndHashCode(callSuper = true)
@Schema(description = "教学页模版表")
public class PlayerConfig extends Model<PlayerConfig> {

 
	/**
	* id
	*/
    @TableId(type = IdType.ASSIGN_ID)
    @Schema(description="id")
    private Integer id;

	/**
	* 客户端 1 主讲 2教室
	*/
    @Schema(description="客户端 1 主讲 2教室")
    private Integer client;

	/**
	* 课程类型 1试听 2 常规
	*/
    @Schema(description="课程类型 1试听 2 常规")
    private Integer courseType;

	/**
	* 教学页模版名称
	*/
    @Schema(description="教学页模版名称")
    private String stepName;

	/**
	* 环节类型 课前 课后 课中
	*/
    @Schema(description="环节类型 课前 课后 课中")
    private String stepType;

	/**
	* 下一页类型 1直播 2点播 默认 0
	*/
    @Schema(description="下一页类型 1直播 2点播 默认 0")
    private Integer nextPageType;

	/**
	* 行为 1 我要上课 2 我要练课
	*/
    @Schema(description="行为 1 我要上课 2 我要练课")
    private Integer act;

	/**
	* 数据类型 1课件
	*/
    @Schema(description="数据类型 1课件")
    private Integer dataType;

	/**
	* 配置页信息
	*/
    @Schema(description="配置页信息")
    private String attr;

	/**
	* 备注
	*/
    @Schema(description="备注")
    private String remark;

	/**
	* 是否启用，1 启用 0 禁用
	*/
    @Schema(description="是否启用，1 启用 0 禁用")
    private Integer sort;

	/**
	* 创建人
	*/
	@TableField(fill = FieldFill.INSERT)
    @Schema(description="创建人")
    private String createBy;

	/**
	* 创建时间
	*/
	@TableField(fill = FieldFill.INSERT)
    @Schema(description="创建时间")
    private LocalDateTime createTime;

	/**
	* 修改人
	*/
	@TableField(fill = FieldFill.INSERT_UPDATE)
    @Schema(description="修改人")
    private String updateBy;

	/**
	* 修改时间
	*/
	@TableField(fill = FieldFill.INSERT_UPDATE)
    @Schema(description="修改时间")
    private LocalDateTime updateTime;

	/**
	* 是否删除
	*/
    @TableLogic
	@TableField(fill = FieldFill.INSERT)
    @Schema(description="是否删除")
    private Integer delFlag;
}