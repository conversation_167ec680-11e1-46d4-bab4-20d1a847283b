package com.yuedu.teaching.dto;

import com.yuedu.ydsf.common.core.util.V_A_E;
import com.yuedu.ydsf.common.core.util.ValidGroup;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.util.List;

/**
 * @ClassName BookVersionDTO
 * @Description 书籍版本DTO
 * <AUTHOR>
 * @Date 2024/10/24 09:06
 * @Version v0.0.1
 */

@Data
public class BookVersionDTO {
    /**
     * ISBN号
     */
    @Schema(description = "ISBN号")
    @NotBlank(groups = {V_A_E.class}, message = "ISBN号不能为空")
    private String isbn;

    /**
     * 出版社
     */
    @Schema(description = "出版社")
    @NotBlank(groups = {V_A_E.class}, message = "出版社不能为空")
    private String press;

    /**
     * 译者
     */
    @Schema(description = "译者")
    private String translator;

    /**
     * 阶段ID
     */
    @Schema(description = "阶段ID")
    @NotEmpty(groups = {V_A_E.class}, message = "阶段ID不能为空")
    private List<Integer> stageId;

    /**
     * 封面图片
     */
    @Schema(description = "封面图片")
    @NotBlank(groups = {V_A_E.class}, message = "封面图片不能为空")
    private String cover;

    /**
     * 来源
     */
    @Schema(description = "来源")
    @NotNull(groups = {V_A_E.class}, message = "来源不能为空")
    private Integer source;

    /**
     * 书籍ID
     */
    @Schema(description = "书籍ID")
    @NotNull(groups = {V_A_E.class, ValidGroup.Update.class}, message = "书籍ID不能为空")
    private Integer bookId;

    /**
     * 书籍版本id
     */
    @Schema(description = "书籍版本id")
    @NotNull(groups = {ValidGroup.Update.class}, message = "书籍版本ID不能为空")
    private Integer bookVersionId;
}
