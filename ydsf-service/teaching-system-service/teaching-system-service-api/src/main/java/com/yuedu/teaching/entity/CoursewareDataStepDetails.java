package com.yuedu.teaching.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

import java.time.LocalDateTime;


/**
 * @ClassName CoursewareDataStepDetailsEntity
 * @Description 资料数据和教学环节详情表
 * <AUTHOR>
 * @Date 2024/11/04 9:27
 * @Version v0.0.1
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName("courseware_data_step_details")
@EqualsAndHashCode(callSuper = true)
@Schema(description = "资料数据和教学环节详情表")
public class CoursewareDataStepDetails extends Model<CoursewareDataStepDetails> {

    /**
     * id
     */
    @TableId(type = IdType.AUTO)
    @Schema(description = "id")
    private Integer id;


    /**
     * 关联课件ID
     */
    @Schema(description = "关联课件ID")
    private Integer coursewareId;

    /**
     * 关联课件内容ID
     */
    @Schema(description = "关联课件内容ID")
    private Integer coursewareDataId;

    /**
     * 教学页所属环节ID
     */
    @Schema(description = "教学页所属环节ID")
    private Integer stepId;

    /**
     * 详细数据
     */
    @Schema(description = "详细数据")
    private String details;

    /**
     * teaching_page_template表的Id
     */
    @Schema(description = "teaching_page_template表的Id")
    private Integer pageTemplateId;


    /**
     * 教学页分类
     */
    @Schema(description = "教学页分类")
    private Integer teachingPageCategory;

    /**
     * 教学页类型
     */
    @Schema(description = "教学页类型")
    private Integer teachingPageType;

    /**
     * 创建人
     */
    @TableField(fill = FieldFill.INSERT)
    @Schema(description = "创建人")
    private String createBy;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    /**
     * 修改人
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    @Schema(description = "修改人")
    private String updateBy;

    /**
     * 修改时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    @Schema(description = "修改时间")
    private LocalDateTime updateTime;

    /**
     * 是否删除
     */
    @TableLogic
    @TableField(fill = FieldFill.INSERT)
    @Schema(description = "是否删除")
    private Integer delFlag;

    /**
     * 工具
     */
    @Schema(description = "工具")
    private String tool;
}