package com.yuedu.teaching.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * 文件上传
 *
 * <AUTHOR>
 * @date 2024-10-29 09:34:33
 */
@Data
@Schema(description = "文件上传DTO")
public class FileUploadDTO {

    /**
     * 服务名
     */
    @Schema(description = "服务名")
    @NotBlank(message = "服务名不能为空")
    private String serviceName;

    /**
     * 模块
     */
    @Schema(description = "模块")
    @NotBlank(message = "模块不能为空")
    private String module;

    /**
     * 后缀
     */
    @Schema(description = "后缀")
    @NotNull(message = "文件后缀不能为空")
    private String suffix;

    /**
     * content-md5
     */
    @Schema(description = "content-md5")
    private String contentMd5;

    /**
     * fileName
     */
    @Schema(description = "自定义文件名称")
    private String fileName;
}
