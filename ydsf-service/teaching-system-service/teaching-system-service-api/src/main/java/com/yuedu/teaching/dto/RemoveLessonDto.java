package com.yuedu.teaching.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;


/**
 * @ClassName LessonEntity
 * @Description 课程章节表
 * <AUTHOR>
 * @Date 2024/10/24 9:48
 * @Version v0.0.1
 */
@Data
@Schema(description = "删除课程章节dto")
public class RemoveLessonDto {


    /**
     * id
     */
    @Schema(description = "课节Id", requiredMode = Schema.RequiredMode.REQUIRED)
    private Integer id;


    /**
     * courseId
     */
    @Schema(description = "课程Id", requiredMode = Schema.RequiredMode.REQUIRED)
    private Long courseId;
}