package com.yuedu.teaching.constant.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * @ClassName CoursewareEnum
 * @Description 课件枚举类
 * <AUTHOR>
 * @Date 2024/11/6 10:27
 * @Version v0.0.1
 */

@Getter
@AllArgsConstructor
public enum CoursewareEnum {
    /**
     * 逻辑删除标记，1为已删除
     */
    IS_PUBLISHED(1, "已发布"),

    /**
     * 逻辑删除标记，0为未删除
     */
    NOT_PUBLISHED(0, "未发布");

    private final int code;

    private final String desc;
}
