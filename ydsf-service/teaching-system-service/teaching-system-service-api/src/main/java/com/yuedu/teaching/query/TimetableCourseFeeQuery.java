package com.yuedu.teaching.query;

import com.yuedu.teaching.dto.CourseFeePairDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Map;

@Data
@Schema(description = "课次课时费查询对象")
public class TimetableCourseFeeQuery {
    /**
     * 门店ID
     */
    @Schema(description = "门店ID", requiredMode = Schema.RequiredMode.REQUIRED)
    private Long storeId;

    /**
     * 课次信息，key为课次ID，value为课程ID和上课日期的Pair
     */
    @Schema(description = "课次信息，key为课次ID，value为课程ID和上课日期的Pair", requiredMode = Schema.RequiredMode.REQUIRED)
    private Map<Long, CourseFeePairDTO> timetableIdCourseMap;
}
