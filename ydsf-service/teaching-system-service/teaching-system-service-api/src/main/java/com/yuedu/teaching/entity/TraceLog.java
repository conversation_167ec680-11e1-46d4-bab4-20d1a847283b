package com.yuedu.teaching.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * 操作留痕表 实体类
 *
 * <AUTHOR>
 * @date 2024-10-25 11:31:28
 */
@Data
@TableName("trace_log")
@EqualsAndHashCode(callSuper = true)
@Schema(description = "操作留痕表实体类")
public class TraceLog extends Model<TraceLog> {


    /**
     * id
     */
    @TableId(type = IdType.AUTO)
    @Schema(description = "id")
    private Integer id;

    /**
     * 日志所属类目 1:书名 2:课程 3:课程 4:课件
     */
    @Schema(description = "日志所属类目 1:书名 2:版本 3:课程 4:课节")
    private Integer type;

    /**
     * 操作类型 1 添加 2修改 3删除 等
     */
    @Schema(description = "操作类型 1 添加 2修改 3删除 等")
    private Integer operate;

    /**
     * 操作前
     */
    @Schema(description = "操作前")
    private String oldData;

    /**
     * 操作后
     */
    @Schema(description = "操作后")
    private String newData;

    /**
     * 创建人
     */
    @TableField(fill = FieldFill.INSERT)
    @Schema(description = "创建人")
    private String createBy;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    /**
     * 修改人
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    @Schema(description = "修改人")
    private String updateBy;

    /**
     * 修改时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    @Schema(description = "修改时间")
    private LocalDateTime updateTime;

    /**
     * 是否删除
     */
    @TableLogic
    @TableField(fill = FieldFill.INSERT)
    @Schema(description = "是否删除")
    private Integer delFlag;
}
