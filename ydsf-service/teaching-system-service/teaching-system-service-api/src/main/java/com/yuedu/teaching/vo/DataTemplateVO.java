package com.yuedu.teaching.vo;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * 资料表 实体类
 *
 * <AUTHOR>
 * @date 2024-11-06 08:40:10
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DataTemplateVO {


    /**
     * id
     */
    @TableId(type = IdType.AUTO)
    @Schema(description = "id")
    private Integer id;

    /**
     * 资料模版名称
     */
    @Schema(description = "资料模版名称")
    private String templateName;

    /**
     * 课件名称
     */
    @Schema(description = "课件名称")
    private String coursewareName;

    /**
     * 关联课件ID
     */
    @Schema(description = "关联课件ID")
    private Integer coursewareId;

    /**
     * 关联课件模版ID
     */
    @Schema(description = "关联课件模版ID")
    private Integer dataTemplateId;

    /**
     * 详细数据
     */
    @Schema(description = "详细数据")
    private String details;

    /**
     * 文件名
     */
    @Schema(description = "文件名")
    private String fileName;

    /**
     * 完整图片路径
     */
    @Schema(description = "完整图片路径")
    private String url;

    /**
     * 发布状态，0:不可发布 1：可发布
     */
    @Schema(description = "发布状态，0:不可发布 1：可发布")
    private Integer canPublish;

    /**
     * 类型 课件类型 与课件模版一致
     */
    @Schema(description = "类型 课件类型 与课件模版一致")
    private Integer type;

    /**
     * 是否公开，0：默认，1：公开
     */
    @Schema(description = "是否公开")
    private Integer open;


    /**
     * 允许下载，0：默认 1：下载
     */
    @Schema(description = "允许下载")
    private Integer download;


    /**
     * 上传数量 默认1
     */
    @Schema(description = "上传数量")
    private Integer quantity;

    /**
     * 上传文件限制
     */
    @Schema(description = "上传文件限制")
    private String fileFormat;

    /**
     * 上传文件大小
     */
    @Schema(description = "上传文件大小")
    private String fileSize;

    /**
     * 是否必须 1是 0否
     */
    @Schema(description = "是否必须 1是 0否")
    private Integer required;

    /**
     * 创建人
     */
    @TableField(fill = FieldFill.INSERT)
    @Schema(description = "创建人")
    private String createBy;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    /**
     * 修改人
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    @Schema(description = "修改人")
    private String updateBy;

    /**
     * 修改时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    @Schema(description = "修改时间")
    private LocalDateTime updateTime;

    /**
     * 文件数组
     */
    @Schema(description = "数组")
    private String uploadPath;

}
