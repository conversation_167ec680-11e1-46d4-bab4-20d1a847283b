package com.yuedu.teaching.dto;

/**
 * @ClassName CoursewareVersionDTO
 * @Description 课件版本DTO
 * <AUTHOR>
 * @Date 2024/12/3 15:51
 * @Version v0.0.1
 */

import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

@Data
public class CoursewareVersionDTO {
    /**
     * 版本idLIst
     */
    private List<Integer> versionList;

    /**
     * 课件idList
     */
    private List<Long> coursewareIdList;

    /**
     * 课件id
     */
    private Long coursewareId;

    /**
     * 最近一下录课时间
     */
    private LocalDateTime latestRecordTaskTime;
}
