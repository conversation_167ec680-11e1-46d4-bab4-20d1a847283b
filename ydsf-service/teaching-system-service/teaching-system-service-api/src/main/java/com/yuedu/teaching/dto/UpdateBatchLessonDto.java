package com.yuedu.teaching.dto;

import com.yuedu.teaching.entity.LessonEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;


/**
 * @ClassName LessonEntity
 * @Description 课程章节表
 * <AUTHOR>
 * @Date 2024/10/24 9:48
 * @Version v0.0.1
 */
@Data
@Schema(description = "批量更新课程章节dto")
public class UpdateBatchLessonDto {


    /**
     * id
     */
    @Schema(description = "课程id", requiredMode = Schema.RequiredMode.REQUIRED)
    private Integer courseId;


    @Schema(description = "要调整的课节列表", requiredMode = Schema.RequiredMode.REQUIRED)
    private List<LessonEntity> entityList;
}