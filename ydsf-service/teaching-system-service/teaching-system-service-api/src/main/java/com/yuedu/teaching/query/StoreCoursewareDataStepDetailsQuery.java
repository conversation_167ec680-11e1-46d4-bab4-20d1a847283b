package com.yuedu.teaching.query;

import com.yuedu.teaching.valid.StoreCoursewareDataStepDetailsValidGroup;
import lombok.Data;
import java.time.LocalDateTime;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Positive;


/**
* 门店教学环节详情表
*
* <AUTHOR>
* @date  2025/08/05
*/
@Data
@Schema(description = "门店教学环节详情表查询对象")
public class StoreCoursewareDataStepDetailsQuery {

    /**
     * 主键id
     */
    @Schema(description = "主键id")
    private Integer id;

    /**
     * 原details表id
     */
    @Schema(description = "原details表id")
    private Integer coursewareDataStepDetailsId;

    /**
     * 课件名称
     */
    @Schema(description = "课件名称")
    private String coursewareName;

    /**
     * 关联课件ID
     */
    @NotNull(groups = StoreCoursewareDataStepDetailsValidGroup.StoreDataStepDetails.class, message = "课件ID不能为空")
    @Positive(message = "课件ID错误")
    @Schema(description = "关联课件ID", requiredMode = Schema.RequiredMode.REQUIRED)
    private Integer coursewareId;

    /**
     * 关联课件内容ID
     */
    @NotNull(groups = StoreCoursewareDataStepDetailsValidGroup.StoreDataStepDetails.class, message = "课件内容ID不能为空")
    @Positive(message = "课件内容ID错误")
    @Schema(description = "关联课件内容ID", requiredMode = Schema.RequiredMode.REQUIRED)
    private Integer coursewareDataId;

    /**
     * 环节ID
     */
    @NotNull(groups = StoreCoursewareDataStepDetailsValidGroup.StoreDataStepDetails.class, message = "教学页所属环节ID不能为空")
    @Positive(message = "教学页所属环节ID错误")
    @Schema(description = "环节ID", requiredMode = Schema.RequiredMode.REQUIRED)
    private Integer stepId;

    /**
     * 详细数据
     */
    @Schema(description = "详细数据")
    private Object details;

    /**
     * 页面工具
     */
    @Schema(description = "页面工具")
    private Object tool;

    /**
     * 教学页分类
     */
    @Schema(description = "教学页分类")
    private Byte teachingPageCategory;

    /**
     * 教学页类型
     */
    @Schema(description = "教学页类型")
    private Byte teachingPageType;

    /**
     * 教学页模版ID
     */
    @Schema(description = "教学页模版ID")
    private Integer pageTemplateId;

    /**
     * 门店id
     */
    @Schema(description = "门店id")
    private Integer storeId;

    /**
     * 校区id
     */
    @Schema(description = "校区id")
    private Integer schoolId;

    /**
     * 所属者(副本创建人)
     */
    @Schema(description = "所属者(副本创建人)")
    private Long owner;

    /**
     * 是否删除
     */
    @Schema(description = "是否删除")
    private Byte delFlag;

    /**
     * 创建人
     */
    @Schema(description = "创建人")
    private String createBy;

    /**
     * 创建时间
     */
    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    /**
     * 修改人
     */
    @Schema(description = "修改人")
    private String updateBy;

    /**
     * 修改时间
     */
    @Schema(description = "修改时间")
    private LocalDateTime updateTime;

}
