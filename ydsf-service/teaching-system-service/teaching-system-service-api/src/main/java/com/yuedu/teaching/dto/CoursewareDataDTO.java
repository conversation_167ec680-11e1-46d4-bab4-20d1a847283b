package com.yuedu.teaching.dto;

import com.yuedu.teaching.valid.CoursewareDataValidGroup;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @author: 张浩宇
 * @date: 2024/11/07
 **/
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CoursewareDataDTO {


    /**
     * id
     */
    @Schema(description = "id")
    @NotNull(groups = {CoursewareDataValidGroup.UpdateCoursewareData.class, CoursewareDataValidGroup.SetPublishStatus.class})
    private Integer id;

    /**
     * 关联课件ID
     */
    @Schema(description = "关联课件ID")
    @NotNull(groups = {CoursewareDataValidGroup.InsertCoursewareData.class})
    private Integer coursewareId;

    /**
     * 关联课件模版ID
     */
    @Schema(description = "关联课件模版ID")
    @NotNull(groups = {CoursewareDataValidGroup.InsertCoursewareData.class})
    private Integer dataTemplateId;

    /**
     * 发布状态
     */
    @Schema(description = "发布状态")
    private Integer canPublish;

    /**
     * 是否开放给门店
     */
    @Schema(description = "是否开放给门店")
    private Integer open;

    /**
     * 是否允许下载
     */
    @Schema(description = "是否允许下载")
    private Integer download;

    /**
     * 图片,word,pdf等路径 array
     */
    @Schema(description = "图片,word,pdf等路径列表")
    private String details;
}
