package com.yuedu.teaching.vo;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * @author: shaozz
 * @date: 20250509
 **/
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CoursewareDataUploadVO {
    /**
     * 完整图片路径
     */
    @Schema(description = "完整图片路径")
    private String url;

        /**
     * 图片,word,pdf等路径
     */
    @Schema(description = "图片,word,pdf等路径")
    private String path;

    /**
     * 文件名
     */
    @Schema(description = "图片,word,pdf等路径")
    private String fileName;
}
