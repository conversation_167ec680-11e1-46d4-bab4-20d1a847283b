package com.yuedu.teaching.constant;

/**
 * 课程常量
 *
 * <AUTHOR>
 * @date 2024/10/24
 **/

public class TeachingConstant {
    /**
     * 课程发布状态----未发布
     */
    public static final Integer COURSE_PUBLISH_STATUS_UNPUBLISH = 0;


    /**
     * 课程发布状态----已发布
     */
    public static final Integer COURSE_PUBLISH_STATUS_PUBLISH = 1;


    /**
     * 课节发布状态----未发布
     */
    public static final Integer LESSON_PUBLISH_STATUS_UNPUBLISH = 0;


    /**
     * 课节发布状态----已发布
     */
    public static final Integer LESSON_PUBLISH_STATUS_PUBLISH = 1;

    /**
     * 课节是否可发布----不可发布
     */
    public static final Integer LESSON_CANNOT_PUBLISH = 0;


    /**
     * 课节是否可发布----可发布
     */
    public static final Integer LESSON_CAN_PUBLISH = 1;

    /**
     * 课件不可发布
     */
    public static final int COURSE_WARE_CANNOT_PUBLISH = 0;


    /**
     * 课程类型
     */
    public static final String COURSE_TYPE = "R";


    /**
     * 初始序列号
     */
    public static final Integer SERIAL_NUMBER = 1;


    /**
     * 课程编号
     */
    public static final String COURSE_SERIAL_NUMBER = "course_serial_number";

    /**
     * 课程id
     */
    public static final String COURSE_ID = "course_id";

    /**
     * 书籍版本数量更新数量
     */
    public static final Integer BOOK_VERSION_COUNT_UPDATE_NUM = 1;


    /**
     * 添加课节
     */
    public static final int INSERT_LESSON_COUNT = 1;


    /**
     * 删除课节
     */
    public static final int REMOVE_LESSON_COUNT = -1;


    /**
     * 下一个课节顺序
     */
    public static final int NEXT_LESSON_ORDER = 1;

    /**
     * 阶段数为0
     */
    public static final int ZERO_STAGE_COUNT = 0;

    /**
     * 课程模板必填项
     */
    public static final int DATA_TEMPLATE_REQUIRED = 1;

    /**
     * 课程资料不可发布
     */
    public static final int COURSEWARE_DATA_CANNOT_PUBLISH = 0;

    /**
     * 课程资料可发布
     */
    public static final int COURSEWARE_DATA_CAN_PUBLISH = 1;

    /**
     * 课程资料发布状态
     */
    public static final int COURSEWARE_PUBLISH_STATUS = 1;

    /**
     * 课件环节类型----环节
     */
    public static final int COURSEWARE_STEP_TYPE_LINK = 1;

    /**
     * 课件环节类型----页面
     */
    public static final int COURSEWARE_STEP_TYPE_PAGE = 2;


    /**
     * 教学页模板是否启用----启用
     */
    public static final int TEACHING_PAGE_TEMPLATE_ENABLED = 1;

    /**
     * 教学页模板是否启用----禁用
     */
    public static final int TEACHING_PAGE_TEMPLATE_DISABLED = 0;

    /**
     * 课程是否停用----停用
     */
    public static final int COURSE_IS_DISABLE = 1;

    /**
     * 课程是否停用----未停用
     */
    public static final int COURSE_NOT_DISABLE = 0;

    /**
     * 有声绘本模版ID
     */
    public static final String PICTURE_BOOK_TEMPLATE_ID = "PICTURE_BOOK_TEMPLATE_ID";

    /**
     * 默认环节名称
     */
    public static final String STEP_NAME_LINK = "STEP_NAME_LINK";

    /**
     * 默认教学页ID
     */
    public static final String PAGE_TEMPLATE_ID = "PAGE_TEMPLATE_ID";

    /**
     * 课程资料类型-可否开放给门店
     */
    public static final int COURSEWARE_DATA_OPEN_STORE = 1;
    /**
     * 课程资料类型-可否下载
     */
    public static final int COURSEWARE_DATA_DAWNLOAD_STORE = 2;

    /**
     * 资料包 第一节课件ID
     */
    public static final int DATA_TEMPLATE_FIRST_COURSEWARE = 1;

    /**
     * 资料包 公开状态
     */
    public static final int COURSEWARE_DATA_OPEN_YES = 1;

    /**
     * limit 1
     */
    public static final String LIMIT_ONE_SQL = "limit 1";

    /**
     * 副本课件
     */
    public static final String COPY_COURSE_WARE_REMAK = "副本课件";

}
