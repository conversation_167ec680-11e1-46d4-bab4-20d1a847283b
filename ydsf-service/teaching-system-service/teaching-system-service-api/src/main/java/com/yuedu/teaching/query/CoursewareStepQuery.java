package com.yuedu.teaching.query;

import com.yuedu.teaching.valid.CoursewareStepValidGroup;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * 教学环节 Query
 *
 * <AUTHOR>
 * @date 2024-11-05 17:30:36
 */
@Data
public class CoursewareStepQuery {
    /**
     * id
     */
    @Schema(description = "id")
    private Integer id;

    /**
     * 课件内容Id
     */
    @Schema(description = "课件内容Id")
    @NotNull(groups = { CoursewareStepValidGroup.ListCoursewareStep.class }, message = "课件内容Id不能为null")
    private Integer coursewareDataId;

    /**
     * 关联课件ID
     */
    @Schema(description = "关联课件ID")
    @NotNull(groups = { CoursewareStepValidGroup.ListCoursewareStep.class}, message = "关联课件ID不能为null")
    private Integer coursewareId;
}
