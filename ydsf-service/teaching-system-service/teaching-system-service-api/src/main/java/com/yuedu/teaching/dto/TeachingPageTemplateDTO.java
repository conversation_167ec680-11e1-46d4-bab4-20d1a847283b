package com.yuedu.teaching.dto;

import com.yuedu.ydsf.common.core.util.ValidGroup;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

/**
 * <AUTHOR>
 * @date 2024/11/07
 **/

@Data
public class TeachingPageTemplateDTO {
    /**
     * id
     */
    @Schema(description = "id")
    @NotNull(message = "id不能为空", groups = { ValidGroup.Update.class })
    private Integer id;

    /**
     * 教学页模板名称
     */
    @Schema(description = "教学页模板名称")
    @Length(max = 50, message = "教学页模板名称长度不能超过50", groups = { ValidGroup.Insert.class, ValidGroup.Update.class })
    private String templateName;

    /**
     * 类型
     */
    @Schema(description = "分类")
    @NotNull(message = "分类不能为空", groups = { ValidGroup.Insert.class, ValidGroup.Update.class })
    private Integer type;

    /**
     * 分类
     */
    @Schema(description = "类型")
    @NotNull(message = "类型不能为空", groups = { ValidGroup.Insert.class, ValidGroup.Update.class })
    private Integer category;

    /**
     * 图片/视频url
     */
    @Schema(description = "url")
    @NotNull(message = "url不能为空", groups = { ValidGroup.Insert.class, ValidGroup.Update.class })
    private String url;

    /**
     * 页面url
     */
    @Schema(description = "viewUrl")
    @NotNull(message = "页面url不能为空", groups = { ValidGroup.Insert.class, ValidGroup.Update.class })
    private String viewUrl;

    /**
     * 配置信息
     */
    @Schema(description = "配置信息")
    @NotNull(message = "配置信息不能为空", groups = { ValidGroup.Insert.class, ValidGroup.Update.class })
    private Object attr;

    /**
     * 备注
     */
    @Schema(description = "备注")
    private String remark;

    /**
     * 是否启用，1 启用 0 禁用
     */
    @Schema(description = "是否启用，1 启用 0 禁用")
    private Integer enabled;


}
