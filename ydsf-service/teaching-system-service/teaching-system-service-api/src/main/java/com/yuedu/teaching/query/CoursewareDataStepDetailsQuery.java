package com.yuedu.teaching.query;

import com.yuedu.teaching.valid.DataStepDetailsValidGroup;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Positive;
import lombok.Data;

/**
 * 环节与页面添加DTO
 * <AUTHOR>
 * @Date 2024/11/16 13:30
 */
@Data
public class CoursewareDataStepDetailsQuery {

    /**
     * 课件ID
     */
    @NotNull(groups = {DataStepDetailsValidGroup.DataStepDetails.class,
            DataStepDetailsValidGroup.InnerGetDataStepDetails.class},message = "课件ID不能为空")
    @Positive(message = "课件ID错误")
    @Schema(description = "课件ID", requiredMode = Schema.RequiredMode.REQUIRED)
    private Integer coursewareId;

    /**
     * 课件内容Id
     */
    @NotNull(groups = DataStepDetailsValidGroup.DataStepDetails.class,message = "课件内容ID不能为空")
    @Positive(message = "课件内容ID错误")
    @Schema(description = "课件内容ID", requiredMode = Schema.RequiredMode.REQUIRED)
    private Integer coursewareDataId;

    /**
     * 教学页所属环节ID
     */
    @Schema(description = "教学页所属环节ID", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(groups = {DataStepDetailsValidGroup.DataStepDetails.class,
            DataStepDetailsValidGroup.InnerGetDataStepDetails.class},message = "教学页所属环节ID不能为空")
    @Positive(message = "教学页所属环节ID错误")
    private Integer stepId;

    /**
     * 版本号
     */
    @Schema(description = "版本号")
    private Integer version;
}