package com.yuedu.teaching.dto;

import com.yuedu.teaching.valid.CoursewareStepValidGroup;
import com.yuedu.ydsf.common.core.util.ValidGroup;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.Length;

/**
 * 教学环节表 实体类
 *
 * <AUTHOR>
 * @date 2024-11-05 17:30:36
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CoursewareStepDTO {

    /**
     * id
     */
    @Schema(description = "id")
    @NotNull(groups = {ValidGroup.Update.class, CoursewareStepValidGroup.DeleteStep.class}, message = "id不能为空")
    private Integer id;

    /**
     * 课件内容Id
     */
    @Schema(description = "课件内容Id")
    @NotNull(groups = {ValidGroup.Insert.class, ValidGroup.Update.class}, message = "id不能为空")
    private Integer coursewareDataId;

    /**
     * 关联课件ID
     */
    @Schema(description = "关联课件ID")
    @NotNull(message = "关联课件ID不能为null")
    @NotNull(groups = {ValidGroup.Insert.class, ValidGroup.Update.class, CoursewareStepValidGroup.DeleteStep.class}, message = "关联课件ID不能为空")
    private Integer coursewareId;

    /**
     * 环节名称
     */
    @Schema(description = "环节名称")
    @NotBlank(message = "环节名称不能为空")
    @NotBlank(groups = {ValidGroup.Insert.class}, message = "环节名称不能为空")
    @Length(groups = {ValidGroup.Insert.class}, max = 50, message = "课件名称长度不能超过50")
    private String stepName;

    /**
     * 父ID
     * 如果是环节类型为1（环节），则父ID的值为0
     * 如果是环节类型为2（页面），则父ID的值为该页面的父环节的ID
     */
    @Schema(description = "父ID，type为1时值为0")
    @NotNull(groups = {ValidGroup.Insert.class}, message = "父ID不能为空")
    private Integer stepParent;

    /**
     * 排序
     */
    @Schema(description = "排序")
    @NotNull(groups = {ValidGroup.Insert.class}, message = "排序不能为null")
    private Integer stepOrder;

    /**
     * 环节类型，1是环节，2是页面
     */
    @Schema(description = "环节类型，1是环节，2是页面")
    @NotNull(groups = {ValidGroup.Insert.class}, message = "环节类型不能为null")
    private Integer type;

    /**
     * 页面模板ID
     */
    @Schema(description = "页面模板ID")
    @NotNull(groups = {ValidGroup.Insert.class}, message = "页面模板ID不能为空")
    private Integer pageTemplateId;

}
