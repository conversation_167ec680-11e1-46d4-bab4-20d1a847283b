package com.yuedu.teaching.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;


/**
 * 教学环节存储展示
 *
 * <AUTHOR>
 * @date 2024-11-05 17:32:03
 */
@Data
public class StepVO {

    /**
     * id
     */
    @Schema(description = "id")
    private Integer id;


    /**
     * 环节名称
     */
    @Schema(description = "环节名称")
    private String stepName;

    /**
     * 父ID
     */
    @Schema(description = "父ID")
    private Integer stepParent;

    /**
     * 排序
     */
    @Schema(description = "排序")
    private Integer stepOrder;

    /**
     * 环节类型，1是环节，2是页面
     */
    @Schema(description = "环节类型，1是环节，2是页面")
    private Integer type;

    /**
     * 页面模板ID
     */
    @Schema(description = "页面模板ID")
    private Integer pageTemplateId;


    /**
     * 子级
     */
    @Schema(description = "子级")
    private List<StepVO> children;

    /**
     * 页面url
     */
    @Schema(description = "页面url")
    private String viewUrl;

    /**
     * 父ID排序
     */
    @Schema(description = "父ID排序")
    private Integer stepParentOrder;

    /**
     * 环节详情
     */
    @Schema(description = "环节详情")
    private Object configs;

    /**
     * 页面工具
     */
    @Schema(description = "页面工具")
    private Object tools;

}
