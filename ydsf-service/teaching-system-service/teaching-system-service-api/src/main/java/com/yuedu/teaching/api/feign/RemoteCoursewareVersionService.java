package com.yuedu.teaching.api.feign;

import com.yuedu.teaching.dto.CoursewareVersionDTO;
import com.yuedu.teaching.vo.CoursewareVersionVO;
import com.yuedu.ydsf.common.core.constant.ServiceNameConstants;
import com.yuedu.ydsf.common.core.util.R;
import com.yuedu.ydsf.common.feign.annotation.NoToken;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.Mapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

/**
 * @ClassName RemoteCourseService
 * @Description courseService feign调用
 * <AUTHOR>
 * @Date 2024/11/26 08:59
 * @Version v0.0.1
 */
@FeignClient(contextId = "remoteCoursewareVersionService", value = ServiceNameConstants.TEACHING_SERVICE)
public interface RemoteCoursewareVersionService {

    /**
     * 通过课节Id列表查询课件版本信息
     *
     * @param ids id列表
     * @return List<CoursewareVO>
     */
    @PostMapping("/courseVersion/listCoursewareByIds")
    @NoToken
    R<List<CoursewareVersionVO>> listCoursewareByIds(@RequestBody List<Long> ids);

    /**
     * 获取时间段内的课件版本修改历史
     * <AUTHOR>
     * @date 2025/3/13 16:05
     * @param coursewareVersionDTO
     * @return com.yuedu.ydsf.common.core.util.R<java.util.List<com.yuedu.teaching.vo.CoursewareVersionVO>>
     */
    @PostMapping ("/courseVersion/listCoursewareVersionHis")
    @NoToken
    R<List<CoursewareVersionVO>> listCoursewareVersionHis(@RequestBody CoursewareVersionDTO coursewareVersionDTO);
}
