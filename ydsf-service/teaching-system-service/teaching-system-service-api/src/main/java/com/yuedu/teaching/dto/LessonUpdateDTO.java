package com.yuedu.teaching.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;


/**
 * @ClassName LessonEntity
 * @Description 课程章节表
 * <AUTHOR>
 * @Date 2024/10/24 9:48
 * @Version v0.0.1
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class LessonUpdateDTO extends LessonAddDTO {

    /**
     * 课节Id
     */
    @Schema(description = "课节Id", requiredMode = Schema.RequiredMode.REQUIRED)
    private Integer id;


    /**
     * 发布状态，0:未发布 1：已发布
     */
    @Schema(description = "发布状态，0:未发布 1：已发布")
    private Integer publishStatus;


    /**
     * 是否可发布，0：不可发布，1:可发布
     */
    @Schema(description = "是否可发布，0：不可发布，1:可发布")
    private Integer canPublish;


    /**
     * 课程排序
     */
    @Schema(description = "课程排序")
    private Integer lessonOrder;
}