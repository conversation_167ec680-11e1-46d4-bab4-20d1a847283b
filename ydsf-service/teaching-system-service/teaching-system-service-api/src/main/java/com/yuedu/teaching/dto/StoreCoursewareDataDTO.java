package com.yuedu.teaching.dto;

import com.yuedu.ydsf.common.core.util.V_A_E;
import com.yuedu.ydsf.common.core.util.V_E;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
* 门店课件表
*
* <AUTHOR>
* @date  2025/08/05
*/
@Data
@Schema(description = "门店课件表传输对象")
public class StoreCoursewareDataDTO implements Serializable {


    /**
     * 主键id
     */
    @NotNull(groups = {V_E.class}, message = "主键id不能为空")
    private Integer id;



    /**
     * 资料表ID
     */
    @Schema(description = "资料表ID")
    @NotNull(groups = {V_A_E.class }, message = "资料表ID不能为空")
    private Integer coursewareDataId;

    /**
     * 版本(创建副本时总部课件的版本)
     */
    @Schema(description = "版本(创建副本时总部课件的版本)")
    @NotNull(groups = {V_A_E.class }, message = "版本(创建副本时总部课件的版本)不能为空")
    private Integer version;

    /**
     * 课件名称
     */
    @Schema(description = "课件名称")
    @NotBlank(groups = {V_A_E.class }, message = "课件名称不能为空")
    @Length(groups = {V_A_E.class }, max =255 ,message = "课件名称长度不能大于255")
    private String coursewareName;

    /**
     * 关联课件ID
     */
    @Schema(description = "关联课件ID")
    @NotNull(groups = {V_A_E.class }, message = "关联课件ID不能为空")
    private Integer coursewareId;

    /**
     * 关联课件模版ID
     */
    @Schema(description = "关联课件模版ID")
    @NotNull(groups = {V_A_E.class }, message = "关联课件模版ID不能为空")
    private Integer dataTemplateId;

    /**
     * 类型 课件类型 与课件模版一致
     */
    @Schema(description = "类型 课件类型 与课件模版一致")
    @NotNull(groups = {V_A_E.class }, message = "类型 课件类型 与课件模版一致不能为空")
    private Byte type;

    /**
     * 详细数据
     */
    @Schema(description = "详细数据")
    @NotNull(groups = {V_A_E.class }, message = "详细数据不能为空")
    private Object details;

    /**
     * 门店id
     */
    @Schema(description = "门店id")
    @NotNull(groups = {V_A_E.class }, message = "门店id不能为空")
    private Integer storeId;

    /**
     * 校区id
     */
    @Schema(description = "校区id")
    @NotNull(groups = {V_A_E.class }, message = "校区id不能为空")
    private Integer schoolId;

    /**
     * 所属者(副本创建人)
     */
    @Schema(description = "所属者(副本创建人)")
    @NotNull(groups = {V_A_E.class }, message = "所属者(副本创建人)不能为空")
    private Long owner;

    /**
     * 是否使用:0-未使用;1-使用
     */
    @Schema(description = "是否使用:0-未使用;1-使用")
    @NotNull(groups = {V_A_E.class }, message = "是否使用不能为空")
    private Integer isUse;

    /**
     * 是否删除
     */
    @Schema(description = "是否删除")
    @NotNull(groups = {V_A_E.class }, message = "是否删除不能为空")
    private Byte delFlag;

    /**
     * 创建人
     */
    @Schema(description = "创建人")
    @Length(groups = {V_A_E.class }, max =255 ,message = "创建人长度不能大于255")
    private String createBy;

    /**
     * 创建时间
     */
    @Schema(description = "创建时间")
    @NotNull(groups = {V_A_E.class }, message = "创建时间不能为空")
    private LocalDateTime createTime;

    /**
     * 修改人
     */
    @Schema(description = "修改人")
    @Length(groups = {V_A_E.class }, max =255 ,message = "修改人长度不能大于255")
    private String updateBy;

    /**
     * 修改时间
     */
    @Schema(description = "修改时间")
    @NotNull(groups = {V_A_E.class }, message = "修改时间不能为空")
    private LocalDateTime updateTime;


}
