package com.yuedu.teaching.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Positive;
import lombok.Data;

/**
 * 环节与页面添加DTO
 * <AUTHOR>
 * @Date 2024/11/21 10:54
 */
@Data
public class CoursewareDataStepDetailsDTO {

    /**
     * 课件ID
     */
    @NotNull(message = "课件ID不能为空")
    @Positive(message = "课件ID错误")
    @Schema(description = "课件ID", requiredMode = Schema.RequiredMode.REQUIRED)
    private Integer coursewareId;

    /**
     * 课件内容Id
     */
    @NotNull(message = "课件内容ID不能为空")
    @Positive(message = "课件内容ID错误")
    @Schema(description = "课件内容ID", requiredMode = Schema.RequiredMode.REQUIRED)
    private Integer coursewareDataId;

    /**
     * 教学页所属环节ID
     */
    @Schema(description = "教学页所属环节ID", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "教学页所属环节ID不能为空")
    @Positive(message = "教学页所属环节ID错误")
    private Integer stepId;

    /**
     * 详细数据
     */
    @Schema(description = "详细数据")
    private String details;

    /**
     * 工具数据
     */
    @Schema(description = "工具数据")
    private String tool;
}