package com.yuedu.teaching.vo;

import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 发版课程章节表
 *
 * <AUTHOR>
 * @date 2024-10-24 14:20:46
 */
@Data
@Schema(description = "发版课程章节表")
public class LessonPubVO {

    /**
     * 课程章节ID
     */
    @Schema(description = "课程章节ID")
    private Long lessonId;

    /**
     * 版本
     */
    @Schema(description = "版本")
    private Integer version;

    /**
     * 关联课件
     */
    @Schema(description = "关联课件")
    private Integer coursewareId;

    /**
     * 关联课件版本
     */
    @Schema(description = "关联课件版本")
    private Integer coursewareVersion;

    /**
     * 课节名称
     */
    @Schema(description = "课节名称")
    private String lessonName;

    /**
     * 课程Id
     */
    @Schema(description = "课程Id")
    private Long courseId;

    /**
     * 课节顺序
     */
    @Schema(description = "课节顺序")
    private Integer lessonOrder;

    /**
     * 书籍名称
     */
    @Schema(description = "书籍名称")
    private String bookName;

    /**
     * 书籍封面
     */
    @Schema(description = "书籍封面")
    private String bookCover;
}
