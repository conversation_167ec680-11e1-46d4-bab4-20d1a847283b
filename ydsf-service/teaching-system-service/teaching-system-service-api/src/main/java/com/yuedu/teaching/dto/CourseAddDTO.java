package com.yuedu.teaching.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/10/24
 **/

@Data
public class CourseAddDTO {

    /**
     * 课程产品名称
     */
    @Schema(description = "课程产品名称")
    @NotBlank(message = "课程名称不能为空")
    @Length(max = 50, message = "课程产品名称长度不能超过50")
    private String courseName;

    /**
     * 阶段ID
     */
    @NotNull(message = "阶段ID不能为空")
    @Schema(description = "阶段ID")
    private Integer stageId;

    /**
     * 课程类型ID
     */
    @NotNull(message = "课程类型ID")
    @Schema(description = "课程类型ID")
    private Long courseTypeId;

    /**
     * 收费方式:0-门店设置;1-自定义
     */
    @Schema(description = "收费方式:0-门店设置;1-自定义")
    @NotNull(message = "收费方式")
    private Integer chargeMethod;


    /**
     * 自定义课时费标准(收费方式为自定义时设置)
     */
    @Schema(description = "自定义课时费标准(收费方式为自定义时设置)")
    private BigDecimal customizeFee;

    /**
     * 授权门店ID集合
     */
    @Schema(description = "授权门店ID集合")
    private List<Long> authStoreIds;
}
