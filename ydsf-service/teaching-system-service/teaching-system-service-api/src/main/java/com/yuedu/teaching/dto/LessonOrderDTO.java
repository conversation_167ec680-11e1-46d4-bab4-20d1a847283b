package com.yuedu.teaching.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * @ClassName LessonOrderDTO
 * @Description 根据课程id和课节排序DTO
 * <AUTHOR>
 * @Date 2024/12/24 17:10
 * @Version v0.0.1
 */

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class LessonOrderDTO {
    /**
     * 课程id
     */
    private Long courseId;

    /**
     * 课节排序列表
     */
    private List<Integer> lessonOrderList;

    /**
     * 课程版本
     */
    private Integer version;
}
