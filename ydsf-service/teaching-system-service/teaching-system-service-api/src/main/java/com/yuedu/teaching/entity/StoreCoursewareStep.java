package com.yuedu.teaching.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import java.time.LocalDateTime;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 门店教学环节表
 * 
 * <AUTHOR>
 * @date 2025/08/05
 */
@TableName("store_courseware_step")
@Data
@EqualsAndHashCode(callSuper = true)
public class StoreCoursewareStep extends Model<StoreCoursewareStep> {
    /**
     * 主键id
     */
    @TableId(type = IdType.AUTO)
    private Integer id;

    /**
     * 原step表id
     */
    private Integer stepId;

    /**
     * 关联课件ID
     */
    private Integer coursewareId;

    /**
     * 课件内容Id
     */
    private Integer coursewareDataId;

    /**
     * 课件名称
     */
    private String stepName;

    /**
     * 父ID
     */
    private Integer stepParent;

    /**
     * 排序
     */
    private Integer stepOrder;

    /**
     * 版本
     */
    private Integer version;

    /**
     * 环节类型，1是环节，2是页面
     */
    private Integer type;

    /**
     * 教学页模版ID
     */
    private Integer pageTemplateId;

    /**
     * 门店id
     */
    private Integer storeId;

    /**
     * 校区id
     */
    private Integer schoolId;

    /**
     * 所属者(副本创建人)
     */
    private Long owner;

    /**
     * 是否删除
     */
    @TableLogic
    @TableField(fill = FieldFill.INSERT)
    private Integer delFlag;

    /**
     * 创建人
     */
    @TableField(fill = FieldFill.INSERT)
    private String createBy;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 修改人
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private String updateBy;

    /**
     * 修改时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;
}