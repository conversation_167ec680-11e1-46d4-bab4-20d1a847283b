package com.yuedu.teaching.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Positive;
import lombok.Data;

/**
 * @ClassName CoursewareDataStepDetailsUpdateDTO
 * @Description 环节与页面添加DTO
 * <AUTHOR>
 * @Date 2024/11/6 14:14
 * @Version v0.0.1
 */
@Data
public class CoursewareDataStepDetailsUpdateDTO {

    /**
     * id
     */
    @Schema(description = "id")
    private Integer id;

    /**
     * 课件ID
     */
    @NotNull(message = "课件ID不能为空")
    @Positive(message = "课件ID错误")
    @Schema(description = "课件ID", requiredMode = Schema.RequiredMode.REQUIRED)
    private Integer coursewareId;

    /**
     * 课件内容Id
     */
    @NotNull(message = "课件内容ID不能为空")
    @Positive(message = "课件内容ID错误")
    @Schema(description = "课件内容ID", requiredMode = Schema.RequiredMode.REQUIRED)
    private Integer coursewareDataId;

    /**
     * 教学页所属环节ID
     */
    @Schema(description = "教学页所属环节ID", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "教学页所属环节ID不能为空")
    @Positive(message = "教学页所属环节ID错误")
    private Integer stepId;


    /**
     * 背景图片
     */
    @Schema(description = "背景图片")
    private String backImg;

    /**
     * 环节教案
     */
    @Schema(description = "环节教案")
    private String teachingPlanInfo;

    /**
     * 动画信息
     */
    @Schema(description = "动画信息")
    private String animationInfo;
}