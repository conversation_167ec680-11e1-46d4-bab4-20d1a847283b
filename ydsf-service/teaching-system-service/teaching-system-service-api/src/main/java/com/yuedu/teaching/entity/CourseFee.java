package com.yuedu.teaching.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * TODO
 * 
 * <AUTHOR>
 * @date 2025/05/22
 */
@TableName("course_fee")
@Data
@EqualsAndHashCode(callSuper = true)
public class CourseFee extends Model<CourseFee> {
    /**
     * 课消ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 是否删除: 0-正常;1-删除
     */
    @TableLogic
    @TableField(fill = FieldFill.INSERT)
    private Integer delFlag;

    /**
     * 创建人
     */
    @TableField(fill = FieldFill.INSERT)
    private String createBy;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 修改人
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private String updateBy;

    /**
     * 修改时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    /**
     * 标准价格
     */
    private BigDecimal standardPrice;

    /**
     * 生效日期
     */
    private LocalDate effectiveDate;

    /**
     * 门店ID
     */
    private Long storeId;

    /**
     * 课程类型ID
     */
    private Long courseTypeId;

    /**
     * 课程ID
     */
    private Long courseId;

    /**
     * 操作类型: 0-门店设置; 1-自定义
     */
    private Integer optType;
}