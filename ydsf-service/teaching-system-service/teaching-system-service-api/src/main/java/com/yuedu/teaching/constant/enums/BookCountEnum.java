package com.yuedu.teaching.constant.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * @ClassName BookCountEnum
 * @Description 增加、减少书籍数量枚举类
 * <AUTHOR>
 * @Date 2024/10/28 09:06
 * @Version v0.0.1
 */

@Getter
@AllArgsConstructor
public enum BookCountEnum {
    /**
     * 增加书籍数量
     */
    ADD(1, "增加"),
    /**
     * 减少书籍数量
     */
    SUBTRACT(-1, "减少"),
    /**
     * 没有版本数量
     */
    NO_COUNT(0, "版本数为0");

    private final int code;

    private final String desc;
}
