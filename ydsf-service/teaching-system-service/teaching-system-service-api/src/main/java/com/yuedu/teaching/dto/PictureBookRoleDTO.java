package com.yuedu.teaching.dto;

import com.yuedu.teaching.valid.PictureBookRoleValidGroup;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

/**
 * 绘本角色表 DTO
 *
 * <AUTHOR>
 * @date 2025/1/3 15:06
 */
@Data
public class PictureBookRoleDTO {
    /**
     * id
     */
    @Schema(description = "id")
    @NotNull(message = "ID不能为空", groups = { PictureBookRoleValidGroup.UpdateRoles.class })
    private Integer id;

    /**
     * 书籍ID
     */
    @Schema(description = "书籍ID")
    @NotNull(message = "书籍ID不能为空", groups = { PictureBookRoleValidGroup.AddRoles.class, PictureBookRoleValidGroup.UpdateRoles.class })
    private Integer bookId;

    /**
     * 角色名
     */
    @NotBlank(message = "角色名不能为空", groups = { PictureBookRoleValidGroup.AddRoles.class })
    @Length(max = 10, message = "角色名长度不能超过10", groups = { PictureBookRoleValidGroup.AddRoles.class, PictureBookRoleValidGroup.UpdateRoles.class })
    @Schema(description = "角色名")
    private String roleName;

    /**
     * 缩略图
     */
    @NotBlank(message = "缩略图不能为空", groups = { PictureBookRoleValidGroup.AddRoles.class })
    @Schema(description = "缩略图")
    private String url;
}
