package com.yuedu.teaching.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * 资料数据和教学环节详情版本表 实体类
 *
 * <AUTHOR>
 * @date 2024-11-06 08:42:46
 */
@Data
@TableName("courseware_data_step_details_pub")
@EqualsAndHashCode(callSuper = true)
@Schema(description = "资料数据和教学环节详情版本表实体类")
public class CoursewareDataStepDetailsPub extends Model<CoursewareDataStepDetailsPub> {


    /**
     * id
     */
    @TableId(type = IdType.AUTO)
    @Schema(description = "id")
    private Integer id;

    /**
     * 原details表id
     */
    @Schema(description = "courseware_data_step_details_id")
    private Integer coursewareDataStepDetailsId;

    /**
     * 关联课件ID
     */
    @Schema(description = "关联课件ID")
    private Integer coursewareId;

    /**
     * 关联课件内容ID
     */
    @Schema(description = "关联课件内容ID")
    private Integer coursewareDataId;

    /**
     * 环节ID
     */
    @Schema(description = "环节ID")
    private Integer stepId;

    /**
     * 详细数据
     */
    @Schema(description = "详细数据")
    private String details;

    /**
     * 页面工具
     */
    @Schema(description = "页面工具")
    private String tool;

    /**
     * teaching_page_template表的Id
     */
    @Schema(description = "teaching_page_template表的Id")
    private Integer pageTemplateId;

    /**
     * 教学页分类
     */
    @Schema(description = "教学页分类")
    private Integer teachingPageCategory;

    /**
     * 教学页类型
     */
    @Schema(description = "教学页类型")
    private Integer teachingPageType;


    /**
     * 版本
     */
    @Schema(description = "版本")
    private Integer version;

    /**
     * 创建人
     */
    @TableField(fill = FieldFill.INSERT)
    @Schema(description = "创建人")
    private String createBy;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    /**
     * 修改人
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    @Schema(description = "修改人")
    private String updateBy;

    /**
     * 修改时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    @Schema(description = "修改时间")
    private LocalDateTime updateTime;

    /**
     * 是否删除
     */
    @TableLogic
    @TableField(fill = FieldFill.INSERT)
    @Schema(description = "是否删除")
    private Integer delFlag;
}
