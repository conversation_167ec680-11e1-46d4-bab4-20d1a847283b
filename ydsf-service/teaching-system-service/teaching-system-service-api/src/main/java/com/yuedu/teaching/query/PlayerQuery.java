package com.yuedu.teaching.query;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;


/**
 * 播放器环节
 * <AUTHOR>
 */
@Data
@Schema(description = "播放器环节")
public class PlayerQuery {

    /**
     * act 1上课 2练课
     */
    @Schema(description = "动作")
    @NotBlank(message = "上课练课不能为空")
    private Integer act;

    /**
     * client 端 2 教室 3 主讲
     */
    @Schema(description = "端")
    private Integer client;


    /**
     * 课程类型
     */
    @Schema(description = "课程类型")
    @NotBlank(message = "课程类型不能为空")
    private Integer courseType;

    /**
     * 课件ID
     */
    @Schema(description = "课件ID")
    @NotBlank(message = "课件ID不能为空")
    private Integer coursewareId;

    /**
     * 课件ID
     */
    @Schema(description = "课件版本")
    private Integer coursewareVersion;
}
