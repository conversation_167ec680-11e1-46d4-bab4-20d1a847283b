package com.yuedu.teaching.api.feign;

import com.yuedu.teaching.query.LessonPubQuery;
import com.yuedu.teaching.vo.LessonPubVO;
import com.yuedu.ydsf.common.core.constant.ServiceNameConstants;
import com.yuedu.ydsf.common.core.util.R;
import com.yuedu.ydsf.common.feign.annotation.NoToken;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;
import java.util.Map;

/**
 * @ClassName RemoteLessonService
 * @Description lessonService feign调用
 * <AUTHOR>
 * @Date 2024/11/26 09:02
 * @Version v0.0.1
 */
@FeignClient(contextId = "remoteLessonPubService", value = ServiceNameConstants.TEACHING_SERVICE)
public interface RemoteLessonPubService {

    /**
     * 通过 课节id列表查询已发布的课件信息
     *
     * @param lessonPubQuery 课节发布查询列表
     * @return 结果
     */
    @PostMapping("/lessonPub/getLessonByIds")
    @NoToken
    R<List<LessonPubVO>> getLessonByIds(@RequestBody LessonPubQuery lessonPubQuery);

    /**
     * 通过课程ID查询已发布的课节信息
     * @param courseIdList
     * @return
     */
    @PostMapping("/lessonPub/getLessonByCourseIdList")
    @NoToken
    R<Map<Long, List<LessonPubVO>>> getLessonByCourseIdList(@RequestBody List<Long> courseIdList);
}