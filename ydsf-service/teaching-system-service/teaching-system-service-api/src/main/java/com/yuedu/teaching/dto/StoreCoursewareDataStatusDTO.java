package com.yuedu.teaching.dto;

import com.yuedu.teaching.valid.StoreCoursewareDataValidGroup;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Positive;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.Max;
import lombok.Data;

import java.io.Serializable;

/**
 * 门店课件使用状态更新DTO
 *
 * <AUTHOR>
 * @date 2025/08/05
 */
@Data
@Schema(description = "门店课件使用状态更新DTO")
public class StoreCoursewareDataStatusDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 关联课件ID
     */
    @NotNull(groups = StoreCoursewareDataValidGroup.UpdateStatus.class, message = "课件ID不能为空")
    @Positive(groups = StoreCoursewareDataValidGroup.UpdateStatus.class, message = "课件ID错误")
    @Schema(description = "关联课件ID", requiredMode = Schema.RequiredMode.REQUIRED)
    private Integer coursewareId;

    /**
     * 关联课件内容ID
     */
    @NotNull(groups = StoreCoursewareDataValidGroup.UpdateStatus.class, message = "课件内容ID不能为空")
    @Positive(groups = StoreCoursewareDataValidGroup.UpdateStatus.class, message = "课件内容ID错误")
    @Schema(description = "关联课件内容ID", requiredMode = Schema.RequiredMode.REQUIRED)
    private Integer coursewareDataId;

    /**
     * 是否使用:0-未使用;1-使用
     */
    @NotNull(groups = StoreCoursewareDataValidGroup.UpdateStatus.class, message = "使用状态不能为空")
    @Min(value = 0, groups = StoreCoursewareDataValidGroup.UpdateStatus.class, message = "使用状态值错误")
    @Max(value = 1, groups = StoreCoursewareDataValidGroup.UpdateStatus.class, message = "使用状态值错误")
    @Schema(description = "是否使用:0-未使用;1-使用", requiredMode = Schema.RequiredMode.REQUIRED, allowableValues = {"0", "1"})
    private Integer isUse;
}
