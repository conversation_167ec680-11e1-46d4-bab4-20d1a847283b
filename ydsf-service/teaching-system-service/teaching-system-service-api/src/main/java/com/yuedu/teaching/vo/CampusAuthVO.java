package com.yuedu.teaching.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2025/05/21
 */
@Data
public class CampusAuthVO {
  /** 主键Id */
  @Schema(description = "主键ID")
  private Long id;

  /** 校管家校区ID(类型为主讲端时为空) */
  @Schema(description = "校管家校区ID(类型为主讲端时为空)")
  private String xgjCampusId;

  /** 校区编号 */
  @Schema(description = "校区编号")
  private String campusNo;

  /** 校区名称 */
  @Schema(description = "校区名称")
  private String campusName;

  /** 校区状态: 0-启用; 1-禁用; */
  @Schema(description = "校区状态: 0-启用; 1-禁用;")
  private Integer campusState;

  /** 校区类型: 1-主讲端; 2-教室端; */
  @Schema(description = "校区类型: 1-主讲端; 2-教室端;")
  private Integer campusType;

  /** 校区ID */
  @Schema(description = "校区ID")
  private Integer schoolId;

  /** 校区名称 */
  @Schema(description = "校区名称")
  private String schoolName;

  /** 大区ID */
  @Schema(description = "大区ID")
  private Integer regionId;

  /** 大区名称 */
  @Schema(description = "大区名称")
  private String regionName;

  /** 省份编码 */
  @Schema(description = "省份编码")
  private String provinceCode;

  /** 省份名称 */
  @Schema(description = "省份名称")
  private String provinceName;

  /** 城市编码 */
  @Schema(description = "城市编码")
  private String cityCode;

  /** 城市名称 */
  @Schema(description = "城市名称")
  private String cityName;

  /** 是否已授权 */
  @Schema(description = "是否已授权")
  private Boolean isAuthorized;
}
