package com.yuedu.teaching.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDate;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class CourseFeePairDTO {
    /**
     * 课程ID
     */
    @Schema(description = "课程ID", requiredMode = Schema.RequiredMode.REQUIRED)
    private Long courseId;

    /**
     * 上课时间
     */
    @Schema(description = "上课时间", requiredMode = Schema.RequiredMode.REQUIRED)
    private LocalDate classDate;


}
