package com.yuedu.teaching.query;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * @ClassName CoursePublishQuery
 * @Description 课程发布查询参数
 * <AUTHOR>
 * @Date 2024/11/27 09:34
 * @Version v0.0.1
 */

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CoursePublishQuery {
    /**
     * 课程id
     */
    private Long courseId;

    /**
     * 课程名称
     */
    private String courseName;

    /**
     * 课节名称
     */
    private String lessonName;

    /**
     * 课节id列表
     */
    private List<Integer> lessonIdList;

    /**
     * 课程排序
     */
    private Integer lessonOrder;

    /**
     * 版本
     */
    private Integer version;
}
