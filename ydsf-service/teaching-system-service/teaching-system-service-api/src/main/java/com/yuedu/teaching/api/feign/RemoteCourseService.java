package com.yuedu.teaching.api.feign;

import com.yuedu.teaching.dto.CourseDTO;
import com.yuedu.teaching.query.CoursePublishQuery;
import com.yuedu.teaching.vo.CourseVO;
import com.yuedu.ydsf.common.core.constant.ServiceNameConstants;
import com.yuedu.ydsf.common.core.util.R;
import com.yuedu.ydsf.common.feign.annotation.NoToken;
import io.swagger.v3.oas.annotations.Operation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * @ClassName RemoteCourseService
 * @Description courseService feign调用
 * <AUTHOR>
 * @Date 2024/11/26 08:59
 * @Version v0.0.1
 */
@FeignClient(contextId = "remoteCourseService", value = ServiceNameConstants.TEACHING_SERVICE)
public interface RemoteCourseService {
    /**
     * 获取所有已发布课程列表
     *
     * @return R<List<CourseVO>>
     */
    @GetMapping("/course/list")
    @NoToken
    R<List<CourseVO>> getCourseList();


    /**
     *  根据门店ID获得课程列表
     *
     * <AUTHOR>
     * @date 2025年06月05日 16时04分
     */
    @GetMapping("/course/listByStoreId")
    @NoToken
    R<List<CourseVO>> getCourseListByStoreId(Long id);

    /**
     * 根据课程名称获取已发布课程列表
     *
     * @param coursePublishQuery   查询参数
     * @return R<List<CourseVO>>
     */
    @PostMapping("/course/listByName")
    @NoToken
    R<List<CourseVO>> getCourseListByName(@RequestBody CoursePublishQuery coursePublishQuery);

    /**
     * 通过Id列表查询课件Id和课件名称
     *
     * @param ids id列表
     * @return List<CoursewareVO>
     */
    @PostMapping("/course/listCourseByIds")
    @NoToken
    R<List<CourseVO>> listCourseByIds(@RequestBody List<Long> ids);

    /**
     * 根据课程id获取已发布课程列表
     *
     * @param courseDTO 课程id列表
     * @return R<List < CourseVO>>
     */
    @PostMapping("/course/listById")
    @NoToken
    R<List<CourseVO>> getCourseListByIds(@RequestBody CourseDTO courseDTO);

    /**
     * 根据课程id获取已发布课程列表
     * @param courseIdList 课程id列表
     * @return Map<Long,CourseVO>
     */
    @PostMapping("/course/getMapById")
    @NoToken
    R<Map<Long,CourseVO>> getCourseMapByIdList(@RequestBody List<Long> courseIdList);

    /**
     * 根据课程版本获取课程信息列表
     *
     * @param courseDTO 课程DTO
     * @return R<List<CourseVO>>
     */
    @PutMapping("/course/listByVersion")
    @NoToken
    R<List<CourseVO>> getCourseListByVersion(@RequestBody CourseDTO courseDTO);

    /**
     * 根据课程的阶段获取课程信息
     * <AUTHOR>
     * @date 2025/4/22 11:57
     * @param courseDTO
     * @return com.yuedu.ydsf.common.core.util.R<java.util.List<com.yuedu.teaching.vo.CourseVO>>
     */
    @PostMapping("/course/getCourseListByStageId")
    @NoToken
    R<List<CourseVO>> getCourseListByStageId(CourseDTO courseDTO);

    /**
     * 根据课程类型查询课程ID
     */
    @GetMapping("/course/getCourseIdByType")
    @NoToken
    R<List<Integer>> getCourseIdByType(@RequestParam("courseTypeId") Long courseTypeId);
}
