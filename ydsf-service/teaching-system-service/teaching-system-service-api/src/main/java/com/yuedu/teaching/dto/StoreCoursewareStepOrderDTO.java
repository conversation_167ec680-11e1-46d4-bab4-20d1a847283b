package com.yuedu.teaching.dto;

import com.yuedu.teaching.valid.StoreCoursewareStepValidGroup;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * 门店教学环节排序DTO
 * <AUTHOR>
 * @date 2025/08/06
 */
@Data
@Schema(description = "门店教学环节排序DTO")
public class StoreCoursewareStepOrderDTO {

    /**
     * 课件内容Id
     */
    @Schema(description = "课件内容Id", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(groups = {StoreCoursewareStepValidGroup.UpdateStoreStepOrder.class}, message = "课件内容Id不能为null")
    private Integer coursewareDataId;

    /**
     * 关联课件ID
     */
    @Schema(description = "关联课件ID", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(groups = {StoreCoursewareStepValidGroup.UpdateStoreStepOrder.class}, message = "关联课件ID不能为null")
    private Integer coursewareId;

    /**
     * 数据列表
     */
    @Schema(description = "环节排序信息列表", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(groups = {StoreCoursewareStepValidGroup.UpdateStoreStepOrder.class}, message = "环节排序信息列表不能为空")
    @Valid
    private List<Info> infoList;

    @Data
    @Accessors(chain = true)
    @Schema(description = "环节排序信息")
    public static class Info{
        /**
         * 门店教学环节id
         */
        @Schema(description = "门店教学环节id", requiredMode = Schema.RequiredMode.REQUIRED)
        @NotNull(groups = {StoreCoursewareStepValidGroup.UpdateStoreStepOrder.class}, message = "门店教学环节id不能为null")
        private Integer id;

        /**
         * 父id
         */
        @Schema(description = "父环节id", requiredMode = Schema.RequiredMode.REQUIRED)
        @NotNull(groups = {StoreCoursewareStepValidGroup.UpdateStoreStepOrder.class}, message = "父环节id不能为null")
        private Integer stepParent;

        /**
         * 排序
         */
        @Schema(description = "排序")
        @NotNull(groups = {StoreCoursewareStepValidGroup.UpdateStoreStepOrder.class}, message = "排序不能为空")
        private Integer stepOrder;
    }
}
