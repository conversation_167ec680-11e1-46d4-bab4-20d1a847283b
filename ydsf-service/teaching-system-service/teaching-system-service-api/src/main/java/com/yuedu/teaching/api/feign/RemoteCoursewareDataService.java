package com.yuedu.teaching.api.feign;

import com.yuedu.teaching.query.CoursewareDataStepDetailsQuery;
import com.yuedu.teaching.query.RemoveCoursewareQuery;
import com.yuedu.teaching.vo.CourseVO;
import com.yuedu.teaching.vo.CoursewareDataStepDetailsVO;
import com.yuedu.ydsf.common.core.constant.ServiceNameConstants;
import com.yuedu.ydsf.common.core.util.R;
import com.yuedu.ydsf.common.feign.annotation.NoToken;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

/**
 * 课件列表
 * <AUTHOR>
 */
@FeignClient(contextId = "remoteCoursewareDataService", value = ServiceNameConstants.TEACHING_SERVICE)
public interface RemoteCoursewareDataService {
    /**
     * 获取课件列表
     *
     * @return R<List<CourseVO>>
     */
    @GetMapping("/coursewareDataPub/list")
    @NoToken
    R<List<CourseVO>> getCoursewareDataList(RemoveCoursewareQuery params);

    /**
     * 获取互动页选项参数
     * @param coursewareDataStepDetailsQuery coursewareId stepId
     * @return R
     */
    @PostMapping ("/coursewareDataStepDetails/details")
    @NoToken
    R<CoursewareDataStepDetailsVO> getDetails(@RequestBody CoursewareDataStepDetailsQuery coursewareDataStepDetailsQuery);
}
