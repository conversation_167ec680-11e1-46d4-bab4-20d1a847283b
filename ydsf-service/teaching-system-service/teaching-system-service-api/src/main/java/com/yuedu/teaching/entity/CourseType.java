package com.yuedu.teaching.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import java.time.LocalDateTime;

/**
 * 课程类型 实体类
 *
 * <AUTHOR>
 * @date 2025-05-21 08:57:32
 */
@Data
@TableName("course_type")
@EqualsAndHashCode(callSuper = true)
@Schema(description = "课程类型实体类")
public class CourseType extends Model<CourseType> {


	/**
	* 自增主键
	*/
    @TableId(type = IdType.AUTO)
    @Schema(description="自增主键")
    private Integer id;

	/**
	* 课程类型名称
	*/
    @Schema(description="课程类型名称")
    private String name;

	/**
	* 课程类型编码
	*/
    @Schema(description="课程类型编码")
    private String code;

	/**
	 * 联营收费类型
	 */
	@Schema(description="联营收费类型")
	private Integer jointPayType;

	/**
	* 状态：1可用，2停用
	*/
    @Schema(description="状态：1可用，2停用")
    private Integer status;

	/**
	* 创建人
	*/
	@TableField(fill = FieldFill.INSERT)
    @Schema(description="创建人")
    private String createBy;

	/**
	* 创建时间
	*/
	@TableField(fill = FieldFill.INSERT)
    @Schema(description="创建时间")
    private LocalDateTime createTime;

	/**
	* 修改人
	*/
	@TableField(fill = FieldFill.INSERT_UPDATE)
    @Schema(description="修改人")
    private String updateBy;

	/**
	* 修改时间
	*/
	@TableField(fill = FieldFill.INSERT_UPDATE)
    @Schema(description="修改时间")
    private LocalDateTime updateTime;

	/**
	* 是否删除: 0-否; 1-是;
	*/
    @TableLogic
	@TableField(fill = FieldFill.INSERT)
    @Schema(description="是否删除: 0-否; 1-是;")
    private Integer delFlag;
}
