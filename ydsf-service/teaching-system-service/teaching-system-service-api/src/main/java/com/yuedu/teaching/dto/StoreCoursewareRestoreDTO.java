package com.yuedu.teaching.dto;

import com.yuedu.teaching.valid.StoreCoursewareDataValidGroup;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Positive;
import lombok.Data;

import java.io.Serializable;

/**
 * 门店课件恢复标准版DTO
 *
 * <AUTHOR>
 * @date 2025/08/05
 */
@Data
@Schema(description = "门店课件恢复标准版DTO")
public class StoreCoursewareRestoreDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 关联课件ID
     */
    @NotNull(groups = StoreCoursewareDataValidGroup.RestoreStandard.class, message = "课件ID不能为空")
    @Positive(groups = StoreCoursewareDataValidGroup.RestoreStandard.class, message = "课件ID错误")
    @Schema(description = "关联课件ID", requiredMode = Schema.RequiredMode.REQUIRED)
    private Integer coursewareId;

    /**
     * 关联课件内容ID
     */
    @NotNull(groups = StoreCoursewareDataValidGroup.RestoreStandard.class, message = "课件内容ID不能为空")
    @Positive(groups = StoreCoursewareDataValidGroup.RestoreStandard.class, message = "课件内容ID错误")
    @Schema(description = "关联课件内容ID", requiredMode = Schema.RequiredMode.REQUIRED)
    private Integer coursewareDataId;
}
