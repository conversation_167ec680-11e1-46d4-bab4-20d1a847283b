package com.yuedu.teaching.api.feign;

import com.yuedu.teaching.vo.CourseVersionVO;
import com.yuedu.ydsf.common.core.constant.ServiceNameConstants;
import com.yuedu.ydsf.common.core.util.R;
import com.yuedu.ydsf.common.feign.annotation.NoToken;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

/**
 * @ClassName RemoteCourseService
 * @Description courseService feign调用
 * <AUTHOR>
 * @Date 2024/11/26 08:59
 * @Version v0.0.1
 */
@FeignClient(contextId = "remoteCourseVersionService", value = ServiceNameConstants.TEACHING_SERVICE)
public interface RemoteCourseVersionService {

    /**
     * 通过课节Id列表查询课件版本信息
     *
     * @param ids id列表
     * @return List<CoursewareVO>
     */
    @PostMapping("/courseVersion/listCourseByIds")
    @NoToken
    R<List<CourseVersionVO>> listCoursewareByIds(@RequestBody List<Long> ids);
}
