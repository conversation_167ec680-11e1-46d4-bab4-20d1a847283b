package com.yuedu.teaching.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import java.time.LocalDateTime;

/**
* 门店教学环节表
*
* <AUTHOR>
* @date  2025/08/05
*/
@Data
@Schema(description = "门店教学环节表展示对象")
public class StoreCoursewareStepVO {


    /**
     * 主键id
     */
    @Schema(description = "主键id")
    private Integer id;

    /**
     * 原step表id
     */
    @Schema(description = "原step表id")
    private Integer stepId;

    /**
     * 关联课件ID
     */
    @Schema(description = "关联课件ID")
    private Integer coursewareId;

    /**
     * 课件内容Id
     */
    @Schema(description = "课件内容Id")
    private Integer coursewareDataId;

    /**
     * 课件名称
     */
    @Schema(description = "课件名称")
    private String stepName;

    /**
     * 父ID
     */
    @Schema(description = "父ID")
    private Integer stepParent;

    /**
     * 排序
     */
    @Schema(description = "排序")
    private Integer stepOrder;

    /**
     * 版本
     */
    @Schema(description = "版本")
    private Integer version;

    /**
     * 环节类型，1是环节，2是页面
     */
    @Schema(description = "环节类型，1是环节，2是页面")
    private Byte type;

    /**
     * 教学页模版ID
     */
    @Schema(description = "教学页模版ID")
    private Integer pageTemplateId;

    /**
     * 门店id
     */
    @Schema(description = "门店id")
    private Integer storeId;

    /**
     * 校区id
     */
    @Schema(description = "校区id")
    private Integer schoolId;

    /**
     * 所属者(副本创建人)
     */
    @Schema(description = "所属者(副本创建人)")
    private Long owner;

    /**
     * 是否删除
     */
    @Schema(description = "是否删除")
    private Byte delFlag;

    /**
     * 创建人
     */
    @Schema(description = "创建人")
    private String createBy;

    /**
     * 创建时间
     */
    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    /**
     * 修改人
     */
    @Schema(description = "修改人")
    private String updateBy;

    /**
     * 修改时间
     */
    @Schema(description = "修改时间")
    private LocalDateTime updateTime;

}
