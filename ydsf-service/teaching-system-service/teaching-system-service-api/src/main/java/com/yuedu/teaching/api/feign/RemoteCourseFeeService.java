package com.yuedu.teaching.api.feign;

import com.yuedu.teaching.dto.CourseFeeDTO;
import com.yuedu.teaching.dto.TimetableCourseFeeDTO;
import com.yuedu.teaching.query.TimetableCourseFeeQuery;
import com.yuedu.ydsf.common.core.constant.ServiceNameConstants;
import com.yuedu.ydsf.common.core.util.R;
import com.yuedu.ydsf.common.feign.annotation.NoToken;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;
import java.util.Map;

/**
 * TODO
 *
 * @author: KL
 * @date: 2025/05/27
 **/
@FeignClient(contextId = "remoteCourseFeeService", value = ServiceNameConstants.TEACHING_SERVICE)
public interface RemoteCourseFeeService {



    /**
     *  根据校区添加课时费
     *
     * <AUTHOR>
     * @date 2025年05月27日 14时29分
     */
    @PostMapping("/courseFee/addCourseFeeByStore")
    @NoToken
    R addCourseFeeByStore(@RequestBody CourseFeeDTO courseFeeDTO);

    /**
     * 查询课次费列表
     */
    @PostMapping("/courseFee/inner/timetable/courseFeeList")
    @NoToken
    R<Map<Long, CourseFeeDTO>> getCourseFeeListByTimetable(@RequestBody TimetableCourseFeeQuery timetableCourseFeeQuery);

    /**
     * 查询课次费列表
     */
    @PostMapping("/courseFee/inner/timetable/batchCourseFeeList")
    @NoToken
    R<Map<Long, Map<Long, CourseFeeDTO>>> listCourseFeeListByTimetable(@RequestBody List<TimetableCourseFeeQuery> timetableCourseFeeQueryList);
}
