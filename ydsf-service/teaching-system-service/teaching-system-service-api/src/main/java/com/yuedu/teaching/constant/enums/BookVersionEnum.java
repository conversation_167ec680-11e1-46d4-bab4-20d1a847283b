package com.yuedu.teaching.constant.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * @ClassName BookVersionEnum
 * @Description 书籍版本枚举
 * <AUTHOR>
 * @Date 2024/10/28 09:57
 * @Version v0.0.1
 */

@Getter
@AllArgsConstructor
public enum BookVersionEnum {
    /**
     * 逻辑删除标记，1为已删除
     */
    IS_DEL(1, "已删除"),

    /**
     * 逻辑删除标记，0为未删除
     */
    NOT_DEL(0, "未删除");

    private final int code;

    private final String desc;
}
