package com.yuedu.teaching.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2024/10/24
 **/
@Data
public class CourseVO {
    /**
     * id
     */
    @Schema(description = "id")
    private Integer id;

    /**
     * 课程编码
     */
    @Schema(description = "课程编码")
    private String courseCode;

    /**
     * 课程产品名称
     */
    @Schema(description = "课程产品名称")
    private String courseName;

    /**
     * 阶段ID
     */
    @Schema(description = "阶段ID")
    private Integer stageId;

    /**
     * 课节数量
     */
    @Schema(description = "课节数量")
    private Integer lessonCount;

    /**
     * 发布状态
     */
    @Schema(description = "发布状态")
    private Integer publishStatus;

    /**
     * 是否停用(0未停用，1停用)
     */
    @Schema(description = "是否停用(0未停用，1停用)")
    private Integer disable;

    /**
     * 版本
     */
    @Schema(description = "版本")
    private Integer version;

    /**
     * 发布记录
     */
    @Schema(description = "发布记录")
    private LocalDateTime publishTime;

    /**
     * 修改时间
     */
    @Schema(description = "修改时间")
    private LocalDateTime updateTime;

    /**
     * 课程类型名称
     */
    @Schema(description = "课程类型名称")
    private String courseTypeName;

    /**
     * 收费方式：0-门店设置；1-自定义
     */
    @Schema(description = "收费方式：0-门店设置；1-自定义")
    private Integer chargeMethod;

    /**
     * 课时费标准
     */
    @Schema(description = "课时费标准")
    private String courseFee;

    /**
     * 授权门店数量
     */
    @Schema(description = "授权门店数量")
    private Integer authStoreCount;

    /**
     * 课程类型id
     */
    @Schema(description = "课程类型id")
    private Long courseTypeId;
}
