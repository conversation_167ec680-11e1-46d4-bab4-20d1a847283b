package com.yuedu.teaching.query;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import java.time.LocalDateTime;

/**
 * 课程类型 查询类
 *
 * <AUTHOR>
 * @date 2025-05-21 08:57:32
 */
@Data
@Schema(description = "课程类型查询类")
public class CourseTypeQuery {


	/**
	* 自增主键
	*/
    @Schema(description="自增主键")
    private Integer id;

	/**
	* 课程类型名称
	*/
    @Schema(description="课程类型名称")
    private String name;

	/**
	* 课程类型编码
	*/
    @Schema(description="课程类型编码")
    private String code;

	/**
	* 状态：1可用，2停用
	*/
    @Schema(description="状态：1可用，2停用")
    private Integer status;

	/**
	* 创建人
	*/
    @Schema(description="创建人")
    private String createBy;

	/**
	* 创建时间
	*/
    @Schema(description="创建时间")
    private LocalDateTime createTime;

	/**
	* 修改人
	*/
    @Schema(description="修改人")
    private String updateBy;

	/**
	* 修改时间
	*/
    @Schema(description="修改时间")
    private LocalDateTime updateTime;

	/**
	* 是否删除: 0-否; 1-是;
	*/
    @Schema(description="是否删除: 0-否; 1-是;")
    private Integer delFlag;
}
