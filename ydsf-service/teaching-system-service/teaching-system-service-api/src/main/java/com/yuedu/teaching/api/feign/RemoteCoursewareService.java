package com.yuedu.teaching.api.feign;

import com.yuedu.teaching.dto.CoursewareInfoDTO;
import com.yuedu.teaching.dto.CoursewareVersionDTO;
import com.yuedu.teaching.vo.CoursewareInfoVO;
import com.yuedu.teaching.vo.CoursewareVO;
import com.yuedu.teaching.vo.CoursewareVersionVO;
import com.yuedu.ydsf.common.core.constant.ServiceNameConstants;
import com.yuedu.ydsf.common.core.util.R;
import com.yuedu.ydsf.common.feign.annotation.NoToken;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 * @ClassName RemoteCoursewareService
 * @Description 课件远程调用接口
 * <AUTHOR>
 * @Date 2024/12/3 15:48
 * @Version v0.0.1
 */
@FeignClient(contextId = "remoteCoursewareService", value = ServiceNameConstants.TEACHING_SERVICE)
public interface RemoteCoursewareService {
    /**
     * 根据版本id查询课件版本列表
     *
     * @param coursewareVersionDTO 版本id列表
     * @return R<List<CoursewareVersionVO>>
     */
    @PostMapping("/courseware/versionList")
    @NoToken
    R<List<CoursewareVersionVO>> getCoursewareVersionList(@RequestBody CoursewareVersionDTO coursewareVersionDTO);

    /**
     * 根据课件版本查询课件信息
     *
     * @param coursewareInfoDTO 课件版本列表
     * @return R<List<CoursewareInfoVO>>
     */
    @PostMapping("/courseware/infoList")
    @NoToken
    R<List<CoursewareInfoVO>> getCoursewareInfoList(@RequestBody CoursewareInfoDTO coursewareInfoDTO);

    /**
     * 根据id查询课程信息
     * @param id id
     * @return R<Courseware>
     */
    @GetMapping("/courseware/getById")
    @NoToken
    R<CoursewareVO> getById(@RequestParam Long id);
}
