package com.yuedu.teaching.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * @author: 张浩宇
 * @date: 2024/11/07
 **/
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CoursewareDataDetailsDTO {

    /**
     * 图片,word,pdf等路径
     */
    @Schema(description = "图片,word,pdf等路径")
    private String path;

    /**
     * 文件名
     */
    @Schema(description = "文件名")
    private String fileName;

    /**
     * 完整图片路径
     */
    @Schema(description = "完整图片路径")
    private String url;

    /**
     * 数组
     */
    @Schema(description = "数组")
    private String details;
}
