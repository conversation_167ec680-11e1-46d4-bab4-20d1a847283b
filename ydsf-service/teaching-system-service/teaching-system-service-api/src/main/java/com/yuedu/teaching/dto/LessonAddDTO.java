package com.yuedu.teaching.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;


/**
 * @ClassName LessonEntity
 * @Description 课程章节表
 * <AUTHOR>
 * @Date 2024/10/24 9:48
 * @Version v0.0.1
 */
@Data
public class LessonAddDTO {

    /**
     * 课程Id
     */
    @Schema(description = "课程id", requiredMode = Schema.RequiredMode.REQUIRED)
    private Integer courseId;


    /**
     * 课节名称
     */
    @Schema(description = "课节名称", requiredMode = Schema.RequiredMode.REQUIRED)
    private String lessonName;

    /**
     * 关联书籍
     */
    @Schema(description = "关联数据Id")
    private Integer bookId;

    /**
     * 关联课件
     */
    @Schema(description = "关联课件Id", requiredMode = Schema.RequiredMode.REQUIRED)
    private Integer coursewareId;

    /**
     * 课节类型，0：正式课， 1：试听课
     */
    @Schema(description = "课节类型", requiredMode = Schema.RequiredMode.REQUIRED)
    private Integer type;

    /**
     * 课件名称
     */
    @Schema(description = "课节名称", requiredMode = Schema.RequiredMode.REQUIRED)
    private String coursewareName;


    /**
     * 出版社
     */
    @Schema(description = "出版社名称", requiredMode = Schema.RequiredMode.REQUIRED)
    private String press;
}