package com.yuedu.teaching.constant.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 数据库操作类型枚举
 *
 * <AUTHOR>
 * @date 2024/10/25
 **/
@Getter
@AllArgsConstructor
public enum DatabaseOperationTypeEnum {
    /**
     * 插入
     */
    INSERT(1, "插入"),
    /**
     * 修改
     */
    UPDATE(2, "修改"),
    /**
     * 删除
     */
    DELETE(3, "删除");

    private final int code;

    private final String desc;
}
