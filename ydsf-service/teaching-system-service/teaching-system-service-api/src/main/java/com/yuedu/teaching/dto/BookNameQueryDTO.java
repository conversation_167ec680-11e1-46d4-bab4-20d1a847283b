package com.yuedu.teaching.dto;

import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;

/**
 * @author: 张浩宇
 * @date: 2024/10/29
 **/
@Data
@TableName("book_name")
@Schema(description = "书名表DTO")
public class BookNameQueryDTO {

    /**
     * 书名
     */
    @Schema(description = "书名")
    @NotBlank(message = "书名不能为空")
    private String title;

    /**
     * 作者
     */
    @Schema(description = "作者")
    private String author;

    /**
     * 阶段id
     */
    @Schema(description = "阶段Id")
    private Integer stageId;

    /**
     * 书名id
     */
    @Schema(description = "书名Id")
    private Integer bookId;
}
