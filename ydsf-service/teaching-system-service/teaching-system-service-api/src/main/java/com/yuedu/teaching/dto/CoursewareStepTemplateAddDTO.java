package com.yuedu.teaching.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Positive;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

/**
 * 教学环节模版表 实体类
 *
 * <AUTHOR>
 * @date 2024-11-05 17:32:03
 */
@Data
public class CoursewareStepTemplateAddDTO {
    /**
     * 课件模版名称
     */
    @Schema(description = "课件模版名称", requiredMode = Schema.RequiredMode.REQUIRED)
    @Length(max = 50, message = "课件模版名称长度不能超过50")
    @NotBlank(message = "课件模版名称不能为空")
    private String templateName;

    /**
     * 课件ID
     */
    @NotNull(message = "课件ID不能为空")
    @Positive(message = "课件ID错误")
    @Schema(description = "课件ID", requiredMode = Schema.RequiredMode.REQUIRED)
    private Integer coursewareId;

    /**
     * 课件内容Id
     */
    @NotNull(message = "课件内容ID不能为空")
    @Positive(message = "课件内容ID错误")
    @Schema(description = "课件内容ID", requiredMode = Schema.RequiredMode.REQUIRED)
    private Integer coursewareDataId;
}
