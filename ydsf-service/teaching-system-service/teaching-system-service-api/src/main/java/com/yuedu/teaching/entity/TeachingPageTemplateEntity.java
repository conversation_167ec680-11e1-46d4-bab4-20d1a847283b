package com.yuedu.teaching.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * 教学页模版表
 *
 * <AUTHOR>
 * @date 2024-11-05 16:39:34
 */
@Data
@TableName("teaching_page_template")
@EqualsAndHashCode(callSuper = true)
@Schema(description = "教学页模版表")
public class TeachingPageTemplateEntity extends Model<TeachingPageTemplateEntity> {

    /**
     * id
     */
    @TableId(type = IdType.AUTO)
    @Schema(description = "id")
    private Integer id;

    /**
     * 教学页模版名称
     */
    @Schema(description = "教学页模版名称")
    private String templateName;

    /**
     * 分类
     */
    @Schema(description = "分类")
    private Integer type;

    /**
     * 类型
     */
    @Schema(description = "类型")
    private Integer category;

    /**
     * 模版图片
     */
    @Schema(description = "图片/视频url")
    private String url;

    /**
     * 页面url
     */
    @Schema(description = "页面url")
    private String viewUrl;

    /**
     * 附属信息
     */
    @Schema(description = "配置信息")
    @JsonProperty("attr")
    private String attr;

    /**
     * 备注
     */
    @Schema(description = "备注")
    private String remark;

    /**
     * 排序
     */
    @Schema(description = "排序")
    private Integer pageOrder;

    /**
     * 是否启用，1 启用 0 禁用
     */
    @Schema(description = "是否启用，1 启用 0 禁用")
    private Integer enabled;

    /**
     * 创建人
     */
    @TableField(fill = FieldFill.INSERT)
    @Schema(description = "创建人")
    private String createBy;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    /**
     * 修改人
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    @Schema(description = "修改人")
    private String updateBy;

    /**
     * 修改时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    @Schema(description = "修改时间")
    private LocalDateTime updateTime;

    /**
     * 是否删除
     */
    @TableLogic
    @TableField(fill = FieldFill.INSERT)
    @Schema(description = "是否删除")
    private Integer delFlag;
}