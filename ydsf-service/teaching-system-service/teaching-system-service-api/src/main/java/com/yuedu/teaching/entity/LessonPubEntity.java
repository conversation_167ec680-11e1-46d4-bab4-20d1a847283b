package com.yuedu.teaching.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * 发版课程章节表
 *
 * <AUTHOR>
 * @date 2024-10-24 14:20:46
 */
@Data
@TableName("lesson_pub")
@EqualsAndHashCode(callSuper = true)
@Schema(description = "发版课程章节表")
public class LessonPubEntity extends Model<LessonPubEntity> {


    /**
     * id
     */
    @TableId(type = IdType.AUTO)
    @Schema(description = "id")
    private Integer id;

    /**
     * 课程章节ID
     */
    @Schema(description = "课程章节ID")
    private Long lessonId;

    /**
     * 课程Id
     */
    @Schema(description = "课程Id")
    private Long courseId;

    /**
     * 版本
     */
    @Schema(description = "版本")
    private Integer version;

    /**
     * 课程排序
     */
    @Schema(description = "课程排序")
    private Integer lessonOrder;

    /**
     * 课节名称
     */
    @Schema(description = "课节名称")
    private String lessonName;

    /**
     * 关联书籍
     */
    @Schema(description = "关联书籍")
    private Integer bookId;

    /**
     * 关联课件
     */
    @Schema(description = "关联课件")
    private Integer coursewareId;

    /**
     * 关联课件版本
     */
    @TableField(exist = false)
    private Integer coursewareVersion;

    /**
     * 是否生成录课任务，0:不生成 1：生成
     */
    @TableField(exist = false)
    private Integer genRecordTask;

    /**
     * 书籍名称
     */
    @TableField(exist = false)
    private String bookName;

    /**
     * 书籍封面
     */
    @TableField(exist = false)
    private String bookCover;

    /**
     * 课节类型，0：正式课， 1：试听课
     */
    @Schema(description = "课节类型，0：正式课， 1：试听课")
    private Integer type;

    /**
     * 发布状态，0:未发布 1：已发布
     */
    @Schema(description = "发布状态，0:未发布 1：已发布")
    private Integer publishStatus;

    /**
     * 是否可发布，0：不可发布，1:可发布
     */
    @Schema(description = "是否可发布，0：不可发布，1:可发布")
    private Integer canPublish;

    /**
     * 创建人
     */
    @Schema(description = "创建人")
    @TableField(fill = FieldFill.INSERT)
    private String createBy;

    /**
     * 创建时间
     */
    @Schema(description = "创建时间")
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 修改人
     */
    @Schema(description = "修改人")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private String updateBy;

    /**
     * 修改时间
     */
    @Schema(description = "修改时间")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    /**
     * 是否删除
     */
    @TableLogic
    @TableField(fill = FieldFill.INSERT)
    @Schema(description = "是否删除")
    private Integer delFlag;
}
