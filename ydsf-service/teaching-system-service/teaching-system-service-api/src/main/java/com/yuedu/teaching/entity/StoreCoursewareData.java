package com.yuedu.teaching.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import java.time.LocalDateTime;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 门店课件表
 * 
 * <AUTHOR>
 * @date 2025/08/05
 */
@TableName("store_courseware_data")
@Data
@EqualsAndHashCode(callSuper = true)
public class StoreCoursewareData extends Model<StoreCoursewareData> {
    /**
     * 主键id
     */
    @TableId(type = IdType.AUTO)
    private Integer id;

    /**
     * 资料表ID
     */
    private Integer coursewareDataId;

    /**
     * 版本(创建副本时总部课件的版本)
     */
    private Integer version;

    /**
     * 课件名称
     */
    private String coursewareName;

    /**
     * 关联课件ID
     */
    private Integer coursewareId;

    /**
     * 关联课件模版ID
     */
    private Integer dataTemplateId;

    /**
     * 类型 课件类型 与课件模版一致
     */
    private Integer type;

    /**
     * 详细数据
     */
    private Object details;

    /**
     * 门店id
     */
    private Integer storeId;

    /**
     * 校区id
     */
    private Integer schoolId;

    /**
     * 所属者(副本创建人)
     */
    private Long owner;

    /**
     * 是否使用:0-未使用;1-使用
     */
    private Integer isUse;

    /**
     * 是否删除
     */
    @TableLogic
    @TableField(fill = FieldFill.INSERT)
    private Integer delFlag;

    /**
     * 创建人
     */
    @TableField(fill = FieldFill.INSERT)
    private String createBy;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 修改人
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private String updateBy;

    /**
     * 修改时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;
}