package com.yuedu.teaching.constant;

/**
 * <AUTHOR>
 * @date 2019-04-28
 * <p>
 * 缓存的key 常量
 */
public interface CacheConstant {

    /**
     * 全局缓存，在缓存名称上加上该前缀表示该缓存不区分租户，比如:
     * <p/>
     * {@code @Cacheable(value = CacheConstants.GLOBALLY+CacheConstants.MENU_DETAILS, key = "#roleId  + '_menu'", unless = "#result == null")}
     */
    String GLOBALLY = "teaching:";

    /**
     * 课件
     */
    String COURSEWARE_STEP_PUB = "courseware_step_pub:%s-%s-%s";

    /**
     * 课件角色
     */
    String COURSEWARE_BOOK_ROLE = "courseware_book_role:%s";

    /**
     * 课节
     */
    String LESSON_PUB = "lesson_pub:%s-%s";
}
