package com.yuedu.teaching.dto;

import com.yuedu.ydsf.common.core.util.V_A_E;
import com.yuedu.ydsf.common.core.util.V_E;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
* 门店教学环节表
*
* <AUTHOR>
* @date  2025/08/05
*/
@Data
@Schema(description = "门店教学环节表传输对象")
public class StoreCoursewareStepDTO implements Serializable {


    /**
     * 主键id
     */
    @NotNull(groups = {V_E.class}, message = "主键id不能为空")
    private Integer id;



    /**
     * 原step表id
     */
    @Schema(description = "原step表id")
    private Integer stepId;

    /**
     * 关联课件ID
     */
    @Schema(description = "关联课件ID")
    @NotNull(groups = {V_A_E.class }, message = "关联课件ID不能为空")
    private Integer coursewareId;

    /**
     * 课件内容Id
     */
    @Schema(description = "课件内容Id")
    private Integer coursewareDataId;

    /**
     * 课件名称
     */
    @Schema(description = "课件名称")
    @NotBlank(groups = {V_A_E.class }, message = "课件名称不能为空")
    @Length(groups = {V_A_E.class }, max =255 ,message = "课件名称长度不能大于255")
    private String stepName;

    /**
     * 父ID
     */
    @Schema(description = "父ID")
    @NotNull(groups = {V_A_E.class }, message = "父ID不能为空")
    private Integer stepParent;

    /**
     * 排序
     */
    @Schema(description = "排序")
    @NotNull(groups = {V_A_E.class }, message = "排序不能为空")
    private Integer stepOrder;

    /**
     * 版本
     */
    @Schema(description = "版本")
    @NotNull(groups = {V_A_E.class }, message = "版本不能为空")
    private Integer version;

    /**
     * 环节类型，1是环节，2是页面
     */
    @Schema(description = "环节类型，1是环节，2是页面")
    @NotNull(groups = {V_A_E.class }, message = "环节类型，1是环节，2是页面不能为空")
    private Byte type;

    /**
     * 教学页模版ID
     */
    @Schema(description = "教学页模版ID")
    @NotNull(groups = {V_A_E.class }, message = "教学页模版ID不能为空")
    private Integer pageTemplateId;

    /**
     * 门店id
     */
    @Schema(description = "门店id")
    @NotNull(groups = {V_A_E.class }, message = "门店id不能为空")
    private Integer storeId;

    /**
     * 校区id
     */
    @Schema(description = "校区id")
    @NotNull(groups = {V_A_E.class }, message = "校区id不能为空")
    private Integer schoolId;

    /**
     * 所属者(副本创建人)
     */
    @Schema(description = "所属者(副本创建人)")
    @NotNull(groups = {V_A_E.class }, message = "所属者(副本创建人)不能为空")
    private Long owner;

    /**
     * 是否删除
     */
    @Schema(description = "是否删除")
    @NotNull(groups = {V_A_E.class }, message = "是否删除不能为空")
    private Byte delFlag;

    /**
     * 创建人
     */
    @Schema(description = "创建人")
    @Length(groups = {V_A_E.class }, max =255 ,message = "创建人长度不能大于255")
    private String createBy;

    /**
     * 创建时间
     */
    @Schema(description = "创建时间")
    @NotNull(groups = {V_A_E.class }, message = "创建时间不能为空")
    private LocalDateTime createTime;

    /**
     * 修改人
     */
    @Schema(description = "修改人")
    @Length(groups = {V_A_E.class }, max =255 ,message = "修改人长度不能大于255")
    private String updateBy;

    /**
     * 修改时间
     */
    @Schema(description = "修改时间")
    @NotNull(groups = {V_A_E.class }, message = "修改时间不能为空")
    private LocalDateTime updateTime;


}
