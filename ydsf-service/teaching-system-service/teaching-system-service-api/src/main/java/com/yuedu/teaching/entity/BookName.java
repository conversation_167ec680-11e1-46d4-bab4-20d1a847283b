package com.yuedu.teaching.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.yuedu.ydsf.common.core.util.ValidGroup;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.*;

import java.time.LocalDateTime;

/**
 * 书名表 实体类
 *
 * <AUTHOR>
 * @date 2024-10-24 08:57:33
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@TableName("book_name")
@EqualsAndHashCode(callSuper = true)
@Schema(description = "书名表实体类")
public class BookName extends Model<BookName> {


    /**
     * id
     */
    @TableId(type = IdType.AUTO)
    @Schema(description = "id")
    @NotNull(groups = ValidGroup.Update.class)
    private Integer id;

    /**
     * 书名
     */
    @Schema(description = "书名")
    @NotNull(groups = ValidGroup.Update.class)
    private String title;

    /**
     * 作者
     */
    @Schema(description = "作者")
    @NotNull(groups = ValidGroup.Update.class)
    private String author;

    /**
     * 书籍版本数
     */
    @Schema(description = "书籍版本数")
    private Integer versionCount;

    /**
     * 课件数量
     */
    @Schema(description = "课件数量")
    private Integer coursewareCount;

    /**
     * 创建人
     */
    @Schema(description = "创建人")
    @TableField(fill = FieldFill.INSERT)
    private String createBy;

    /**
     * 创建时间
     */
    @Schema(description = "创建时间")
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 修改人
     */
    @Schema(description = "修改人")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private String updateBy;

    /**
     * 修改时间
     */
    @Schema(description = "修改时间")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    /**
     * 是否删除
     */
    @TableLogic
    @Schema(description = "是否删除")
    private Integer delFlag;
}
