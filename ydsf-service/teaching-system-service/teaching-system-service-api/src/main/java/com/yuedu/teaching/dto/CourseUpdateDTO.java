package com.yuedu.teaching.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/10/24
 **/

@Data
public class CourseUpdateDTO {

    /**
     * 课程ID
     */
    @Schema(description = "id")
    @NotNull(message = "id不能为空")
    private Integer id;

    /**
     * 课程产品编码
     */
    @Schema(description = "课程产品编码")
    @NotBlank(message = "课程编码不能为空")
    private String courseCode;

    /**
     * 课程产品名称
     */
    @Schema(description = "课程产品名称")
    @Length(max = 50, message = "课程产品名称长度不能超过50")
    private String courseName;

    /**
     * 阶段ID
     */
    @Schema(description = "阶段ID")
    private Integer stageId;

    /**
     * 课节数量
     */
    @Schema(description = "课节数量")
    private Integer lessonCount;

    /**
     * 更新类型，1：添加 3：删除
     */
    private Integer updateType;

    /**
     * 授权门店ID集合
     */
    @Schema(description = "授权门店ID集合")
    private List<Long> authStoreIds;


    /**
     * 收费方式：0-门店设置；1-自定义
     */
    @Schema(description = "收费方式：0-门店设置；1-自定义")
    private Integer chargeMethod;

    /**
     * 课时费标准
     */
    @Schema(description = "课时费标准")
    private String courseFee;

    /**
     * 课时费标准
     */
    @Schema(description = "课时费标准")
    private String customizeFee;

    /**
     * 授权门店数量
     */
    @Schema(description = "授权门店数量")
    private Integer authStoreCount;

    /**
     * 课程类型id
     */
    @Schema(description = "课程类型id")
    private Long courseTypeId;
}
