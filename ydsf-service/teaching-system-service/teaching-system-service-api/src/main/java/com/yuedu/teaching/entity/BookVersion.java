package com.yuedu.teaching.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * 书籍版本 实体类
 *
 * <AUTHOR>
 * @date 2024-10-24 08:43:58
 */
@Data
@TableName("book_version")
@EqualsAndHashCode(callSuper = true)
@Schema(description = "书籍版本实体类")
public class BookVersion extends Model<BookVersion> {


    /**
     * id
     */
    @TableId(type = IdType.AUTO)
    @Schema(description = "id")
    private Integer id;

    /**
     * 书籍ID
     */
    @Schema(description = "书籍ID")
    private Integer bookId;

    /**
     * ISBN号
     */
    @Schema(description = "ISBN号")
    private String isbn;

    /**
     * 出版社
     */
    @Schema(description = "出版社")
    private String press;

    /**
     * 译者
     */
    @Schema(description = "译者")
    private String translator;

    /**
     * 来源
     */
    @Schema(description = "来源")
    private Integer source;

    /**
     * 封面图片
     */
    @Schema(description = "封面图片")
    private String cover;

    /**
     * 创建人
     */
    @Schema(description = "创建人")
    @TableField(fill = FieldFill.INSERT)
    private String createBy;

    /**
     * 创建时间
     */
    @Schema(description = "创建时间")
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 修改人
     */
    @Schema(description = "修改人")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private String updateBy;

    /**
     * 修改时间
     */
    @Schema(description = "修改时间")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    /**
     * 是否删除（0为不删除，1为删除）
     */
    @TableLogic
    @Schema(description = "是否删除（0为不删除，1为删除）")
    private Integer delFlag;
}
