package com.yuedu.teaching.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * 阶段/书单
 *
 * <AUTHOR>
 * @date 2024-10-24 10:45:18
 */
@Data
@TableName("stage")
@EqualsAndHashCode(callSuper = true)
@Schema(description = "阶段/书单")
public class StageEntity extends Model<StageEntity> {


    /**
     * id
     */
    @TableId(type = IdType.AUTO)
    @Schema(description = "id")
    private Integer id;

    /**
     * 名称
     */
    @Schema(description = "名称")
    private String stageName;

    /**
     * 描述
     */
    @Schema(description = "描述")
    private String description;

    /**
     * 排序字段
     */
    @Schema(description = "排序字段")
    private Integer stageOrder;

    /**
     * 创建人
     */
    @Schema(description = "创建人")
    private String createBy;

    /**
     * 创建时间
     */
    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    /**
     * 修改人
     */
    @Schema(description = "修改人")
    private String updateBy;

    /**
     * 修改时间
     */
    @Schema(description = "修改时间")
    private LocalDateTime updateTime;

    /**
     * 是否删除
     */
    @TableLogic
    @TableField(fill = FieldFill.INSERT)
    @Schema(description = "是否删除")
    private Integer delFlag;

    /**
     * 类型1双师
     */
    @TableField(fill = FieldFill.INSERT)
    @Schema(description = "类型")
    private Integer productId;
}