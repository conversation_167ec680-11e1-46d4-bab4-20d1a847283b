package com.yuedu.teaching.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
* 课程授权门店表
*
* <AUTHOR>
* @date  2025/05/21
*/
@Data
@Schema (description = "课程授权门店表展示对象")
public class CourseAuthStoreVO {


    /**
     * 主键id
     */
    @Schema(description = "主键id")
    private Long id;

    /**
     * 课程表id
     */
    @Schema(description = "课程表id")
    private Long courseId;

    /**
     * 门店id
     */
    @Schema(description = "门店id")
    private Long storeId;

    /**
     * 创建人
     */
    @Schema(description = "创建人")
    private String createBy;

    /**
     * 创建时间
     */
    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    /**
     * 修改人
     */
    @Schema(description = "修改人")
    private String updateBy;

    /**
     * 修改时间
     */
    @Schema(description = "修改时间")
    private LocalDateTime updateTime;

    /**
     * 自定义课时费标准
     */
    @Schema(description = "自定义课时费标准")
    private BigDecimal customizeFee;
    /**
     * 课程类型关联id
     */
    @Schema(description = "课程类型关联id")
    private Long courseTypeId;
    /**
     * 课程最新版本
     */
    @Schema(description = "课程最新版本")
    private Long version;

    /**
     * 课程名称
     */
    @Schema(description = "课程名称")
    private String courseName;

    /**
     * 课程类型名称
     */
    @Schema(description = "课程类型名称")
    private String courseTypeName;

    /**
     * 收费方式:0-门店设置;1-自定义
     */
    @Schema(description = "收费方式:0-门店设置;1-自定义",type = "charge_method")
    private Integer chargeMethod;

    /**
     *  生效日期
     */
    @Schema(description = "生效日期")
    private LocalDate effectiveDate;

}
