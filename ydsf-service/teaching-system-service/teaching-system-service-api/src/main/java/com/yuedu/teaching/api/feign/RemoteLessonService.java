package com.yuedu.teaching.api.feign;

import com.yuedu.teaching.dto.LessonNameDTO;
import com.yuedu.teaching.dto.LessonOrderDTO;
import com.yuedu.teaching.entity.LessonEntity;
import com.yuedu.teaching.query.CoursePublishQuery;
import com.yuedu.teaching.vo.LessonCoursewareVO;
import com.yuedu.teaching.vo.LessonNameVO;
import com.yuedu.teaching.vo.LessonVO;
import com.yuedu.ydsf.common.core.constant.ServiceNameConstants;
import com.yuedu.ydsf.common.core.util.R;
import com.yuedu.ydsf.common.feign.annotation.NoToken;
import io.swagger.v3.oas.annotations.Operation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

/**
 * @ClassName RemoteLessonService
 * @Description lessonService feign调用
 * <AUTHOR>
 * @Date 2024/11/26 09:02
 * @Version v0.0.1
 */
@FeignClient(contextId = "remoteLessonService", value = ServiceNameConstants.TEACHING_SERVICE)
public interface RemoteLessonService {
    /**
     * 根据课程id获取已发布课节列表
     *
     * @param coursePublishQuery 内含课程id
     * @return R<List < LessonVO>>
     */
    @PostMapping("/lesson/pubList")
    @NoToken
    R<List<LessonVO>> getPublishLessonList(@RequestBody CoursePublishQuery coursePublishQuery);

    /**
     * 根据课节名称获取已发布的课节列表
     *
     * @param coursePublishQuery 课节名称
     * @return R<List<LessonEntity>>
     */
    @PostMapping("/lesson/pubListByName")
    @NoToken
    R<List<LessonEntity>> getLessonByName(@RequestBody CoursePublishQuery coursePublishQuery);

    /**
     * 通过id列表查询课件名列表
     *
     * @param ids id列表
     * @return List<LessonNameVO>
     */
    @PostMapping("/lesson/listLessonNameByIds")
    @NoToken
    R<List<LessonNameVO>> listLessonNameByIds(@RequestBody List<Long> ids);

    /**
     * 通过Id列表查询课件Id和课件名称
     *
     * @param ids id列表
     * @return List<LessonCoursewareVO>
     */
    @PostMapping("/lesson/listCoursewareByIds")
    @NoToken
    R<List<LessonCoursewareVO>> listCoursewareByIds(@RequestBody List<Integer> ids);

    /**
     * 通过id列表获取已发布课节列表
     *
     * @param coursePublishQuery 课节id列表
     * @return R<List<LessonVO>>
     */
    @PostMapping("/lesson/pubListById")
    @NoToken
    R<List<LessonVO>> getPublishLessonListById(@RequestBody CoursePublishQuery coursePublishQuery);

    /**
     * 获取课节名称列表
     *
     * @param lessonNameDTO 版本id集合
     * @return R<List<LessonNameVO>>
     */
    @PostMapping("/lesson/lessonNameList")
    @NoToken
    R<List<LessonNameVO>> getLessonNameList(@RequestBody LessonNameDTO lessonNameDTO);

    /**
     * 根据课程id和课节排序获取课节列表
     *
     * @param lessonOrderDTOList 根据课程id和课节排序DTO
     * @return R<List<LessonVO>>
     */
    @Operation(summary = "根据课程id和课节排序获取课节列表",description = "根据课程id和课节排序获取课节列表")
    @PostMapping("/lesson/lessonListByOrder")
    @NoToken
    R<List<LessonVO>> getLessonListByOrder(@RequestBody List<LessonOrderDTO> lessonOrderDTOList);
}
