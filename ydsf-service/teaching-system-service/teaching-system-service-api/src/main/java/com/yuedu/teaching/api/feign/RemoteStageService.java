package com.yuedu.teaching.api.feign;

import com.yuedu.teaching.vo.StageVO;
import com.yuedu.ydsf.common.core.constant.ServiceNameConstants;
import com.yuedu.ydsf.common.core.util.R;
import com.yuedu.ydsf.common.feign.annotation.NoToken;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;
import java.util.List;

/**
 * 阶段
 *
 * <AUTHOR>
 * @date 2024/12/12 15:24
 */
@FeignClient(contextId = "remoteStageService", value = ServiceNameConstants.TEACHING_SERVICE)
public interface RemoteStageService {

    /**
     * 查询双师阶段
     *
     * @param productId
     * @return com.yuedu.ydsf.common.core.util.R<java.util.List < com.yuedu.teaching.vo.StageVO>>
     * <AUTHOR>
     * @date 2024/12/12 15:31
     */
    @GetMapping("/stage/getStageList")
    @NoToken
    R<List<StageVO>> getStageList(@RequestParam Integer productId);

}
