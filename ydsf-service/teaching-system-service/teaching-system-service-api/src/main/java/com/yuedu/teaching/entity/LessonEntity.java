package com.yuedu.teaching.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;


/**
 * @ClassName LessonEntity
 * @Description 课程章节表
 * <AUTHOR>
 * @Date 2024/10/24 9:48
 * @Version v0.0.1
 */
@Data
@TableName("lesson")
@EqualsAndHashCode(callSuper = true)
@Schema(description = "课程章节表")
public class LessonEntity extends Model<LessonEntity> {


    /**
     * id
     */
    @TableId(type = IdType.AUTO)
    @Schema(description = "id")
    private Long id;

    /**
     * 课程Id
     */
    @Schema(description = "courseId")
    private Long courseId;

    /**
     * 课程排序
     */
    @Schema(description = "课程排序")
    private Integer lessonOrder;

    /**
     * 课节名称
     */
    @NotNull(message = "课节名称不能为空!")
    @Schema(description = "课节名称")
    private String lessonName;

    /**
     * 关联书籍
     */
    @Schema(description = "关联书籍")
    private Integer bookId;

    /**
     * 关联课件
     */
    @Schema(description = "关联课件")
    private Integer coursewareId;

    /**
     * 课节类型，0：正式课， 1：试听课
     */
    @NotNull(message = "课节类型不能为空!")
    @Schema(description = "课节类型，0：正式课， 1：试听课")
    private Integer type;

    /**
     * 发布状态，0:未发布 1：已发布
     */
    @Schema(description = "发布状态，0:未发布 1：已发布")
    private Integer publishStatus;

    /**
     * 是否可发布，0：不可发布，1:可发布
     */
    @Schema(description = "是否可发布，0：不可发布，1:可发布")
    private Integer canPublish;

    /**
     * 课件名称
     */
    @Schema(description = "课件名称")
    @TableField(exist = false)
    private String coursewareName;


    /**
     * 出版社
     */
    @Schema(description = "出版社名称")
    private String press;

    /**
     * 创建人
     */
    @Schema(description = "创建人")
    @TableField(fill = FieldFill.INSERT)
    private String createBy;

    /**
     * 创建时间
     */
    @Schema(description = "创建时间")
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 修改人
     */
    @Schema(description = "修改人")
    @TableField(fill = FieldFill.UPDATE)
    private String updateBy;

    /**
     * 修改时间
     */
    @Schema(description = "修改时间")
    @TableField(fill = FieldFill.UPDATE)
    private LocalDateTime updateTime;

    /**
     * 是否删除
     */
    @TableLogic
    @TableField(fill = FieldFill.INSERT)
    @Schema(description = "是否删除")
    private Integer delFlag;
}