package com.yuedu.teaching.query;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
*@ClassName CoursewareQuery
*@Description 课件查询类
*<AUTHOR>
*@Date 2024/11/19 15:55
*@Version v0.0.1 
*/

@Data
public class CoursewareQuery {
    /**
     * id
     */
    @Schema(description = "id")
    @NotNull(message = "课件id不能为空")
    private Integer id;
}
