package com.yuedu.teaching.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * @ClassName CoursewareVO
 * @Description 课件VO
 * <AUTHOR>
 * @Date 2024/11/8 09:38
 * @Version v0.0.1
 */

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class CoursewareVO {
    /**
     * id
     */
    @Schema(description = "id")
    private Integer id;

    /**
     * 课件名称
     */
    @Schema(description = "课件名称")
    private String coursewareName;

    /**
     * 关联书籍
     */
    @Schema(description = "关联书籍")
    private Integer bookId;

    /**
     * 课节Id
     */
    @Schema(description = "课节Id")
    private Long lessonId;

    /**
     * 关联课件
     */
    @Schema(description = "关联书籍版本")
    private Integer bookVersionId;

    /**
     * 发布状态，0:未发布 1：已发布
     */
    @Schema(description = "发布状态，0:未发布 1：已发布")
    private Integer publishStatus;

    /**
     * 出版社
     */
    @Schema(description = "出版社")
    private String press;

    /**
     * 回显版本
     */
    @Schema(description = "回显版本")
    private Integer version;

    /**
     * 创建人
     */
    @Schema(description="创建人")
    private String createBy;

    /**
     * 版本发布时间
     */
    @Schema(description="版本发布时间")
    private LocalDateTime publishTime;

    /**
     * 修改时间
     */
    @Schema(description="修改时间")
    private LocalDateTime updateTime;

    /**
     * 是否首次发布
     */
    @Schema(description="修改时间")
    private Integer isFirstPublish;
}
