package com.yuedu.teaching.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

/**
 * <AUTHOR>
 * @date 2024/10/24
 **/

@Data
public class CourseQueryDTO {
    /**
     * id
     */
    @Schema(description = "id")
    @NotNull(message = "id不能为空")
    private Long id;

    /**
     * 课程产品名称
     */
    @Schema(description = "课程产品名称")
    @Length(max = 50, message = "课程产品名称长度不能超过50")
    private String courseName;

    /**
     * 阶段ID
     */
    @Schema(description = "阶段ID")
    private Integer stageId;

    @Schema(description = "是否停用")
    private Integer disable;

    @Schema(description = "是否发布")
    private Integer publishStatus;

    /**
     * 课程类型ID
     */
    @Schema(description = "课程类型ID")
    private Long courseTypeId;

    /**
     * 门店ID
     */
    @Schema(description = "门店ID")
    private Long storeId;
}
