package com.yuedu.teaching.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * @ClassName CourseWareDataStepDetailsVO
 * <AUTHOR>
 * @Date 2024/11/6 16:38
 */
@Data
public class CoursewareDataStepDetailsVO {

    /**
     * 关联课件ID
     */
    @Schema(description = "关联课件ID")
    private Integer coursewareId;

    /**
     * 关联课件内容ID
     */
    @Schema(description = "关联课件内容ID")
    private Integer coursewareDataId;

    /**
     * 教学页所属环节ID
     */
    @Schema(description = "教学页所属环节ID")
    private Integer stepId;


    /**
     * 详细数据
     */
    @Schema(description = "详细数据")
    private Object details;

    /**
     * 跳转页
     */
    @Schema(description = "跳转页")
    private String viewUrl;

    /**
     * 教学页模版ID
     */
    @Schema(description = "教学页模版ID")
    private Integer pageTemplateId;

    /**
     * 教学页分类
     */
    @Schema(description = "教学页分类")
    private Integer teachingPageCategory;

    /**
     * 教学页类型
     */
    @Schema(description = "教学页类型")
    private Integer teachingPageType;

    /**
     * 工具
     */
    @Schema(description = "工具")
    private Object tool;

    /**
     * 模版页备注
     */
    @Schema(description = "模版页备注")
    private String remark;

    /**
     * 环节名称
     */
    @Schema(description = "环节名称")
    private String stepName;
}
