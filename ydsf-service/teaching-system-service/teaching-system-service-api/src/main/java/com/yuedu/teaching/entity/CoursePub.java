package com.yuedu.teaching.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 发版课程表 实体类
 *
 * <AUTHOR>
 * @date 2024-10-24 09:08:15
 */
@Data
@TableName("course_pub")
@EqualsAndHashCode(callSuper = true)
@Schema(description = "发版课程表实体类")
public class CoursePub extends Model<CoursePub> {


    /**
     * id
     */
    @TableId(type = IdType.AUTO)
    @Schema(description = "id")
    private Integer id;

    /**
     * 课程Id
     */
    @Schema(description = "course_id")
    private Integer courseId;

    /**
     * 版本
     */
    @Schema(description = "版本")
    private Integer version;

    /**
     * 课程编码
     */
    @Schema(description = "课程编码")
    private String courseCode;

    /**
     * 课程产品名称
     */
    @Schema(description = "课程产品名称")
    private String courseName;

    /**
     * 阶段ID
     */
    @Schema(description = "阶段ID")
    private Integer stageId;

    /**
     * 课节数量
     */
    @Schema(description = "课节数量")
    private Integer lessonCount;

    /**
     * 发布状态
     */
    @Schema(description = "发布状态")
    private Integer publishStatus;

    /**
     * 创建人
     */
    @TableField(fill = FieldFill.INSERT)
    @Schema(description = "创建人")
    private String createBy;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    /**
     * 修改人
     */
    @Schema(description = "修改人")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private String updateBy;

    /**
     * 修改时间
     */
    @Schema(description = "修改时间")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    /**
     * 是否删除
     */
    @TableLogic
    @TableField(fill = FieldFill.INSERT)
    @Schema(description = "是否删除")
    private Integer delFlag;

    /**
     * 课程类型关联id
     */
    @Schema(description = "课程类型关联id")
    private Long courseTypeId;

    /**
     * 收费方式:0-门店设置;1-自定义
     */
    @Schema(description = "收费方式:0-门店设置;1-自定义")
    private Integer chargeMethod;

    /**
     * 自定义课时费标准(收费方式为自定义时设置)
     */
    @Schema(description = "自定义课时费标准(收费方式为自定义时设置)")
    private BigDecimal customizeFee;
}
