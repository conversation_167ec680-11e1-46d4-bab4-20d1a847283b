package com.yuedu.teaching.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * @ClassName CoursewareVO
 * @Description 课件VO
 * <AUTHOR>
 * @Date 2024/11/8 09:38
 * @Version v0.0.1
 */

@Data
@AllArgsConstructor
@NoArgsConstructor
@ToString
public class LessonCoursewareVO {
    /**
     * lessonId
     */
    @Schema(description = "lessonId")
    private Integer lessonId;

    /**
     * id
     */
    @Schema(description = "id")
    private Integer id;

    /**
     * 课件名称
     */
    @Schema(description = "课件名称")
    private String coursewareName;
}
