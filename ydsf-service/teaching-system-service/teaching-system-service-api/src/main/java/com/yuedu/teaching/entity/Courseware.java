package com.yuedu.teaching.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

import java.time.LocalDateTime;

/**
 * 课件表 实体类
 *
 * <AUTHOR>
 * @date 2024-11-05 15:05:46
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName("courseware")
@EqualsAndHashCode(callSuper = true)
@Schema(description = "课件表实体类")
public class Courseware extends Model<Courseware> {


    /**
     * id
     */
    @TableId(type = IdType.AUTO)
    @Schema(description = "id")
    private Integer id;

    /**
     * 课件名称
     */
    @Schema(description = "课件名称")
    private String coursewareName;

    /**
     * 关联书籍
     */
    @Schema(description = "关联书籍")
    private Integer bookId;

    /**
     * 关联课件
     */
    @Schema(description = "关联书籍版本")
    private Integer bookVersionId;

    /**
     * 发布状态，0:未发布 1：已发布
     */
    @Schema(description = "发布状态，0:未发布 1：已发布")
    private Integer publishStatus;

    /**
     * 课节Id
     */
    @TableField(exist = false)
    @Schema(description = "课节Id")
    private Long lessonId;

    /**
     * 回显版本
     */
    @Schema(description = "回显版本")
    private Integer version;

    /**
     * 版本发布时间
     */
    @Schema(description = "版本发布时间")
    private LocalDateTime publishTime;

    /**
     * 创建人
     */
    @Schema(description = "创建人")
    @TableField(fill = FieldFill.INSERT)
    private String createBy;

    /**
     * 创建时间
     */
    @Schema(description = "创建时间")
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 修改人
     */
    @Schema(description = "修改人")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private String updateBy;

    /**
     * 修改时间
     */
    @Schema(description = "修改时间")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    /**
     * 是否删除
     */
    @TableLogic
    @TableField(fill = FieldFill.INSERT)
    @Schema(description = "是否删除")
    private Integer delFlag;
}
