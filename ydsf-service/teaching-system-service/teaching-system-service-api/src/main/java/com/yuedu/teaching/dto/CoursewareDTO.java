package com.yuedu.teaching.dto;

import com.yuedu.teaching.valid.CoursewareValidGroup;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * @ClassName CoursewareDTO
 * @Description 课件DTO
 * <AUTHOR>
 * @Date 2024/11/5 15:15
 * @Version v0.0.1
 */

@Data
@Schema(description = "课件表DTO")
public class CoursewareDTO {
    /**
     * 课件id
     */
    @Schema(description="id")
    @NotNull(groups = {CoursewareValidGroup.UpdateCoursewareGroup.class},message = "id不能为空")
    private Integer id;

    /**
     * 课件名称
     */
    @Schema(description="课件名称")
    @NotBlank(groups = {CoursewareValidGroup.AddCoursewareGroup.class,CoursewareValidGroup.UpdateCoursewareGroup.class},message = "课件名称不能为空")
    private String coursewareName;

    /**
     * 关联课件
     */
    @Schema(description="关联书籍版本")
    @NotNull(groups = {CoursewareValidGroup.AddCoursewareGroup.class},message = "图书版本不能为空")
    private Integer bookVersionId;

    /**
     * 关联书籍
     */
    @Schema(description="关联书籍")
    @NotNull(groups = {CoursewareValidGroup.AddCoursewareGroup.class,CoursewareValidGroup.ListByNameGroup.class},message = "书籍id不能为空")
    private Integer bookId;

    @Schema(description = "是否生成录课任务，0:不生成 1：生成")
    private Integer genRecordTask;

    /**
     * 回显版本
     */
    @Schema(description = "回显版本")
    private Integer version;
}
