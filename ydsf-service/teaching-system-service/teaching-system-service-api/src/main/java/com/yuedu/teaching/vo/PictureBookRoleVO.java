package com.yuedu.teaching.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;

/**
 * 绘本角色表 VO
 *
 * <AUTHOR>
 * @date 2025/1/3 15:07
 */
@Data
public class PictureBookRoleVO implements Serializable {
    /**
     * id
     */
    @Schema(description = "id")
    private Integer id;

    /**
     * 书籍ID
     */
    @Schema(description = "书籍ID")
    private Integer bookId;

    /**
     * 角色名
     */
    @Schema(description = "角色名")
    private String roleName;

    /**
     * 缩略图
     */
    @Schema(description = "缩略图")
    private String url;

    @Schema(description = "头像")
    private Avatar avatar;

    @Data
    @Schema(description = "角色头像")
    public static class Avatar {
        /**
         * 相对路径
         */
        @Schema(description = "相对路径")
        private String relativePath;

        /**
         * 完整路径
         */
        @Schema(description = "完整路径")
        private String fullPath;
    }
}
