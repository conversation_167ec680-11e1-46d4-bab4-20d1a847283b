package com.yuedu.teaching.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * 文件md5提报
 *
 * <AUTHOR>
 * @date 2024-12-10 08:34:33
 */
@Data
@Schema(description = "文件提报DTO")
public class FileSubmitDTO {

    /**
     * 后缀
     */
    @Schema(description = "url")
    @NotNull(message = "url")
    private String url;

    /**
     * content-md5
     */
    @Schema(description = "content-md5")
    private String contentMd5;
}
