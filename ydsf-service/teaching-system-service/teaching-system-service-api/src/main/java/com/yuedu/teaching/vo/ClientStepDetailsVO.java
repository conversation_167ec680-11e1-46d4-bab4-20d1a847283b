package com.yuedu.teaching.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 播放器环节详情
 *
 * <AUTHOR>
 * @date 2024-11-11 15:54:03
 */
@Data
public class ClientStepDetailsVO {

    /**
     * 环节名称
     */
    @Schema(description = "环节名称")
    private String stepName;

    /**
     * 环节类型
     */
    @Schema(description = "环节类型")
    private String stepType;

    /**
     * 下一环节类型
     */
    @Schema(description = "下一环节类型")
    private Integer nextPageType;

    /**
     * 数据类型 1 课件1 2 课件2
     */
    @Schema(description = "数据类型")
    private Integer dataType;

    /**
     * 页信息
     */
    @Schema(description = "页信息")
    private Object pages;

}
