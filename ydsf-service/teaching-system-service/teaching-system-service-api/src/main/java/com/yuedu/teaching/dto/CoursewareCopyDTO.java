package com.yuedu.teaching.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * 课件副本创建DTO
 *
 * <AUTHOR>
 * @date 2025/08/05
 */
@Data
@Schema(description = "课件副本创建DTO")
public class CoursewareCopyDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 课件ID
     */
    @Schema(description = "课件ID")
    @NotNull(message = "课件ID不能为空")
    private Integer coursewareId;

    /**
     * 课件内容ID
     */
    @Schema(description = "课件内容ID")
    @NotNull(message = "课件内容ID不能为空")
    private Integer coursewareDataId;

}
