package com.yuedu.teaching.dto;

import com.yuedu.teaching.valid.StoreCoursewareDataStepDetailsValidGroup;
import com.yuedu.ydsf.common.core.util.V_A_E;
import com.yuedu.ydsf.common.core.util.V_E;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Positive;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
* 门店教学环节详情表
*
* <AUTHOR>
* @date  2025/08/05
*/
@Data
@Schema(description = "门店教学环节详情表传输对象")
public class StoreCoursewareDataStepDetailsDTO implements Serializable {


    /**
     * 主键id
     */
    @NotNull(groups = {V_E.class, StoreCoursewareDataStepDetailsValidGroup.UpdateStoreDataStepDetails.class}, message = "主键id不能为空")
    @Schema(description = "主键id")
    private Integer id;

    /**
     * 原details表id
     */
    @Schema(description = "原details表id")
    @NotNull(groups = {V_A_E.class }, message = "原details表id不能为空")
    private Integer coursewareDataStepDetailsId;

    /**
     * 课件名称
     */
    @Schema(description = "课件名称")
    @NotBlank(groups = {V_A_E.class }, message = "课件名称不能为空")
    @Length(groups = {V_A_E.class }, max =255 ,message = "课件名称长度不能大于255")
    private String coursewareName;

    /**
     * 关联课件ID
     */
    @Schema(description = "关联课件ID", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(groups = {V_A_E.class, StoreCoursewareDataStepDetailsValidGroup.UpdateStoreDataStepDetails.class}, message = "关联课件ID不能为空")
    @Positive(groups = {StoreCoursewareDataStepDetailsValidGroup.UpdateStoreDataStepDetails.class}, message = "课件ID错误")
    private Integer coursewareId;

    /**
     * 关联课件内容ID
     */
    @Schema(description = "关联课件内容ID", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(groups = {V_A_E.class, StoreCoursewareDataStepDetailsValidGroup.UpdateStoreDataStepDetails.class}, message = "关联课件内容ID不能为空")
    @Positive(groups = {StoreCoursewareDataStepDetailsValidGroup.UpdateStoreDataStepDetails.class}, message = "课件内容ID错误")
    private Integer coursewareDataId;

    /**
     * 环节ID
     */
    @Schema(description = "环节ID", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(groups = {V_A_E.class, StoreCoursewareDataStepDetailsValidGroup.UpdateStoreDataStepDetails.class}, message = "环节ID不能为空")
    @Positive(groups = {StoreCoursewareDataStepDetailsValidGroup.UpdateStoreDataStepDetails.class}, message = "教学页所属环节ID错误")
    private Integer stepId;

    /**
     * 详细数据
     */
    @Schema(description = "详细数据")
    private Object details;

    /**
     * 页面工具
     */
    @Schema(description = "页面工具")
    private Object tool;

    /**
     * 是否提交使用（保存并提交时为true，仅保存时为false或null）
     */
    @Schema(description = "是否提交使用")
    private Boolean submitForUse;

    /**
     * 教学页分类
     */
    @Schema(description = "教学页分类")
    @NotNull(groups = {V_A_E.class }, message = "教学页分类不能为空")
    private Byte teachingPageCategory;

    /**
     * 教学页类型
     */
    @Schema(description = "教学页类型")
    @NotNull(groups = {V_A_E.class }, message = "教学页类型不能为空")
    private Byte teachingPageType;

    /**
     * 教学页模版ID
     */
    @Schema(description = "教学页模版ID")
    @NotNull(groups = {V_A_E.class }, message = "教学页模版ID不能为空")
    private Integer pageTemplateId;

    /**
     * 门店id
     */
    @Schema(description = "门店id")
    @NotNull(groups = {V_A_E.class }, message = "门店id不能为空")
    private Integer storeId;

    /**
     * 校区id
     */
    @Schema(description = "校区id")
    @NotNull(groups = {V_A_E.class }, message = "校区id不能为空")
    private Integer schoolId;

    /**
     * 所属者(副本创建人)
     */
    @Schema(description = "所属者(副本创建人)")
    @NotNull(groups = {V_A_E.class }, message = "所属者(副本创建人)不能为空")
    private Long owner;

    /**
     * 是否删除
     */
    @Schema(description = "是否删除")
    @NotNull(groups = {V_A_E.class }, message = "是否删除不能为空")
    private Byte delFlag;

    /**
     * 创建人
     */
    @Schema(description = "创建人")
    @Length(groups = {V_A_E.class }, max =255 ,message = "创建人长度不能大于255")
    private String createBy;

    /**
     * 创建时间
     */
    @Schema(description = "创建时间")
    @NotNull(groups = {V_A_E.class }, message = "创建时间不能为空")
    private LocalDateTime createTime;

    /**
     * 修改人
     */
    @Schema(description = "修改人")
    @Length(groups = {V_A_E.class }, max =255 ,message = "修改人长度不能大于255")
    private String updateBy;

    /**
     * 修改时间
     */
    @Schema(description = "修改时间")
    @NotNull(groups = {V_A_E.class }, message = "修改时间不能为空")
    private LocalDateTime updateTime;


}
