package com.yuedu.teaching.dto;

import com.yuedu.ydsf.common.core.util.V_A_E;
import com.yuedu.ydsf.common.core.util.V_E;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
* 课程授权门店历史表
*
* <AUTHOR>
* @date  2025/05/21
*/
@Data
@Schema (description = "课程授权门店历史表传输对象")
public class CourseAuthStoreHisDTO implements Serializable {


    /**
     * 主键id
     */
    @NotNull (groups = {V_E.class}, message = "主键id不能为空")
    private Long id;



    /**
     * 课程表id
     */
    @Schema(description = "课程表id")
    private Long courseId;

    /**
     * 门店id
     */
    @Schema(description = "门店id")
    private Long storeId;

    /**
     * 授权状态:0-正常授权;1-取消授权
     */
    @Schema(description = "授权状态:0-正常授权;1-取消授权")
    @NotNull(groups = {V_A_E.class }, message = "授权状态不能为空")
    private Integer authStatus;

    /**
     * 是否删除
     */
    @Schema(description = "是否删除")
    @NotNull(groups = {V_A_E.class }, message = "是否删除不能为空")
    private Byte delFlag;

    /**
     * 创建人
     */
    @Schema(description = "创建人")
    @Length (groups = {V_A_E.class }, max =255 ,message = "创建人长度不能大于255")
    private String createBy;

    /**
     * 创建时间
     */
    @Schema(description = "创建时间")
    @NotNull(groups = {V_A_E.class }, message = "创建时间不能为空")
    private LocalDateTime createTime;

    /**
     * 修改人
     */
    @Schema(description = "修改人")
    @Length(groups = {V_A_E.class }, max =255 ,message = "修改人长度不能大于255")
    private String updateBy;

    /**
     * 修改时间
     */
    @Schema(description = "修改时间")
    @NotNull(groups = {V_A_E.class }, message = "修改时间不能为空")
    private LocalDateTime updateTime;


}
