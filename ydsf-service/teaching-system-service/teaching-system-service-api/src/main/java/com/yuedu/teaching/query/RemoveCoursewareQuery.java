package com.yuedu.teaching.query;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;


/**
 * 课件
 */
@Data
@Schema(description = "课件")
public class RemoveCoursewareQuery {

    /**
     * 课件Id
     */
    @Schema(description = "课件Id", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "课件Id不能为空")
    private Integer coursewareId;

    /**
     * 课件Id
     */
    @Schema(description = "课件版本", requiredMode = Schema.RequiredMode.REQUIRED)
    private Integer coursewareVersion;

    /**
     * 资料ID
     */
    @Schema(description = "资料ID", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "资料ID")
    private Integer dataTemplateId;
}