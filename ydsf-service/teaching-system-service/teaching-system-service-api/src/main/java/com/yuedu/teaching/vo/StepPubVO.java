package com.yuedu.teaching.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;

/**
 * 教学环节存储展示
 *
 * <AUTHOR>
 * @date 2024-11-05 17:32:03
 */
@Data
public class StepPubVO implements Serializable {

    /**
     * id
     */
    @Schema(description = "id")
    private Integer id;

    /**
     * 父ID排序
     */
    @Schema(description = "父ID排序")
    private Integer stepParentOrder;

    /**
     * 页面url
     */
    @Schema(description = "页面url")
    private String viewUrl;

    /**
     * 排序
     */
    @Schema(description = "排序")
    private Integer stepOrder;

    /**
     * 页面模板ID
     */
    @Schema(description = "页面模板ID")
    private Integer pageTemplateId;

    /**
     * 环节详情
     */
    @Schema(description = "环节详情")
    private Object configs;

    /**
     * 页面工具
     */
    @Schema(description = "页面工具")
    private Object tools;
}
