package com.yuedu.teaching.query;

import com.yuedu.teaching.dto.PictureBookRoleDTO;
import com.yuedu.teaching.valid.PictureBookRoleValidGroup;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.Size;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2024/11/19 15:55
 */

@Data
public class PictureBookRoleQuery {

    @Valid
    @NotEmpty(message = "角色列表不能为空", groups = { PictureBookRoleValidGroup.UpdateRoles.class, PictureBookRoleValidGroup.AddRoles.class })
    @Size(min = 1, max = 6, message = "角色列表最少1个，最多6个角色", groups = { PictureBookRoleValidGroup.UpdateRoles.class, PictureBookRoleValidGroup.AddRoles.class })
    List<PictureBookRoleDTO> bookRoleList;
}
