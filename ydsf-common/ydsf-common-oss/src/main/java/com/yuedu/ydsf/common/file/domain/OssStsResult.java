package com.yuedu.ydsf.common.file.domain;

import lombok.Data;

/**
 * TODO
 *
 * @author: KL
 * @date: 2025/03/10
 **/
@Data
public class OssStsResult {

    /**
     * 临时密钥的AccessKeyId
     */
    private String accessKeyId;

    /**
     * 临时密钥的SecretAccessKey
     */
    private String accessKeySecret;

    /**
     * 临时Token
     */
    private String securityToken;

    /**
     * 临时密钥的bucket
     */
    private String bucket;

    /**
     * 临时region
     */
    private String region;

    /**
     * 临时密钥有效期，单位：秒
     */
    private Long durationSeconds;

    /**
     * 上传路径前缀
     */
    private String uploadPathPrefix;
}
