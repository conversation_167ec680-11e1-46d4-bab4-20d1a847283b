<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.yuedu</groupId>
        <artifactId>ydsf-common</artifactId>
        <version>5.6.17-SNAPSHOT</version>
    </parent>

    <artifactId>ydsf-common-sequence</artifactId>
    <packaging>jar</packaging>

    <description>ydsf 分布式发号器</description>

    <dependencies>
        <dependency>
            <groupId>com.yuedu</groupId>
            <artifactId>ydsf-common-core</artifactId>
        </dependency>
        <!-- redis 实现-->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-data-redis</artifactId>
        </dependency>
        <!--anyline-->
        <dependency>
            <groupId>org.anyline</groupId>
            <artifactId>anyline-environment-spring-data-jdbc</artifactId>
        </dependency>
        <dependency>
            <groupId>org.anyline</groupId>
            <artifactId>anyline-data-jdbc-mysql</artifactId>
        </dependency>
        <dependency>
            <groupId>org.anyline</groupId>
            <artifactId>anyline-data-jdbc-postgresql</artifactId>
        </dependency>
        <dependency>
            <groupId>org.anyline</groupId>
            <artifactId>anyline-data-jdbc-oracle</artifactId>
        </dependency>
        <dependency>
            <groupId>org.anyline</groupId>
            <artifactId>anyline-data-jdbc-dm</artifactId>
        </dependency>
    </dependencies>
</project>
