<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>group.springframework</groupId>
        <artifactId>spring-cloud-dependencies-parent</artifactId>
        <version>2024.0.0</version>
        <relativePath />
    </parent>

    <groupId>com.yuedu</groupId>
    <artifactId>ydsf-common-bom</artifactId>
    <packaging>pom</packaging>
    <version>5.6.17-SNAPSHOT</version>
    <description>ydsf 公共版本控制，在此pom定义的依赖，全局业务微服务不需要指定版本。</description>

    <properties>
        <ydsf.version>5.6.17-SNAPSHOT</ydsf.version>
        <mybatis-plus.version>3.5.7</mybatis-plus.version>
        <mybatis-plus-join.version>1.4.13</mybatis-plus-join.version>
        <dynamic-ds.version>4.3.1</dynamic-ds.version>
        <druid.version>1.2.20</druid.version>
        <hutool.version>5.8.31</hutool.version>
        <mysql.connector.version>8.3.0</mysql.connector.version>
        <oracle.version>********</oracle.version>
        <sqlserver.version>8.4.1.jre8</sqlserver.version>
        <dm.version>*********</dm.version>
        <highgo.version>6.2.0</highgo.version>
        <knife4j.version>3.0.5</knife4j.version>
        <springdoc.version>2.1.0</springdoc.version>
        <swagger.core.version>2.2.14</swagger.core.version>
        <mp.weixin.version>4.6.0</mp.weixin.version>
        <ijpay.version>2.9.10-17</ijpay.version>
        <groovy.version>3.0.3</groovy.version>
        <javax.version>4.0.1</javax.version>
        <jsoup.version>1.15.3</jsoup.version>
        <aviator.version>5.4.3</aviator.version>
        <flowable.version>7.0.0</flowable.version>
        <security.oauth.version>2.5.2.RELEASE</security.oauth.version>
        <fastjson.version>1.2.83</fastjson.version>
        <xxl.job.version>2.3.0</xxl.job.version>
        <aliyun.version>3.0.52.ALL</aliyun.version>
        <aws.version>1.12.261</aws.version>
        <javers.version>7.3.3</javers.version>
        <seata.version>1.7.0</seata.version>
        <asm.version>7.1</asm.version>
        <sensitive.word.version>0.17.0</sensitive.word.version>
        <log4j2.version>2.17.1</log4j2.version>
        <docker.plugin.version>0.33.0</docker.plugin.version>
        <cloud.plugin.version>1.0.0</cloud.plugin.version>
        <sentinel.version>1.8.4</sentinel.version>
        <anyline.version>8.7.2-jdk17-20240808</anyline.version>
        <sms4j.version>3.2.1</sms4j.version>
        <jakarta.mail.version>2.0.1</jakarta.mail.version>
        <velocity.version>2.3</velocity.version>
        <common.io.version>2.16.1</common.io.version>
        <velocity.tool.version>3.1</velocity.tool.version>
    </properties>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>com.yuedu</groupId>
                <artifactId>ydsf-common-core</artifactId>
                <version>${ydsf.version}</version>
            </dependency>
            <dependency>
                <groupId>com.yuedu</groupId>
                <artifactId>ydsf-common-audit</artifactId>
                <version>${ydsf.version}</version>
            </dependency>
            <dependency>
                <groupId>com.yuedu</groupId>
                <artifactId>ydsf-common-data</artifactId>
                <version>${ydsf.version}</version>
            </dependency>
            <dependency>
                <groupId>com.yuedu</groupId>
                <artifactId>ydsf-common-gateway</artifactId>
                <version>${ydsf.version}</version>
            </dependency>
            <dependency>
                <groupId>com.yuedu</groupId>
                <artifactId>ydsf-common-gray</artifactId>
                <version>${ydsf.version}</version>
            </dependency>
            <dependency>
                <groupId>com.yuedu</groupId>
                <artifactId>ydsf-common-datasource</artifactId>
                <version>${ydsf.version}</version>
            </dependency>
            <dependency>
                <groupId>com.yuedu</groupId>
                <artifactId>ydsf-common-idempotent</artifactId>
                <version>${ydsf.version}</version>
            </dependency>
            <dependency>
                <groupId>com.yuedu</groupId>
                <artifactId>ydsf-common-job</artifactId>
                <version>${ydsf.version}</version>
            </dependency>
            <dependency>
                <groupId>com.yuedu</groupId>
                <artifactId>ydsf-common-log</artifactId>
                <version>${ydsf.version}</version>
            </dependency>
            <dependency>
                <groupId>com.yuedu</groupId>
                <artifactId>ydsf-common-oss</artifactId>
                <version>${ydsf.version}</version>
            </dependency>
            <dependency>
                <groupId>com.yuedu</groupId>
                <artifactId>ydsf-common-security</artifactId>
                <version>${ydsf.version}</version>
            </dependency>
            <dependency>
                <groupId>com.yuedu</groupId>
                <artifactId>ydsf-common-sensitive</artifactId>
                <version>${ydsf.version}</version>
            </dependency>
            <dependency>
                <groupId>com.yuedu</groupId>
                <artifactId>ydsf-common-sentinel</artifactId>
                <version>${ydsf.version}</version>
            </dependency>
            <dependency>
                <groupId>com.yuedu</groupId>
                <artifactId>ydsf-common-feign</artifactId>
                <version>${ydsf.version}</version>
            </dependency>
            <dependency>
                <groupId>com.yuedu</groupId>
                <artifactId>ydsf-common-sequence</artifactId>
                <version>${ydsf.version}</version>
            </dependency>
            <dependency>
                <groupId>com.yuedu</groupId>
                <artifactId>ydsf-common-swagger</artifactId>
                <version>${ydsf.version}</version>
            </dependency>
            <dependency>
                <groupId>com.yuedu</groupId>
                <artifactId>ydsf-common-seata</artifactId>
                <version>${ydsf.version}</version>
            </dependency>
            <dependency>
                <groupId>com.yuedu</groupId>
                <artifactId>ydsf-common-xss</artifactId>
                <version>${ydsf.version}</version>
            </dependency>
            <dependency>
                <groupId>com.yuedu</groupId>
                <artifactId>ydsf-common-sse</artifactId>
                <version>${ydsf.version}</version>
            </dependency>
            <dependency>
                <groupId>com.yuedu</groupId>
                <artifactId>ydsf-common-websocket</artifactId>
                <version>${ydsf.version}</version>
            </dependency>
            <dependency>
                <groupId>com.yuedu</groupId>
                <artifactId>ydsf-common-encrypt-api</artifactId>
                <version>${ydsf.version}</version>
            </dependency>
            <dependency>
                <groupId>com.yuedu</groupId>
                <artifactId>ydsf-common-excel</artifactId>
                <version>${ydsf.version}</version>
            </dependency>
            <dependency>
                <groupId>com.yuedu</groupId>
                <artifactId>ydsf-upms-api</artifactId>
                <version>${ydsf.version}</version>
            </dependency>

            <dependency>
                <groupId>com.yuedu</groupId>
                <artifactId>ydsf-app-server-api</artifactId>
                <version>${ydsf.version}</version>
            </dependency>

            <dependency>
                <groupId>com.yuedu</groupId>
                <artifactId>ydsf-flow-task-api</artifactId>
                <version>${ydsf.version}</version>
            </dependency>

            <dependency>
                <groupId>com.yuedu</groupId>
                <artifactId>ydsf-flow-engine-api</artifactId>
                <version>${ydsf.version}</version>
            </dependency>

            <dependency>
                <groupId>org.ow2.asm</groupId>
                <artifactId>asm</artifactId>
                <version>${asm.version}</version>
            </dependency>
            <!-- 必备：敏感词-->
            <dependency>
                <groupId>com.github.houbb</groupId>
                <artifactId>sensitive-word</artifactId>
                <version>${sensitive.word.version}</version>
            </dependency>
            <!--mybatis plus extension,包含了mybatis plus core-->
            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>mybatis-plus-extension</artifactId>
                <version>${mybatis-plus.version}</version>
            </dependency>
            <!--mybatis-->
            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>mybatis-plus-spring-boot3-starter</artifactId>
                <version>${mybatis-plus.version}</version>
            </dependency>
            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>dynamic-datasource-spring-boot3-starter</artifactId>
                <version>${dynamic-ds.version}</version>
            </dependency>
            <!-- 连表查询依赖	-->
            <dependency>
                <groupId>com.github.yulichang</groupId>
                <artifactId>mybatis-plus-join-boot-starter</artifactId>
                <version>${mybatis-plus-join.version}</version>
            </dependency>
            <!-- 连表查询依赖	-->
            <dependency>
                <groupId>com.github.yulichang</groupId>
                <artifactId>mybatis-plus-join-annotation</artifactId>
                <version>${mybatis-plus-join.version}</version>
            </dependency>
            <!-- druid 连接池 -->
            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>druid-spring-boot-3-starter</artifactId>
                <version>${druid.version}</version>
            </dependency>
            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>druid</artifactId>
                <version>${druid.version}</version>
            </dependency>
            <!--mysql 驱动-->
            <dependency>
                <groupId>com.mysql</groupId>
                <artifactId>mysql-connector-j</artifactId>
                <version>${mysql.connector.version}</version>
            </dependency>
            <!--oracle 驱动-->
            <dependency>
                <groupId>com.oracle.database.jdbc</groupId>
                <artifactId>ojdbc8</artifactId>
                <version>${oracle.version}</version>
            </dependency>
            <!-- mssql -->
            <dependency>
                <groupId>com.microsoft.sqlserver</groupId>
                <artifactId>mssql-jdbc</artifactId>
                <version>${sqlserver.version}</version>
            </dependency>
            <!--DM8-->
            <dependency>
                <groupId>com.dameng</groupId>
                <artifactId>DmJdbcDriver18</artifactId>
                <version>${dm.version}</version>
            </dependency>
            <dependency>
                <groupId>com.dameng</groupId>
                <artifactId>DmDialect-for-hibernate5.3</artifactId>
                <version>${dm.version}</version>
            </dependency>
            <dependency>
                <groupId>com.highgo</groupId>
                <artifactId>HgdbJdbc</artifactId>
                <version>${highgo.version}</version>
            </dependency>
            <!--anyline-->
            <dependency>
                <groupId>org.anyline</groupId>
                <artifactId>anyline-environment-spring-data-jdbc</artifactId>
                <version>${anyline.version}</version>
            </dependency>
            <dependency>
                <groupId>org.anyline</groupId>
                <artifactId>anyline-data-jdbc-mysql</artifactId>
                <version>${anyline.version}</version>
            </dependency>
            <dependency>
                <groupId>org.anyline</groupId>
                <artifactId>anyline-data-jdbc-oracle</artifactId>
                <version>${anyline.version}</version>
            </dependency>
            <dependency>
                <groupId>org.anyline</groupId>
                <artifactId>anyline-data-jdbc-postgresql</artifactId>
                <version>${anyline.version}</version>
            </dependency>
            <dependency>
                <groupId>org.anyline</groupId>
                <artifactId>anyline-data-jdbc-dm</artifactId>
                <version>${anyline.version}</version>
            </dependency>
            <!--fastjson-->
            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>fastjson</artifactId>
                <version>${fastjson.version}</version>
            </dependency>
            <!-- commons.io -->
            <dependency>
                <artifactId>commons-io</artifactId>
                <groupId>commons-io</groupId>
                <version>${common.io.version}</version>
            </dependency>
            <!--计算引擎-->
            <dependency>
                <groupId>com.googlecode.aviator</groupId>
                <artifactId>aviator</artifactId>
                <version>${aviator.version}</version>
            </dependency>
            <!-- 对象对比工具-->
            <dependency>
                <groupId>org.javers</groupId>
                <artifactId>javers-core</artifactId>
                <version>${javers.version}</version>
            </dependency>
            <!--springdoc -->
            <dependency>
                <groupId>io.springboot</groupId>
                <artifactId>knife4j-openapi3-ui</artifactId>
                <version>${knife4j.version}</version>
            </dependency>

            <!--springdoc -->
            <dependency>
                <groupId>org.springdoc</groupId>
                <artifactId>springdoc-openapi-starter-webflux-ui</artifactId>
                <version>${springdoc.version}</version>
            </dependency>
            <dependency>
                <groupId>org.springdoc</groupId>
                <artifactId>springdoc-openapi-starter-webmvc-ui</artifactId>
                <version>2.3.0</version>
            </dependency>
            <dependency>
                <groupId>org.springdoc</groupId>
                <artifactId>springdoc-openapi-starter-webmvc-api</artifactId>
                <version>${springdoc.version}</version>
            </dependency>
            <dependency>
                <groupId>io.swagger.core.v3</groupId>
                <artifactId>swagger-annotations-jakarta</artifactId>
                <version>${swagger.core.version}</version>
            </dependency>
            <!--微信依赖-->
            <dependency>
                <groupId>com.github.binarywang</groupId>
                <artifactId>weixin-java-mp</artifactId>
                <version>${mp.weixin.version}</version>
            </dependency>
            <dependency>
                <groupId>com.github.binarywang</groupId>
                <artifactId>weixin-java-cp</artifactId>
                <version>${mp.weixin.version}</version>
                <exclusions>
                    <exclusion>
                        <artifactId>commons-io</artifactId>
                        <groupId>commons-io</groupId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.github.binarywang</groupId>
                <artifactId>weixin-java-common</artifactId>
                <version>${mp.weixin.version}</version>
            </dependency>
            <!--支付相关SDK-->
            <dependency>
                <groupId>com.github.javen205</groupId>
                <artifactId>IJPay-WxPay</artifactId>
                <version>${ijpay.version}</version>
            </dependency>
            <dependency>
                <groupId>com.github.javen205</groupId>
                <artifactId>IJPay-AliPay</artifactId>
                <version>${ijpay.version}</version>
            </dependency>
            <!--定义groovy 版本-->
            <dependency>
                <groupId>org.codehaus.groovy</groupId>
                <artifactId>groovy</artifactId>
                <version>3.0.22</version>
            </dependency>
            <dependency>
                <groupId>javax.servlet</groupId>
                <artifactId>javax.servlet-api</artifactId>
                <version>${javax.version}</version>
            </dependency>
            <!--稳定版本，替代spring security bom内置-->
            <dependency>
                <groupId>org.springframework.security.oauth</groupId>
                <artifactId>spring-security-oauth2</artifactId>
                <version>${security.oauth.version}</version>
            </dependency>
            <!--jsoup html 解析组件-->
            <dependency>
                <groupId>org.jsoup</groupId>
                <artifactId>jsoup</artifactId>
                <version>${jsoup.version}</version>
            </dependency>
            <!--工作流依赖-->
            <dependency>
                <groupId>org.flowable</groupId>
                <artifactId>flowable-spring-boot-starter-process</artifactId>
                <version>${flowable.version}</version>
            </dependency>
            <!-- 短信发送工具类 -->
            <dependency>
                <groupId>org.dromara.sms4j</groupId>
                <artifactId>sms4j-spring-boot-starter</artifactId>
                <version>${sms4j.version}</version>
            </dependency>
            <!--  邮件发送工具 -->
            <dependency>
                <groupId>com.sun.mail</groupId>
                <artifactId>jakarta.mail</artifactId>
                <version>${jakarta.mail.version}</version>
            </dependency>
            <!--webhook-->
            <dependency>
                <groupId>org.dromara.sms4j</groupId>
                <artifactId>sms4j-oa-core</artifactId>
                <version>${sms4j.version}</version>
            </dependency>
            <!-- velocity -->
            <dependency>
                <groupId>org.apache.velocity</groupId>
                <artifactId>velocity-engine-core</artifactId>
                <version>${velocity.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.velocity.tools</groupId>
                <artifactId>velocity-tools-generic</artifactId>
                <version>${velocity.tool.version}</version>
            </dependency>
            <!--  指定 log4j 版本-->
            <dependency>
                <groupId>org.apache.logging.log4j</groupId>
                <artifactId>log4j-to-slf4j</artifactId>
                <version>${log4j2.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.logging.log4j</groupId>
                <artifactId>log4j-bom</artifactId>
                <version>${log4j2.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <!--hutool bom-->
            <dependency>
                <groupId>cn.hutool</groupId>
                <artifactId>hutool-bom</artifactId>
                <version>${hutool.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <dependency>
                <groupId>com.alibaba.csp</groupId>
                <artifactId>sentinel-core</artifactId>
                <version>${sentinel.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <dependency>
                <groupId>com.alibaba.csp</groupId>
                <artifactId>sentinel-web-servlet</artifactId>
                <version>${sentinel.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <dependency>
                <groupId>com.alibaba.csp</groupId>
                <artifactId>sentinel-transport-simple-http</artifactId>
                <version>${sentinel.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <dependency>
                <groupId>com.alibaba.csp</groupId>
                <artifactId>sentinel-parameter-flow-control</artifactId>
                <version>${sentinel.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <dependency>
                <groupId>com.alibaba.csp</groupId>
                <artifactId>sentinel-api-gateway-adapter-common</artifactId>
                <version>${sentinel.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <!--  增加云效nexus仓库 -->
    <distributionManagement>
        <repository>
            <id>maven-releases</id>
            <url>https://packages.aliyun.com/66e3a23f5d0a63a08ebdee89/maven/maven-releases</url>
        </repository>
        <snapshotRepository>
            <id>maven-snapshots</id>
            <url>https://packages.aliyun.com/66e3a23f5d0a63a08ebdee89/maven/maven-snapshots</url>
        </snapshotRepository>
    </distributionManagement>

  <scm>
    <tag>ydsf-5.6.4</tag>
  </scm>
</project>
