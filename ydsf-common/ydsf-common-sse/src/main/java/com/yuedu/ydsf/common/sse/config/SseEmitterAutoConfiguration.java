package com.yuedu.ydsf.common.sse.config;

import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;

/**
 * websocket自动配置
 *
 * <AUTHOR>
 */
@EnableConfigurationProperties(SseEmitterProperties.class)
public class SseEmitterAutoConfiguration {

	@Bean
	@ConditionalOnMissingBean
	public SseEmitterEndpoint sseEmitterEndpoint() {
		return new SseEmitterEndpoint();
	}

}
