<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>
	<parent>
		<groupId>com.yuedu</groupId>
		<artifactId>ydsf-common</artifactId>
		<version>5.6.17-SNAPSHOT</version>
	</parent>

	<artifactId>ydsf-common-sse</artifactId>
	<packaging>jar</packaging>

	<description>ydsf sse 服务端推送</description>

	<dependencies>
		<dependency>
			<groupId>org.springframework.data</groupId>
			<artifactId>spring-data-redis</artifactId>
		</dependency>

		<dependency>
			<groupId>cn.hutool</groupId>
			<artifactId>hutool-json</artifactId>
		</dependency>

		<dependency>
			<groupId>com.yuedu</groupId>
			<artifactId>ydsf-common-security</artifactId>
		</dependency>
	</dependencies>
</project>
