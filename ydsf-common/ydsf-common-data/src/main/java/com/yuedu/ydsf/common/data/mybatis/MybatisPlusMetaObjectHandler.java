package com.yuedu.ydsf.common.data.mybatis;

import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.handlers.MetaObjectHandler;
import com.yuedu.ydsf.common.core.constant.CommonConstants;
import com.yuedu.ydsf.common.core.util.UserContextHolder;
import com.yuedu.ydsf.common.security.service.YdsfUser;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.reflection.MetaObject;
import org.springframework.security.authentication.AnonymousAuthenticationToken;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.util.ClassUtils;

import java.nio.charset.Charset;
import java.time.LocalDateTime;
import java.util.Objects;
import java.util.Optional;

/**
 * MybatisPlus 自动填充配置
 *
 * <AUTHOR>
 */
@Slf4j
public class MybatisPlusMetaObjectHandler implements MetaObjectHandler {

	@Override
	public void insertFill(MetaObject metaObject) {
		log.debug("mybatis plus start insert fill ....");
		LocalDateTime now = LocalDateTime.now();

		// 审计字段自动填充,覆盖用户输入
		// 兼容现有表里的字段
		fillValIfNullByName("ctime", now, metaObject, true);
		fillValIfNullByName("mtime", now, metaObject, true);
		fillValIfNullByName("creator", getUserName(), metaObject, true);
		fillValIfNullByName("modifer", getUserName(), metaObject, true);
		// 新建表的统一字段
		fillValIfNullByName("createTime", now, metaObject, true);
		fillValIfNullByName("updateTime", now, metaObject, true);
		fillValIfNullByName("createBy", getUserIdAndUserName(), metaObject, true);
		fillValIfNullByName("updateBy", getUserIdAndUserName(), metaObject, true);

		// 删除标记自动填充
		fillValIfNullByName("delFlag", CommonConstants.STATUS_NORMAL, metaObject, true);
	}

	@Override
	public void updateFill(MetaObject metaObject) {
		log.debug("mybatis plus start update fill ....");
		// 兼容现有表的字段
		fillValIfNullByName("mtime", LocalDateTime.now(), metaObject, true);
		fillValIfNullByName("modifer", getUserName(), metaObject, true);
		// 新建表的统一字段
		fillValIfNullByName("updateTime", LocalDateTime.now(), metaObject, true);
		fillValIfNullByName("updateBy", getUserIdAndUserName(), metaObject, true);
	}

	/**
	 * 填充值，先判断是否有手动设置，优先手动设置的值，例如：job必须手动设置
	 * @param fieldName 属性名
	 * @param fieldVal 属性值
	 * @param metaObject MetaObject
	 * @param isCover 是否覆盖原有值,避免更新操作手动入参
	 */
	private static void fillValIfNullByName(String fieldName, Object fieldVal, MetaObject metaObject, boolean isCover) {
		// 0. 如果填充值为空
		if (fieldVal == null) {
			return;
		}
		// 1. 没有 get 方法
		if (!metaObject.hasSetter(fieldName)) {
			return;
		}
		// 2. 如果用户有手动设置的值
		Object userSetValue = metaObject.getValue(fieldName);
		String setValueStr = StrUtil.str(userSetValue, Charset.defaultCharset());
		if (StrUtil.isNotBlank(setValueStr) && !isCover) {
			return;
		}
		// 3. field 类型相同时设置
		Class<?> getterType = metaObject.getGetterType(fieldName);
		if (ClassUtils.isAssignableValue(getterType, fieldVal)) {
			metaObject.setValue(fieldName, fieldVal);
		}
	}

	/**
	 * 获取 spring security 当前的用户名
	 * @return 当前用户名
	 */
	private String getUserName() {
		Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
		// 匿名接口直接返回
		if (authentication instanceof AnonymousAuthenticationToken) {
			String username = UserContextHolder.getUserName();
			if (CharSequenceUtil.isNotBlank(username)) {
				log.debug("从UserContextHolder线程上下文获取用户名: {}", username);
				return username;
			}
			return null;
		}

		if (Optional.ofNullable(authentication).isPresent()) {
			return authentication.getName();
		}

		return null;
	}

	/**
	 * 获取当前用户id和用户名
	 * @return	用户id和用户名(用户ID:用户名)
	 */
	private String getUserIdAndUserName(){
		YdsfUser ydsfUser = getUserDetails();
		if(Objects.isNull(ydsfUser)){
			Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
			// 匿名接口直接返回
			if (authentication instanceof AnonymousAuthenticationToken) {
				String username = UserContextHolder.getUserName();
				if (CharSequenceUtil.isNotBlank(username)) {
					log.debug("从UserContextHolder线程上下文获取用户名: {}", username);
					return username;
				}
				return null;
			}
			return null;
		}
		return ydsfUser.getUserType()+ ":" + ydsfUser.getId() + ":" + ydsfUser.getName();
	}

	private YdsfUser getUserDetails() {
		Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
		if (Optional.ofNullable(authentication).isPresent()) {
			Object principal = authentication.getPrincipal();
			if (principal instanceof YdsfUser) {
				return (YdsfUser) principal;
			}
		}
		return null;
	}

}
