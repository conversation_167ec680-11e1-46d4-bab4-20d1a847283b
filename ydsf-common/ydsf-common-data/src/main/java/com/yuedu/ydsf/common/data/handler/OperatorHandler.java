package com.yuedu.ydsf.common.data.handler;

import com.yuedu.ydsf.common.core.util.OperatorUtil;
import lombok.SneakyThrows;
import org.apache.ibatis.type.BaseTypeHandler;
import org.apache.ibatis.type.JdbcType;
import org.apache.ibatis.type.MappedJdbcTypes;
import org.apache.ibatis.type.MappedTypes;

import java.sql.CallableStatement;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.List;

@MappedTypes(value = {String.class})
@MappedJdbcTypes(value = JdbcType.VARCHAR)
public class OperatorHandler extends BaseTypeHandler<String> {
    private static final String CREATE_BY = "create_by";
    private static final String UPDATE_BY = "update_by";
    private static final List<String> CREATE_UPDATE_BY = List.of(CREATE_BY, UPDATE_BY);

    @Override
    public void setNonNullParameter(PreparedStatement ps, int i, String parameter, JdbcType jdbcType)
            throws SQLException {
        ps.setString(i, parameter);
    }

    @Override
    @SneakyThrows
    public String getNullableResult(ResultSet rs, String columnName) {
        if (CREATE_UPDATE_BY.contains(columnName)) {
            return OperatorUtil.getUserName(rs.getString(columnName));
        } else {
            return rs.getString(columnName);
        }
    }

    @Override
    @SneakyThrows
    public String getNullableResult(ResultSet rs, int columnIndex) {
        return OperatorUtil.getUserName(rs.getString(columnIndex));
    }

    @Override
    @SneakyThrows
    public String getNullableResult(CallableStatement cs, int columnIndex) {
        return OperatorUtil.getUserName(cs.getString(columnIndex));
    }
}
