package com.yuedu.ydsf.common.data.util;

import com.alibaba.ttl.TransmittableThreadLocal;
import org.apache.commons.lang3.StringUtils;

/**
 * 数据库上下文
 */
public class DBContextHolder {
    private static final String MASTER = "/*FORCE_MASTER*/ ";
    private static final String SLAVE = "/*FORCE_SLAVE*/ ";
    private static final ThreadLocal<String> threadLocal = new TransmittableThreadLocal<>();

    // 添加私有构造函数，防止实例化
    private DBContextHolder() {
        throw new UnsupportedOperationException("这是一个工具类，不能被实例化！");
    }

    /**
     * 是否走主库
     */
    public static void setMaster() {
        threadLocal.set(MASTER);
    }

    /**
     * 是否走从库
     */
    public static void setSlave() {
        threadLocal.set(SLAVE);
    }

    /**
     * 获取Hint标识
     */
    public static String getHintTag() {
        return threadLocal.get();
    }

    public static String getHintTagDesc() {
        if (StringUtils.isEmpty(getHintTag())) {
            return "未设置";
        } else if (getHintTag().equals(MASTER)) {
            return "强制走主库";
        } else if (getHintTag().equals(SLAVE)) {
            return "强制走从库";
        } else {
            return "未知";
        }
    }

    public static void clear() {
        threadLocal.remove();
    }
}
