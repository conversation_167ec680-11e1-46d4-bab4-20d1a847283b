package com.yuedu.ydsf.common.data.util;

import org.slf4j.Logger;
import org.springframework.transaction.support.TransactionSynchronization;
import org.springframework.transaction.support.TransactionSynchronizationManager;

import java.util.concurrent.Executor;

/**
 * 事物工具类
 */
public class TransactionUtils {
    private static final Logger logger = org.slf4j.LoggerFactory.getLogger(TransactionUtils.class);
    /**
     * 在事务提交后同步执行
     * @param runnable 任务
     */
    public static void afterCommitSyncExecute(Runnable runnable){
        if (TransactionSynchronizationManager.isSynchronizationActive()) {
            TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronization() {
                @Override
                public void afterCommit() {
                    logger.info("事务提交后同步执行");
                    runnable.run();
                }
            });
        } else {
            logger.info("非事务环境下执行");
            runnable.run();
        }
    }

    /**
     * 在事务提交后异步执行
     * @param runnable
     */
    public static void afterCommitAsyncExecute(Executor executor, Runnable runnable){
        if (TransactionSynchronizationManager.isSynchronizationActive()) {
            TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronization() {
                @Override
                public void afterCommit() {
                    logger.info("事务提交后异步执行");
                    executor.execute(runnable);
                }
            });
        } else {
            logger.info("非事务环境下异步执行");
            executor.execute(runnable);
        }
    }
}
