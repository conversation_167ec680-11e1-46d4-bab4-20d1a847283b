package com.yuedu.ydsf.common.data.component;


import com.yuedu.ydsf.common.data.annotation.ForceMaster;
import com.yuedu.ydsf.common.data.util.DBContextHolder;
import lombok.SneakyThrows;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.springframework.core.Ordered;
import org.springframework.stereotype.Component;

/**
 * 强制走主库切面
 */
@Aspect
@Component
public class ForceMasterAspect implements Ordered {
    @SneakyThrows
    @Around("@within(forceMaster) || @annotation(forceMaster)")
    public Object around(ProceedingJoinPoint point, ForceMaster forceMaster) throws Throwable {
        DBContextHolder.setMaster();
        Object proceed;
        try {
            proceed = point.proceed();
        } finally {
            DBContextHolder.clear();
        }
        return proceed;
    }

    @Override
    public int getOrder() {
        return Ordered.HIGHEST_PRECEDENCE + 4;
    }
}
