package com.yuedu.ydsf.common.data.util;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.transaction.support.TransactionSynchronization;
import org.springframework.transaction.support.TransactionSynchronizationManager;

import java.util.concurrent.Executor;

import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;

@ExtendWith(MockitoExtension.class)
class TransactionUtilsTest {

    @Mock
    private Executor executor;

    @Mock
    private Runnable runnable;

    @Test
    void afterCommitSyncExecute_withActiveTransaction_executesAfterCommit() {
        TransactionSynchronizationManager.initSynchronization();
        TransactionUtils.afterCommitSyncExecute(runnable);
        TransactionSynchronizationManager.getSynchronizations().forEach(TransactionSynchronization::afterCommit);
        verify(runnable, times(1)).run();
        TransactionSynchronizationManager.clear();
    }

    @Test
    void afterCommitSyncExecute_withoutActiveTransaction_executesImmediately() {
        TransactionUtils.afterCommitSyncExecute(runnable);
        verify(runnable, times(1)).run();
    }

    @Test
    void afterCommitAsyncExecute_withActiveTransaction_executesAfterCommit() {
        TransactionSynchronizationManager.initSynchronization();
        TransactionUtils.afterCommitAsyncExecute(executor, runnable);
        TransactionSynchronizationManager.getSynchronizations().forEach(TransactionSynchronization::afterCommit);
        verify(executor, times(1)).execute(runnable);
        TransactionSynchronizationManager.clear();
    }

    @Test
    void afterCommitAsyncExecute_withoutActiveTransaction_executesImmediately() {
        TransactionUtils.afterCommitAsyncExecute(executor, runnable);
        verify(executor, times(1)).execute(runnable);
    }
}