package com.yuedu.ydsf.admin.api.vo;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR> yuxingkui
 * @Date : 2025/2/7
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AreaVO implements Serializable {

    /**
     * 主键ID
     */
    @Schema(description = "主键ID")
    private Long id;

    /**
     * 父ID
     */
    @Schema(description = "父ID")
    private Long pid;

    /**
     * 地区名称
     */
    @Schema(description = "地区名称")
    private String name;


    /**
     * 高德地区code
     */
    @Schema(description = "地区code")
    private Long adcode;

    /**
     * 0:未生效，1:生效
     */
    @Schema(description = "0:未生效，1:生效")
    private String areaStatus;

    /**
     * 0:国家,1:省,2:城市,3:区县
     */
    @Schema(description = "0:国家,1:省,2:城市,3:区县")
    private String areaType;


}
